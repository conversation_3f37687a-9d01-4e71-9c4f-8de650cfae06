from flask import jsonify
from flask_login import login_required, current_user
from models.user_model import User
from datetime import datetime, timedelta
import requests
from pymongo import UpdateOne, IndexModel, ASCENDING
import logging

# Set up logging
logger = logging.getLogger(__name__)

def init_routes(bp, mongo_client):
    @bp.route('/shopify/customers/api/refresh-customers', methods=['POST'])
    @login_required
    def refresh_customers():
        try:
            # Ensure indexes exist for better performance
            try:
                mongo_client['test']['shCustomers'].create_indexes([
                    IndexModel([('username', ASCENDING)]),
                    IndexModel([('id', ASCENDING), ('username', ASCENDING)], unique=True),
                    IndexModel([('email', ASCENDING)]),
                    IndexModel([('total_spent', ASCENDING)])
                ])
                
                mongo_client['test']['shGiftCards'].create_indexes([
                    IndexModel([('customer_id', ASCENDING)]),
                    IndexModel([('code', ASCENDING)], unique=True)
                ])
                
                logger.info("Created indexes for shCustomers and shGiftCards collections")
            except Exception as e:
                logger.warning(f"Error creating indexes: {str(e)}")
            
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            updated_at_min = (datetime.utcnow() - timedelta(days=7)).isoformat()
            
            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers.json"
            params = {
                'updated_at_min': updated_at_min,
                'limit': 250
            }

            new_customers = 0
            bulk_operations = []
            processed_customer_ids = []

            while url:
                response = requests.get(url, headers=headers, params=params)
                
                if response.status_code != 200:
                    return jsonify({"error": "Failed to fetch customers from Shopify"}), 500

                customers_data = response.json()['customers']

                for customer in customers_data:
                    total_spent = float(customer.get('total_spent', 0)) if customer.get('total_spent') else 0.0
                    orders_count = customer.get('orders_count', 0)
                    customer_id = str(customer['id'])
                    
                    customer_doc = {
                        'id': customer_id,
                        'first_name': customer['first_name'],
                        'last_name': customer['last_name'],
                        'email': customer['email'],
                        'total_spent': total_spent,
                        'orders_count': orders_count,
                        'accepts_marketing': customer.get('accepts_marketing', False),
                        'email_subscribed': customer.get('accepts_marketing', False),
                        'updated_at': customer['updated_at'],
                        'username': current_user.username
                    }

                    bulk_operations.append(
                        UpdateOne(
                            {'id': customer_id, 'username': current_user.username},
                            {'$set': customer_doc},
                            upsert=True
                        )
                    )
                    
                    processed_customer_ids.append(customer_id)

                new_customers += len(customers_data)

                link_header = response.headers.get('Link')
                url = None
                if link_header:
                    links = requests.utils.parse_header_links(link_header)
                    next_link = next((link for link in links if link['rel'] == 'next'), None)
                    if next_link:
                        url = next_link['url']
                        params = {}

            if bulk_operations:
                result = mongo_client['test']['shCustomers'].bulk_write(bulk_operations)
                logger.info(f"Bulk write result: {result.bulk_api_result}")
            
            # After updating customers, update gift card balances for these customers
            if processed_customer_ids:
                update_gift_card_balances(mongo_client, processed_customer_ids, shopify_store_name, shopify_access_token)

            return jsonify({"new_customers": new_customers})
        except Exception as e:
            logger.error(f"Error refreshing customers: {str(e)}", exc_info=True)
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

def update_gift_card_balances(mongo_client, customer_ids, shopify_store_name, shopify_access_token):
    """Update gift card balances for the specified customers using aggregation pipeline"""
    try:
        headers = {
            'X-Shopify-Access-Token': shopify_access_token,
            'Content-Type': 'application/json'
        }
        
        # Get existing gift cards for these customers
        pipeline = [
            {'$match': {'customer_id': {'$in': customer_ids}}},
            {'$group': {'_id': '$customer_id', 'gift_cards': {'$push': '$$ROOT'}}}
        ]
        
        gift_card_results = list(mongo_client['test']['shGiftCards'].aggregate(pipeline))
        gift_card_by_customer = {result['_id']: result['gift_cards'] for result in gift_card_results}
        
        bulk_operations = []
        
        for customer_id, gift_cards in gift_card_by_customer.items():
            for gift_card in gift_cards:
                # Fetch the latest balance from Shopify
                gift_card_code = gift_card.get('code')
                if not gift_card_code:
                    continue
                    
                url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/lookup.json?code={gift_card_code}"
                response = requests.get(url, headers=headers)
                
                if response.status_code == 200:
                    gift_card_data = response.json().get('gift_card')
                    if gift_card_data:
                        bulk_operations.append(
                            UpdateOne(
                                {'code': gift_card_code},
                                {'$set': {
                                    'balance': str(gift_card_data.get('balance', '0.00')),
                                    'last_used_at': gift_card_data.get('last_characters'),
                                    'last_updated': datetime.utcnow().isoformat()
                                }}
                            )
                        )
        
        if bulk_operations:
            mongo_client['test']['shGiftCards'].bulk_write(bulk_operations)
            logger.info(f"Updated {len(bulk_operations)} gift card balances")
    except Exception as e:
        logger.error(f"Error updating gift card balances: {str(e)}", exc_info=True)
