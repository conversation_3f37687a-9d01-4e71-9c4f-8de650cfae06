from flask import render_template, jsonify, request
from flask_login import login_required, current_user
import logging
from pymongo import MongoClient
import os

logger = logging.getLogger(__name__)

# MongoDB connection
def get_mongo_client():
    """Get MongoDB client"""
    mongo_uri = os.environ.get('MONGO_URI', 'mongodb://localhost:27017/')
    return MongoClient(mongo_uri)

def get_user_collection():
    """Get user collection"""
    client = get_mongo_client()
    db = client.test
    return db.user

def get_user_settings(username):
    """Get user document from MongoDB"""
    try:
        user_collection = get_user_collection()
        user = user_collection.find_one({"username": username})
        return user if user else {}
    except Exception as e:
        logger.error(f"Error getting user settings: {str(e)}")
        return {}

def update_user_settings(username, field, value):
    """Update user settings in MongoDB"""
    try:
        user_collection = get_user_collection()
        result = user_collection.update_one(
            {"username": username},
            {"$set": {field: value}},
            upsert=True
        )
        return result.acknowledged
    except Exception as e:
        logger.error(f"Error updating user settings: {str(e)}")
        return False

def create_main_routes(bp):
    @bp.route('/')
    @login_required
    def index():
        """Render the Shopify autopricing page."""
        logger.info(f"Rendering autopricing page for user {current_user.username if hasattr(current_user, 'username') else 'unknown'}")
        return render_template('shopify_autopricing.html', user_currency=current_user.currency if hasattr(current_user, 'currency') else 'USD')

    @bp.route('/test')
    def test():
        """Test endpoint to check if the blueprint is accessible."""
        return "Shopify autopricing blueprint is working!"

    @bp.route('/test-page')
    def test_page():
        """Test endpoint to render the autopricing page without login."""
        return render_template('shopify_autopricing.html', user_currency='USD')

    @bp.route('/api/status')
    def api_status():
        """Test endpoint to check if the API is working."""
        return jsonify({
            "status": "ok",
            "message": "Shopify autopricing API is working!"
        })

    # Price Preference API Endpoints
    @bp.route('/api/price-preference', methods=['GET'])
    @login_required
    def get_price_preference():
        """Get user's price type preference from MongoDB."""
        try:
            username = current_user.username
            settings = get_user_settings(username)

            # Default price preference if not found
            default_preference = ["lowPrice", "marketPrice", "midPrice", "highPrice"]

            if 'price_preference' in settings:
                return jsonify({"price_preference": settings['price_preference']})
            else:
                return jsonify({"price_preference": default_preference})
        except Exception as e:
            logger.error(f"Error getting price preference: {str(e)}")
            return jsonify({"error": "Failed to get price preference"}), 500

    @bp.route('/api/price-preference', methods=['POST'])
    @login_required
    def save_price_preference():
        """Save user's price type preference to MongoDB."""
        try:
            data = request.json
            username = current_user.username

            if 'price_preference' not in data:
                return jsonify({"error": "Missing price_preference in request"}), 400

            success = update_user_settings(username, 'price_preference', data['price_preference'])

            if success:
                return jsonify({"message": "Price preference saved successfully"})
            else:
                return jsonify({"error": "Failed to save price preference"}), 500
        except Exception as e:
            logger.error(f"Error saving price preference: {str(e)}")
            return jsonify({"error": "Failed to save price preference"}), 500

    # Price Comparison API Endpoints
    @bp.route('/api/price-comparison-settings', methods=['GET'])
    @login_required
    def get_price_comparison():
        """Get user's price comparison settings from MongoDB."""
        try:
            username = current_user.username
            settings = get_user_settings(username)

            # Default settings if not found
            default_settings = {
                "use_highest_price": False,
                "price_comparison_pairs": [["lowPrice", "marketPrice"]],
                "price_modifiers": {}
            }

            if 'price_comparison' in settings:
                return jsonify(settings['price_comparison'])
            else:
                return jsonify(default_settings)
        except Exception as e:
            logger.error(f"Error getting price comparison settings: {str(e)}")
            return jsonify({"error": "Failed to get price comparison settings"}), 500

    @bp.route('/api/price-comparison-settings', methods=['POST'])
    @login_required
    def save_price_comparison():
        """Save user's price comparison settings to MongoDB."""
        try:
            data = request.json
            username = current_user.username

            required_fields = ['use_highest_price', 'price_comparison_pairs']
            for field in required_fields:
                if field not in data:
                    return jsonify({"error": f"Missing {field} in request"}), 400

            success = update_user_settings(username, 'price_comparison', data)

            if success:
                return jsonify({"message": "Price comparison settings saved successfully"})
            else:
                return jsonify({"error": "Failed to save price comparison settings"}), 500
        except Exception as e:
            logger.error(f"Error saving price comparison settings: {str(e)}")
            return jsonify({"error": "Failed to save price comparison settings"}), 500

    # Autopricing Settings API Endpoints
    @bp.route('/api/autopricing-settings', methods=['GET'])
    @login_required
    def get_settings():
        """Get user's autopricing settings from MongoDB."""
        try:
            username = current_user.username
            user = get_user_settings(username)

            # Get existing customStepping and other settings
            custom_stepping = user.get('customStepping', {})

            # Map existing user settings to autopricing format
            settings = {
                "use_skuid_pricing": user.get('use_skuid_pricing', False),
                "price_rounding_enabled": user.get('price_rounding_enabled', False),
                "price_rounding_thresholds": user.get('price_rounding_thresholds', [49, 99]),
                "nm_percent": custom_stepping.get('nm', 100),
                "lp_percent": custom_stepping.get('lp', 80),
                "mp_percent": custom_stepping.get('mp', 70),
                "hp_percent": custom_stepping.get('hp', 60),
                "dm_percent": custom_stepping.get('dm', 50),
                "min_price": user.get('minPrice', 0.5)
            }

            return jsonify(settings)
        except Exception as e:
            logger.error(f"Error getting autopricing settings: {str(e)}")
            return jsonify({"error": "Failed to get autopricing settings"}), 500

    @bp.route('/api/autopricing-settings', methods=['POST'])
    @login_required
    def save_settings():
        """Save user's autopricing settings to MongoDB."""
        try:
            data = request.json
            username = current_user.username

            required_fields = ['use_skuid_pricing', 'price_rounding_enabled']
            for field in required_fields:
                if field not in data:
                    return jsonify({"error": f"Missing {field} in request"}), 400

            # Get current user document
            user_collection = get_user_collection()

            # Prepare update operations
            update_ops = {}

            # Update customStepping if percentage values are provided
            custom_stepping_updates = {}
            if 'nm_percent' in data:
                custom_stepping_updates['nm'] = data['nm_percent']
            if 'lp_percent' in data:
                custom_stepping_updates['lp'] = data['lp_percent']
            if 'mp_percent' in data:
                custom_stepping_updates['mp'] = data['mp_percent']
            if 'hp_percent' in data:
                custom_stepping_updates['hp'] = data['hp_percent']
            if 'dm_percent' in data:
                custom_stepping_updates['dm'] = data['dm_percent']

            # Update customStepping fields individually
            for key, value in custom_stepping_updates.items():
                update_ops[f'customStepping.{key}'] = value

            # Update other settings
            if 'min_price' in data:
                update_ops['minPrice'] = data['min_price']
            if 'use_skuid_pricing' in data:
                update_ops['use_skuid_pricing'] = data['use_skuid_pricing']
            if 'price_rounding_enabled' in data:
                update_ops['price_rounding_enabled'] = data['price_rounding_enabled']
            if 'price_rounding_thresholds' in data:
                update_ops['price_rounding_thresholds'] = data['price_rounding_thresholds']

            # Perform the update
            result = user_collection.update_one(
                {"username": username},
                {"$set": update_ops},
                upsert=True
            )

            if result.acknowledged:
                return jsonify({"message": "Autopricing settings saved successfully"})
            else:
                return jsonify({"error": "Failed to save autopricing settings"}), 500
        except Exception as e:
            logger.error(f"Error saving autopricing settings: {str(e)}")
            return jsonify({"error": "Failed to save autopricing settings"}), 500

    # Game Minimum Prices API Endpoints
    @bp.route('/api/games', methods=['GET'])
    @login_required
    def get_autopricing_games():
        """Get user's game settings from MongoDB."""
        try:
            username = current_user.username
            settings = get_user_settings(username)

            if 'games' in settings and isinstance(settings['games'], dict):
                games = []
                for game_id, game_data in settings['games'].items():
                    if 'name' in game_data:
                        games.append({
                            "id": game_id,
                            "name": game_data['name'],
                            "default_min_price": game_data.get('default_min_price', 0)
                        })
                return jsonify({"games": games})
            else:
                return jsonify({"games": []})
        except Exception as e:
            logger.error(f"Error getting games: {str(e)}")
            return jsonify({"error": "Failed to get games"}), 500

    @bp.route('/api/game/<game_id>', methods=['GET'])
    @login_required
    def get_autopricing_game(game_id):
        """Get specific game settings from MongoDB."""
        try:
            username = current_user.username
            settings = get_user_settings(username)

            if 'games' in settings and game_id in settings['games']:
                return jsonify(settings['games'][game_id])
            else:
                return jsonify({"error": "Game not found"}), 404
        except Exception as e:
            logger.error(f"Error getting game {game_id}: {str(e)}")
            return jsonify({"error": "Failed to get game settings"}), 500

    @bp.route('/api/game', methods=['POST'])
    @login_required
    def save_autopricing_game():
        """Save a game's settings to MongoDB."""
        try:
            data = request.json
            username = current_user.username

            required_fields = ['id', 'name']
            for field in required_fields:
                if field not in data:
                    return jsonify({"error": f"Missing {field} in request"}), 400

            game_id = data['id']

            # Get current games or initialize empty dict
            settings = get_user_settings(username)
            games = settings.get('games', {})

            # Update or add the game
            games[game_id] = data

            success = update_user_settings(username, 'games', games)

            if success:
                return jsonify({"message": "Game settings saved successfully"})
            else:
                return jsonify({"error": "Failed to save game settings"}), 500
        except Exception as e:
            logger.error(f"Error saving game settings: {str(e)}")
            return jsonify({"error": "Failed to save game settings"}), 500

    @bp.route('/api/game/<game_id>', methods=['DELETE'])
    @login_required
    def delete_autopricing_game(game_id):
        """Delete a game's settings from MongoDB."""
        try:
            username = current_user.username

            # Get current games
            settings = get_user_settings(username)
            games = settings.get('games', {})

            # Remove the game if it exists
            if game_id in games:
                del games[game_id]

                success = update_user_settings(username, 'games', games)

                if success:
                    return jsonify({"message": "Game settings deleted successfully"})
                else:
                    return jsonify({"error": "Failed to delete game settings"}), 500
            else:
                return jsonify({"error": "Game not found"}), 404
        except Exception as e:
            logger.error(f"Error deleting game {game_id}: {str(e)}")
            return jsonify({"error": "Failed to delete game settings"}), 500
