import logging
from routes.shopify.products.routes import variants_bp
from routes.shopify.products.routes.variants import (
    get_variants,
    update_variant,
    update_price
)

logger = logging.getLogger(__name__)
logger.info("Registering variants routes")

# Register routes
variants_bp.route('/<shopify_id>')(get_variants)
variants_bp.route('/api/update_variant', methods=['POST'])(update_variant)
variants_bp.route('/api/update_price', methods=['POST'])(update_price)

logger.info("Variants routes registered successfully")
