{% extends "base.html" %}

{% block title %}Shopify Products v1.0{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/summary.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/shopify_products.css') }}">
<style>
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        backdrop-filter: blur(5px);
    }

    .loading-text {
        color: white;
        margin-top: 15px;
        font-size: 16px;
    }

    .repricing-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.95);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 2000;
        backdrop-filter: blur(5px);
    }

    .progress-container {
        width: 100%;
        padding: 15px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 5px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }

    /* Card styling to match dashboard */
    .card {
        background-color: #1e293b;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        border: none;
    }

    .card-header {
        background-color: #1e293b !important;
        padding: 15px 20px;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .card-body {
        padding: 20px;
        background-color: #1e293b;
    }

    /* Button styling to match dashboard */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-sm {
        padding: 5px 10px;
    }

    /* Table styling to match dashboard */
    .table {
        color: white;
        background-color: #1e293b;
    }

    .table thead th {
        background-color: #1a202c;
        color: white;
        border-bottom: 2px solid #4a5568;
        padding: 15px;
        font-weight: 600;
    }

    .table tbody tr {
        background-color: #1e293b;
    }

    .table tbody tr:hover {
        background-color: #3a4a5c;
    }

    .table td, .table th {
        border-color: #4a5568;
        padding: 15px;
        vertical-align: middle;
    }

    /* Form controls styling */
    .form-control, .form-select, select {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
    }

    .form-control:focus, .form-select:focus, select:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
    }

    /* Fix dropdown option colors */
    .form-select option,
    select option,
    .form-control option {
        background-color: #1e293b !important;
        color: white !important;
    }

    /* Fix dropdown arrow color */
    .form-select, select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 16px 12px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    /* Label styling */
    label {
        color: #e2e8f0;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Badge styling */
    .badge {
        padding: 6px 12px;
        font-weight: 500;
        border-radius: 20px;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }

    /* Expandable rows styling */
    .variants-row {
        background-color: #1a202c !important;
        border-bottom: 2px solid #4a5568;
    }

    .variants-container {
        background-color: #1a202c;
        border-radius: 8px;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
    }

    .variant-type-row {
        background-color: #1e293b;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: 10px;
        padding: 10px;
        transition: all 0.2s ease;
    }

    .variant-type-row:hover {
        background-color: #2d3748;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .variant-type-title {
        font-weight: 600;
        margin-bottom: 8px;
        padding-bottom: 5px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .variant-conditions {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .variant-condition {
        flex: 1;
        min-width: 100px;
        padding: 5px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
    }

    .variant-condition-title {
        font-size: 0.8rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: #a0aec0;
    }

    .expand-collapse-btn {
        width: 30px;
        height: 30px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .expand-collapse-btn:hover {
        background-color: #4a5568;
        color: white;
    }

    .expand-collapse-btn[aria-expanded="true"] .fa-plus {
        display: none;
    }

    .expand-collapse-btn[aria-expanded="false"] .fa-minus {
        display: none;
    }

    .quantity-control-group {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }

    .quantity-btn {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        width: 28px;
        height: 28px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .quantity-btn:hover {
        background-color: rgba(255, 255, 255, 0.2);
    }

    .quantity-btn.minus {
        border-radius: 4px 0 0 4px;
        border-right: none;
    }

    .quantity-btn.plus {
        border-radius: 0 4px 4px 0;
        border-left: none;
    }

    .variant-quantity-input {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 0;
        width: 70px;
        height: 28px;
        padding: 2px 5px;
        text-align: center;
        font-size: 0.9rem;
        margin: 0;
    }

    .variant-quantity-input:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
        z-index: 1;
        position: relative;
    }

    /* Hide spinner buttons on number inputs */
    .variant-quantity-input::-webkit-inner-spin-button,
    .variant-quantity-input::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .variant-update-status {
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }

    .variant-save-btn {
        padding: 2px 6px;
        font-size: 0.75rem;
        height: 28px;
    }

    .variant-condition-empty {
        color: #718096;
        font-style: italic;
        font-size: 0.75rem;
    }

    /* Filter styles */
    .variant-filters .btn-group .btn.active {
        background-color: #4a5568;
        color: white;
    }

    .variant-filters .btn-group .btn {
        min-width: 50px;
    }
</style>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Get user's currency from the server
    let userCurrency = 'USD'; // Default to USD

    fetch('/shopify/products/get_user_currency')
        .then(response => response.json())
        .then(data => {
            userCurrency = data.currency;
        })
        .catch(error => console.error('Error fetching user currency:', error));

    function getCurrencySymbol(currency) {
        const symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            // Add more currency symbols as needed
        };
        return symbols[currency] || currency;
    }

    function showLoading() {
        const tbody = document.querySelector('#productTable tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-white">
                    <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    function hideLoading() {
        const loadingRows = document.querySelectorAll('#productTable tbody tr');
        if (loadingRows.length === 1 && loadingRows[0].querySelector('.spinner-border')) {
            loadingRows[0].remove();
        }
    }
</script>
<div class="container-fluid mt-5">
    <!-- Products Section -->
        <div class="tab-pane fade show active" id="products" role="tabpanel" aria-labelledby="products-tab">


    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h4>Filter Products</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 col-lg-2 mb-2">
                            <label for="itemTypeSelect">Item Type:</label>
                            <select id="itemTypeSelect" class="form-control">
                                <option value="tcg" selected>TCG Items</option>
                                <option value="all">All Items</option>
                                <option value="special_price">Special Price (9999 or 0)</option>
                            </select>
                        </div>

                        <div class="col-md-4 col-lg-2 mb-2">
                            <label for="vendorSelect">Vendor:</label>
                            <select id="vendorSelect" class="form-control">
                                <option value="">Select Vendor</option>
                            </select>
                        </div>

                        <div class="col-md-4 col-lg-2 mb-2">
                            <label for="productTypeSelect">Product Type:</label>
                            <select id="productTypeSelect" class="form-control" disabled>
                                <option value="">Select Product Type</option>
                            </select>
                        </div>
<div class="col-md-4 col-lg-2 mb-2">
    <label for="expansionNameSelect">Expansion Name:</label>
    <select id="expansionNameSelect" class="form-control" disabled>
        <option value="">Select Expansion Name</option>
    </select>
</div>
<div class="col-md-4 col-lg-2 mb-2">
    <label for="firstLetterSelect">First Letter:</label>
    <select id="firstLetterSelect" class="form-control">
        <option value="">All</option>
        <option value="A">A</option>
        <option value="B">B</option>
        <option value="C">C</option>
        <option value="D">D</option>
        <option value="E">E</option>
        <option value="F">F</option>
        <option value="G">G</option>
        <option value="H">H</option>
        <option value="I">I</option>
        <option value="J">J</option>
        <option value="K">K</option>
        <option value="L">L</option>
        <option value="M">M</option>
        <option value="N">N</option>
        <option value="O">O</option>
        <option value="P">P</option>
        <option value="Q">Q</option>
        <option value="R">R</option>
        <option value="S">S</option>
        <option value="T">T</option>
        <option value="U">U</option>
        <option value="V">V</option>
        <option value="W">W</option>
        <option value="X">X</option>
        <option value="Y">Y</option>
        <option value="Z">Z</option>
        <option value=".">.</option>
    </select>
</div>
<div class="col-md-4 col-lg-2 mb-2">
    <label for="searchTermInput">Search:</label>
    <input type="text" id="searchTermInput" class="form-control" placeholder="Search products...">
</div>
<div class="col-md-4 col-lg-2 mb-2">
    <label for="searchNumberInput">Search by Number:</label>
    <div class="input-group">
        <input type="text" id="searchNumberInput" class="form-control" placeholder="Exact card number">
        <button class="btn btn-primary" id="searchNumberBtn" type="button">Search</button>
    </div>
</div>
<div class="col-md-4 col-lg-2 mb-2">
    <label for="sortPriceSelect">Sort by:</label>
    <select id="sortPriceSelect" class="form-control">
        <option value="number-asc" selected>Card #: Ascending</option>
        <option value="low-to-high">Price: Lowest to Highest</option>
        <option value="high-to-low">Price: Highest to Lowest</option>
        <option value="number-desc">Card #: Descending</option>
        <option value="">No Sort</option>
    </select>
</div>
                        <div class="col-12 mb-2">
                            <div class="d-flex justify-content-start gap-4">
                            <div class="d-flex gap-4">
                                <div class="form-check form-switch">
                                    <input type="checkbox" id="inStockOnlyToggle" class="form-check-input" role="switch">
                                    <label for="inStockOnlyToggle" class="form-check-label">Show only in-stock items</label>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <select id="quantityFilterType" class="form-select form-select-sm" style="width: auto;">
                                        <option value="">No quantity filter</option>
                                        <option value="above">Above</option>
                                        <option value="below">Below</option>
                                    </select>
                                    <input type="number" id="quantityFilterValue" class="form-control form-control-sm"
                                           style="width: 80px;" min="0" placeholder="Qty" disabled>
                                    <button id="applyQuantityFilter" class="btn btn-primary btn-sm" disabled>Apply</button>
                                    <span class="text-white small">Quantity filter</span>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card mb-3">
                                <div class="card-header bg-dark" id="advancedHeading">
                                    <h5 class="mb-0">
                                        <button id="advancedToggleBtn" class="btn btn-link text-white w-100 text-start d-flex justify-content-between align-items-center"
                                                style="text-decoration: none; background-color: #1e293b; padding: 10px;">
                                            <span>Advanced</span>
                                            <span id="toggleArrow" class="text-white">▼</span>
                                        </button>
                                    </h5>
                                </div>
                                <div id="advancedCollapse" style="display: none;">
                                <div class="card-body d-flex flex-wrap justify-content-center gap-2"
                                         style="background-color: #1e293b; color: #fff;">
                                        <button id="repairImagesButton" class="btn btn-primary m-1">Repair Images</button>
                                        <button id="standardizeSelectedButton" class="btn btn-success m-1">Standardize Selected</button>
                                        <button id="standardizeAllMatchingButton" class="btn btn-warning m-1">Standardize All Matching</button>
                                        <button id="setInventoryToZeroButton" class="btn btn-danger m-1">Set Inventory to 0</button>
                                    </div>
                                </div>
                            </div>

                            <script>
                                // Simple toggle for the advanced section using vanilla JavaScript
                                document.getElementById('advancedToggleBtn').addEventListener('click', function() {
                                    const collapseElement = document.getElementById('advancedCollapse');
                                    const toggleArrow = document.getElementById('toggleArrow');

                                    if (collapseElement.style.display === 'none') {
                                        collapseElement.style.display = 'block';
                                        toggleArrow.textContent = '▲'; // Up arrow when open
                                    } else {
                                        collapseElement.style.display = 'none';
                                        toggleArrow.textContent = '▼'; // Down arrow when closed
                                    }
                                });
                            </script>
                            <div class="d-flex justify-content-center mt-3">
                                <button id="exportCsvButton" class="btn btn-info m-1">Export CSV</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div class="d-flex align-items-center">
                    <div class="form-check me-3">
                        <input type="checkbox" class="form-check-input" id="selectAllProducts">
                        <label class="form-check-label text-white" for="selectAllProducts">Select All</label>
                    </div>
                    <div id="selectedCount" class="text-white me-3"></div>
                    <div id="resultCounter" class="text-white"></div>
                </div>
                <div class="d-flex align-items-center">
                    <div class="variant-filters me-3">
                        <div class="btn-group btn-group-sm">
                            <button id="filterAllConditionsBtn" class="btn btn-outline-primary active">All Conditions</button>
                            <button id="filterNMBtn" class="btn btn-outline-primary">NM</button>
                            <button id="filterLPBtn" class="btn btn-outline-primary">LP</button>
                            <button id="filterMPBtn" class="btn btn-outline-primary">MP</button>
                            <button id="filterHPBtn" class="btn btn-outline-primary">HP</button>
                            <button id="filterDMBtn" class="btn btn-outline-primary">DM</button>
                        </div>
                    </div>
                    <button id="expandAllBtn" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-expand-alt me-1"></i> Expand All
                    </button>
                    <button id="collapseAllBtn" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-compress-alt me-1"></i> Collapse All
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table id="productTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th class="text-white" style="width: 40px;">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                                </div>
                            </th>
                            <th class="text-white">Image</th>
                            <th class="text-white">Title</th>
                            <th class="text-white">Card #</th>
                            <th class="text-white">Price Range</th>
                            <th class="text-white">Total Quantity</th>
                            <th class="text-white">Manual Pricing</th>
                            <th class="text-white">Last Repriced</th>
                            <th class="text-white">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Products will be dynamically inserted here -->
                    </tbody>
                </table>
            </div>
            <nav aria-label="Product pagination">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination items will be dynamically inserted here -->
                </ul>
            </nav>
        </div>
    </div>


    <!-- Modal for bulk editing -->
    <div class="modal fade" id="bulkEditModal" tabindex="-1" role="dialog" aria-labelledby="bulkEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document" style="max-width: 875px;"> <!-- Increased width by 75% -->
            <div class="modal-content text-white" style="background-color: #1e293b; border: none; border-radius: 16px; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkEditModalLabel">Bulk Edit Products</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <button type="button" class="btn btn-primary mb-3" id="applyBulkEditTop">Apply Changes and Push to Shopify</button>
                    </div>
                    <div class="form-group">
                        <label for="editType">Edit Type:</label>
                        <select id="editType" class="form-control">
                            <option value="vendor">Vendor</option>
                            <option value="product_type">Product Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="originalValue">Original Value:</label>
                        <select id="originalValue" class="form-control">
                            <option value="">Select Original Value</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="newValue">New Value:</label>
                        <input type="text" id="newValue" class="form-control" placeholder="Enter new value">
                    </div>
                    <div id="productList" class="mt-3">
                        <!-- Product list will be dynamically inserted here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="applyBulkEditBottom">Apply Changes and Push to Shopify</button>
                </div>
                <div id="bulkEditLoadingOverlay" class="loading-overlay" style="display: none;">
                    <div class="spinner-border text-light" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for product history -->
    <div class="modal fade" id="historyModal" tabindex="-1" role="dialog" aria-labelledby="historyModalLabel" aria-hidden="true" style="z-index: 1060;">
        <div class="modal-dialog modal-lg" role="document" style="max-width: 875px;"> <!-- Increased width by 75% -->
            <div class="modal-content text-white" style="background-color: #1e293b; border: none; border-radius: 16px; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div class="modal-header">
                    <h5 class="modal-title" id="historyModalLabel">Product Change History</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="historyLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Loading history data...</p>
                    </div>
                    <div id="historyContent" style="display: none;">
                        <div id="noHistoryMessage" class="text-center py-5" style="display: none;">
                            <p class="text-muted">No history records found for this product</p>
                        </div>
                        <div id="historyRecords"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for summary -->
    <div class="modal fade" id="summaryModal" tabindex="-1" role="dialog" aria-labelledby="summaryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document" style="max-width: 875px;"> <!-- Increased width by 75% -->
            <div class="modal-content text-white" style="background-color: #1e293b; border: none; border-radius: 16px; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div class="modal-header">
                    <h5 class="modal-title" id="summaryModalLabel">Price Calculation Summary</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <pre id="summaryContent" class="text-white" style="white-space: pre-wrap; font-family: monospace;"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for product details -->
    <div class="modal fade" id="productModal" tabindex="-1" role="dialog" aria-labelledby="productModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document" style="max-width: 875px;"> <!-- Increased width by 75% -->
            <div class="modal-content text-white" style="background-color: #1e293b; border: none; border-radius: 16px; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalLabel">Product Details</h5>
                    <div class="ms-auto">
                        <a id="tcgplayerLink" href="#" target="_blank" class="btn btn-primary me-2" style="display: none;">
                            <i class="fas fa-external-link-alt"></i> View on TCGPlayer
                        </a>
                        <a id="shopifyLink" href="#" target="_blank" class="btn btn-success me-2" style="display: none;">
                            <i class="fas fa-shopping-cart"></i> View on Shopify
                        </a>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <img id="productImage" src="" alt="Product Image" class="img-fluid mb-3">
                            <div id="priceSettings" class="mt-3">
                                <h5>Price Settings:</h5>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="manualOverrideCheck">
                                    <label class="form-check-label" for="manualOverrideCheck">Price Override</label>
                                    <div class="text-muted small">When enabled, this product will be excluded from automatic pricing updates</div>
                                </div>
                                <p>Min Price: <span id="minPrice"></span></p>
                                <p>Currency: <span id="userCurrency"></span></p>
                                <p>Last Repriced: <span id="lastRepriced" class="text-info fw-bold">Never</span> <span class="badge bg-secondary">UK Time</span></p>
                                <div id="steppingInfo">
                                    <p id="steppingTitle">Custom Stepping:</p>
                                    <ul id="customStepping"></ul>
                                </div>
                            </div>
                            <div id="skuIdStatus" class="mt-3 p-3 rounded" style="border: 1px solid rgba(255, 255, 255, 0.2);">
                                <h5 class="mb-2">SKU ID Status:</h5>
                                <div id="allVariantsHaveSkuId" style="display: none;">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2">✓</span>
                                        <span>All variants have SKU IDs</span>
                                    </div>
                                </div>
                                <div id="someVariantsMissingSkuId" style="display: none;">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2">!</span>
                                        <span>Some variants are missing SKU IDs</span>
                                    </div>
                                    <div class="mt-2 small text-warning">
                                        <span id="missingSkuIdCount"></span> out of <span id="totalVariantCount"></span> variants are missing SKU IDs
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h4 id="productTitle" class="mb-3"></h4>
                            <div id="variantGroups"></div>
                            <div id="tcgplayerPrices" class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="text-primary mb-0">TCGPlayer Prices</h5>
                                    <select id="printingTypeSelect" class="form-select form-select-sm" style="width: auto;">
                                        <option value="">Auto-select printing</option>
                                    </select>
                                </div>
                                <div class="table-responsive">
                                    <h6 class="text-muted mb-2">All TCGPlayer prices shown in your local currency (<span id="tcgPricesCurrency"></span>)</h6>
                                    <table class="table table-dark table-hover border-primary">
                                        <thead class="bg-primary text-white">
                                            <tr>
                                                <th class="text-center">Printing</th>
                                                <th class="text-center">Market Price</th>
                                                <th class="text-center">Low Price</th>
                                                <th class="text-center">Mid Price</th>
                                                <th class="text-center">High Price</th>
                                            </tr>
                                        </thead>
                                        <tbody id="tcgplayerPricesList" class="text-center">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="repriceButton">Reprice</button>
                    <button type="button" class="btn btn-info" id="repriceBySKUButton">Reprice by SKU</button>
                    <button type="button" class="btn btn-warning" id="viewHistoryButton">View History</button>
                    <button type="button" class="btn btn-danger" id="deleteButton">Delete</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

            </div>
        </div>
    </div>
</div>


<script>
    let currentPage = 1;
    let currentProductId = null;
    let fetchTimeout;
    let socket;

    // Helper function to safely get product image source
    function getProductImageSrc(product) {
        if (product.images && Array.isArray(product.images) && product.images.length > 0 && product.images[0].src) {
            return product.images[0].src;
        } else if (product.image && product.image.src) {
            return product.image.src;
        } else if (product.image_url) {
            return product.image_url;
        } else {
            return '/static/images/placeholder.jpg';
        }
    }

    // Socket.IO connection removed


    function showToast(title, message, type = 'info') {
        showNotification(`<strong>${title}</strong>: ${message}`, type);
    }

    function debounceFetch(func, delay) {
        return function(...args) {
            clearTimeout(fetchTimeout);
            fetchTimeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    async function fetchVendors() {
        const itemType = document.getElementById('itemTypeSelect').value;
        const vendorSelect = document.getElementById('vendorSelect');

        try {
            const response = await fetch(`/shopify/products/api/vendors?itemType=${encodeURIComponent(itemType)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            console.log("Raw API response:", data);
            console.log("Response type:", typeof data);
            console.log("Is array:", Array.isArray(data));

            // Check if the response is an error object
            if (data && typeof data === 'object' && data.error) {
                throw new Error(data.error);
            }

            vendorSelect.innerHTML = '<option value="">Select Vendor</option>';

            // Handle response format - check if data has vendors property or is direct array
            let vendors;
            if (Array.isArray(data)) {
                vendors = data;
            } else if (data && Array.isArray(data.vendors)) {
                vendors = data.vendors;
            } else {
                console.error("Expected vendors to be an array or object with vendors property, got:", typeof data, data);
                throw new Error("Invalid response format");
            }

            vendors.forEach(vendor => {
                if (vendor && vendor.trim()) {
                    const option = document.createElement('option');
                    option.value = vendor;
                    option.textContent = vendor;
                    vendorSelect.appendChild(option);
                }
            });
            vendorSelect.disabled = false;

            // Automatically select the first vendor option if vendors are available
            if (vendors.length > 0) {
                vendorSelect.selectedIndex = 1; // Select first vendor (index 1, since index 0 is "Select Vendor")
                // Trigger the change event to load product types for the selected vendor
                vendorSelect.dispatchEvent(new Event('change'));
            }
        } catch (error) {
            console.error("Error fetching vendors:", error);
            vendorSelect.innerHTML = '<option value="">Error loading vendors</option>';
            vendorSelect.disabled = true;
        }
    }

    async function fetchProductTypes() {
        const vendor = document.getElementById('vendorSelect').value;
        const productTypeSelect = document.getElementById('productTypeSelect');

        if (!vendor) {
            productTypeSelect.innerHTML = '<option value="">Select Product Type</option>';
            productTypeSelect.disabled = true;
            return;
        }

        try {
            const response = await fetch(`/shopify/products/api/product-types?vendor=${encodeURIComponent(vendor)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const productTypes = await response.json();
            console.log("Product types response:", productTypes, "Type:", typeof productTypes);

            productTypeSelect.innerHTML = '<option value="">Select Product Type</option>';

            // Handle response format - check if data has product_types property or is direct array
            let productTypesArray;
            if (Array.isArray(productTypes)) {
                productTypesArray = productTypes;
            } else if (productTypes && Array.isArray(productTypes.product_types)) {
                productTypesArray = productTypes.product_types;
            } else {
                console.error("Expected productTypes to be an array or object with product_types property, got:", typeof productTypes, productTypes);
                throw new Error("Invalid response format");
            }

            productTypesArray.forEach(type => {
                if (type && type.trim()) {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    productTypeSelect.appendChild(option);
                }
            });
            productTypeSelect.disabled = false;
        } catch (error) {
            console.error("Error fetching product types:", error);
            productTypeSelect.innerHTML = '<option value="">Error loading product types</option>';
            productTypeSelect.disabled = true;
        }
    }

        async function fetchExpansionNames() {
            const vendor = document.getElementById('vendorSelect').value;
            const productType = document.getElementById('productTypeSelect').value;
            const expansionNameSelect = document.getElementById('expansionNameSelect');

            if (!vendor || !productType) {
                expansionNameSelect.innerHTML = '<option value="">Select Expansion Name</option>';
                expansionNameSelect.disabled = true;
                return;
            }

            try {
                const response = await fetch(`/shopify/products/api/expansion-names?vendor=${encodeURIComponent(vendor)}&productType=${encodeURIComponent(productType)}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const expansionNames = await response.json();
                console.log("Expansion names response:", expansionNames, "Type:", typeof expansionNames);

                expansionNameSelect.innerHTML = '<option value="">Select Expansion Name</option>';

                // Ensure expansionNames is an array
                if (Array.isArray(expansionNames)) {
                    expansionNames.forEach(name => {
                        if (name && typeof name === 'string' && name.trim()) {
                            const option = document.createElement('option');
                            option.value = name;
                            option.textContent = name;
                            expansionNameSelect.appendChild(option);
                        }
                    });
                } else {
                    console.error("Expected expansionNames to be an array, got:", typeof expansionNames);
                    throw new Error("Invalid response format");
                }

                expansionNameSelect.disabled = false;
            } catch (error) {
                console.error("Error fetching expansion names:", error);
                expansionNameSelect.innerHTML = '<option value="">Error loading expansion names</option>';
                expansionNameSelect.disabled = true;
            }
        }

    // Natural sort function for card numbers that can handle mixed string/number formats
    function naturalSort(a, b) {
        // If either value is null or undefined, handle that case
        if (!a) return -1;
        if (!b) return 1;

        // Split the strings into chunks of numbers and non-numbers
        const aParts = a.toString().split(/(\d+)/).filter(Boolean);
        const bParts = b.toString().split(/(\d+)/).filter(Boolean);

        // Compare each chunk
        for (let i = 0; i < Math.min(aParts.length, bParts.length); i++) {
            // If both chunks are numeric, compare as numbers
            if (!isNaN(aParts[i]) && !isNaN(bParts[i])) {
                const diff = parseInt(aParts[i]) - parseInt(bParts[i]);
                if (diff !== 0) return diff;
            } else {
                // Otherwise compare as strings
                const diff = aParts[i].localeCompare(bParts[i]);
                if (diff !== 0) return diff;
            }
        }

        // If we get here, one string is a prefix of the other, or they're identical
        return aParts.length - bParts.length;
    }

    // Store all products when doing natural sort
let allProducts = [];
let currentSortOption = '';
let itemsPerPage = 20; // Default, will be updated from API response
let totalItems = 0;

// --- Search by Number ---
let searchNumberValue = "";

// Function to display a specific page of products when using natural sort
// Function to render products to the table
    function renderProducts(products) {
        const tbody = document.querySelector('#productTable tbody');

        if (products.length > 0) {
            // Display the products
            products.forEach(product => {
                const lowestPrice = parseFloat(product.lowest_price);
                const highestPrice = parseFloat(product.highest_price);
                const totalQuantity = product.total_quantity;

                // Create the main product row
                const productRow = document.createElement('tr');
                productRow.classList.add('text-white', 'product-row');
                productRow.setAttribute('data-product-id', product._id);
                productRow.innerHTML = `
                    <td class="text-white text-center" style="width: 40px;">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input row-checkbox" data-product-id="${product._id}">
                        </div>
                    </td>
                    <td class="text-white">
                        <div class="d-flex align-items-center">
                            <button class="btn btn-sm btn-outline-secondary expand-collapse-btn me-2"
                                    data-product-id="${product._id}"
                                    aria-expanded="false">
                                <i class="fas fa-plus"></i>
                            </button>
                            <img src="${getProductImageSrc(product)}"
                                alt="${product.title}"
                                class="img-thumbnail"
                                style="width: 50px; height: 50px; object-fit: cover;"
                                onerror="this.onerror=null; this.src='/static/images/placeholder.jpg';">
                        </div>
                    </td>
                    <td class="text-white">${product.title}</td>
                    <td class="text-white">${product.number || 'N/A'}</td>
                    <td class="text-white price-range">${lowestPrice.toFixed(2)} - ${highestPrice.toFixed(2)}</td>
                    <td class="text-white">${totalQuantity}</td>
                    <td class="text-white text-center">
                        <div class="form-check form-switch d-flex justify-content-center">
                            <input type="checkbox" class="form-check-input manual-override-toggle"
                                data-product-id="${product._id}"
                                ${product.manualOverride ? 'checked' : ''}>
                            <div class="manual-override-status" style="display: none;">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <div class="badge ${product.manualOverride ? 'bg-success' : 'bg-secondary'} mt-1">
                            ${product.manualOverride ? 'manual' : 'Auto'}
                        </div>
                    </td>
                    <td class="text-center">
                        <div class="d-flex align-items-center justify-content-center">
                            <span class="${product.last_repriced ? 'text-info fw-bold' : 'text-warning fw-bold'}"
                                  data-bs-toggle="tooltip"
                                  title="${product.last_repriced ? 'Last repriced on ' + product.last_repriced : 'This product has never been repriced'}">
                                ${product.last_repriced ? '<i class="fas fa-check-circle me-1"></i>' : '<i class="fas fa-exclamation-circle me-1"></i>'}
                                ${product.last_repriced ? product.last_repriced : 'Never'}
                            </span>
                        </div>
                    </td>
                    <td class="text-white">
                        <div class="btn-group">
                            <button class="btn btn-primary btn-sm view-more-btn" data-product-id="${product._id}">View More</button>
                            ${product.summary ? `<button class="btn btn-info btn-sm view-summary-btn" data-product-id="${product._id}">View Summary</button>` : ''}
                            <button class="btn btn-warning btn-sm repair-image-btn" data-product-id="${product._id}">Repair Image</button>
                            <button class="btn btn-success btn-sm standardize-btn" data-product-id="${product._id}">Standardize</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(productRow);

                // Create a container row for variants (initially hidden)
                const variantsRow = document.createElement('tr');
                variantsRow.classList.add('variants-row', 'bg-dark');
                variantsRow.setAttribute('data-product-id', product._id);
                variantsRow.style.display = 'none';
                variantsRow.innerHTML = `
                    <td colspan="9" class="p-0">
                        <div class="variants-container p-3" data-product-id="${product._id}">
                            <div class="text-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading variants...</span>
                                </div>
                                <p class="mt-2">Loading variants...</p>
                            </div>
                        </div>
                    </td>
                `;
                tbody.appendChild(variantsRow);
            });
        }
    }

    // Variables for infinite scrolling
    let isLoading = false;
    let hasMoreProducts = true;

        async function fetchProducts(page = 1, append = false) {
            if (isLoading) return;
            isLoading = true;

            // Show loading indicator
            if (!append) {
                // Clear the table first to ensure old results are removed
                const tbody = document.querySelector('#productTable tbody');
                tbody.innerHTML = '';

                showLoading();
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center text-white">
                            <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
            } else {
                // Add loading indicator at the bottom of the table
                const tbody = document.querySelector('#productTable tbody');
                const loadingRow = document.createElement('tr');
                loadingRow.id = 'loadingMoreRow';
                loadingRow.innerHTML = `
                    <td colspan="10" class="text-center text-white py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading more...</span>
                        </div>
                        <div class="mt-2">Loading more products...</div>
                    </td>
                `;
                tbody.appendChild(loadingRow);
            }

            const itemType = document.getElementById('itemTypeSelect').value;
            const vendor = document.getElementById('vendorSelect').value;
            const productType = document.getElementById('productTypeSelect').value;
            const expansionName = document.getElementById('expansionNameSelect').value;
            const firstLetter = document.getElementById('firstLetterSelect').value;
            const searchTerm = document.getElementById('searchTermInput').value;
            const inStockOnly = document.getElementById('inStockOnlyToggle').checked;
            let sortPrice = document.getElementById('sortPriceSelect').value;
            const quantityFilterType = document.getElementById('quantityFilterType').value;
            const quantityFilterValue = document.getElementById('quantityFilterValue').value;
            const searchNumber = searchNumberValue; // <-- new

            // Debug the parameters being used
            console.log("Filter parameters:", {
                itemType,
                vendor,
                productType,
                expansionName,
                firstLetter,
                searchTerm,
                inStockOnly,
                sortPrice,
                page,
                quantityFilterType,
                quantityFilterValue,
                searchNumber
            });

        // Default to number-asc sorting if no sort option is selected
        if (!sortPrice) {
            sortPrice = 'number-asc';
        }

        // Check if we're doing a card number sort
        const isCardNumberSort = sortPrice === 'number-asc' || sortPrice === 'number-desc';

        // If we're changing sort options, reset our stored data
        if (currentSortOption !== sortPrice && !append) {
            allProducts = [];
            currentSortOption = sortPrice;
            hasMoreProducts = true;
        }

        // If we already have all products for a card number sort, use them
        if (isCardNumberSort && allProducts.length > 0 && !append) {
            // Clear the table first
            const tbody = document.querySelector('#productTable tbody');
            tbody.innerHTML = '';

            // Calculate start and end indices for the requested page
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, allProducts.length);

            // Get the products for this page
            let pageProducts = allProducts.slice(startIndex, endIndex);

            // If searching by number, filter here (client-side)
            if (searchNumber && searchNumber.trim() !== "") {
                pageProducts = allProducts.filter(p => (p.number || "") === searchNumber);
            }

            // Render the products
            renderProducts(pageProducts);

            // Update result counter
            const resultCounter = document.getElementById('resultCounter');
            resultCounter.textContent = `Showing ${pageProducts.length} of ${allProducts.length} results`;

            isLoading = false;
            return;
        }

    const params = new URLSearchParams({
            itemType,
            vendor,
            productType,
            expansionName,
            searchTerm,
            inStockOnly: inStockOnly.toString(), // Ensure boolean is converted to string 'true'/'false'
            page,
            sortPrice,
            per_page: 20 // Set a consistent page size
        });

        // Add first letter param if set - ensure it's correctly used for filtering by title's first letter
        if (firstLetter && firstLetter !== "") {
            params.set('firstLetter', firstLetter);
            console.log(`Adding first letter filter: ${firstLetter}`);
        }
        // Add search by number param if set
        if (searchNumber && searchNumber.trim() !== "") {
            params.set('number', searchNumber);
        }

        // Add quantity filter parameters if they are set
        if (quantityFilterType && quantityFilterValue) {
            params.set('quantityFilterType', quantityFilterType);
            params.set('quantityFilterValue', quantityFilterValue);
            console.log('✅ Adding quantity filter to API request:', {
                quantityFilterType,
                quantityFilterValue
            });
        } else {
            console.log('❌ No quantity filter applied:', {
                quantityFilterType,
                quantityFilterValue
            });
        }

        // For card number sorting, we'll fetch all pages
        if (isCardNumberSort) {
            params.set('per_page', '1000'); // Request a large number to get all products
        }

        try {
            const apiUrl = `/shopify/products/api/products?${params.toString()}`;
            console.log('🌐 API URL:', apiUrl);

            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            console.log("📦 Fetched data:", data);  // Log the fetched data

            // Remove loading indicators
            if (!append) {
                const tbody = document.querySelector('#productTable tbody');
                tbody.innerHTML = '';
            } else {
                const loadingRow = document.getElementById('loadingMoreRow');
                if (loadingRow) loadingRow.remove();
            }

            if (data.products && data.products.length > 0) {
                // Store pagination info
                itemsPerPage = data.per_page;
                totalItems = data.total;
                currentPage = page;

                // Check if we have more products to load
                hasMoreProducts = data.products.length === data.per_page && (page * data.per_page) < data.total;

        // If sorting by card number, store all products and sort them
        if (isCardNumberSort) {
            // Store all products
            allProducts = data.products;

            // Sort all products using natural sort
            allProducts.sort((a, b) => {
                const aNumber = a.number || '';
                const bNumber = b.number || '';
                return sortPrice === 'number-asc'
                    ? naturalSort(aNumber, bNumber)
                    : naturalSort(bNumber, aNumber);
            });

            // Calculate start and end indices for the requested page
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, allProducts.length);

            // Get the products for this page
            let pageProducts = allProducts.slice(startIndex, endIndex);

            // If searching by number, filter here (client-side)
            if (searchNumber && searchNumber.trim() !== "") {
                pageProducts = allProducts.filter(p => (p.number || "") === searchNumber);
            }

            // Render the products
            renderProducts(pageProducts);

            // Update result counter
            const resultCounter = document.getElementById('resultCounter');
            resultCounter.textContent = `Showing ${pageProducts.length} of ${allProducts.length} results`;

            isLoading = false;
            return;
        }

        // For non-card number sorts, render the products
        let filteredProducts = data.products;
        if (searchNumber && searchNumber.trim() !== "") {
            filteredProducts = data.products.filter(p => (p.number || "") === searchNumber);
        }
        renderProducts(filteredProducts);

        // Update result counter
        const resultCounter = document.getElementById('resultCounter');
        const currentCount = append ?
            document.querySelectorAll('#productTable tbody tr').length :
            filteredProducts.length;
        resultCounter.textContent = `Showing ${currentCount} of ${data.total} results`;

        // Hide pagination since we're using infinite scroll
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

    } else if (!append) {
        const tbody = document.querySelector('#productTable tbody');
        const noDataRow = document.createElement('tr');
        noDataRow.innerHTML = '<td colspan="10" class="text-center text-white">No products found</td>';
        tbody.appendChild(noDataRow);

        // Update result counter
        const resultCounter = document.getElementById('resultCounter');
        resultCounter.textContent = 'No results found';

        // No more products to load
        hasMoreProducts = false;
    }

        } catch (error) {
            console.error("Error fetching products:", error);
            console.log("Failed to fetch products:", error);

            // Remove loading indicator on error
            const loadingRow = document.getElementById('loadingMoreRow');
            if (loadingRow) loadingRow.remove();

            // Show error message if this is the first load
            if (!append) {
                const tbody = document.querySelector('#productTable tbody');
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center text-white">
                            <div class="alert alert-danger">
                                Error loading products. Please try again.
                            </div>
                        </td>
                    </tr>
                `;
            }
        } finally {
            isLoading = false;
            hideLoading();
        }
    }

    async function openProductModal(productId) {
        showLoading();
        try {
            console.log("Opening product modal for productId:", productId);
            const [productResponse, settingsResponse] = await Promise.all([
                fetch(`/shopify/products/api/product/${productId}`),
                fetch(`/shopify/products/api/price-settings?productId=${productId}`)
            ]);

            if (!productResponse.ok || !settingsResponse.ok) {
                throw new Error(`HTTP error! status: ${productResponse.status} ${settingsResponse.status}`);
            }

            const [product, priceSettings] = await Promise.all([
                productResponse.json(),
                settingsResponse.json()
            ]);

            // Set manual override checkbox state and add event listener
            const manualOverrideCheck = document.getElementById('manualOverrideCheck');
            manualOverrideCheck.checked = product.manualOverride || false;

            // Remove any existing event listener by cloning and replacing
            const oldManualOverrideCheck = manualOverrideCheck.cloneNode(true);
            manualOverrideCheck.parentNode.replaceChild(oldManualOverrideCheck, manualOverrideCheck);

            // Add new event listener
            oldManualOverrideCheck.addEventListener('change', async function() {
                const originalState = this.checked;
                try {
                    const response = await fetch('/shopify/products/api/update_manual_override', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            productId: currentProductId,
                            manualOverride: this.checked
                        })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        showNotification(
                            "Update Successful",
                            `Price Override ${this.checked ? 'enabled' : 'disabled'}. This product will ${this.checked ? 'now be' : 'no longer be'} excluded from automatic pricing updates.`,
                            "success"
                        );
                    } else {
                        throw new Error(data.error || 'Failed to update Price Override');
                    }
                } catch (error) {
                    console.error("Error updating manual override:", error);
                    showNotification(
                        "Error",
                        `Failed to update Price Override: ${error.message}`,
                        "error"
                    );
                    // Revert checkbox state on error
                    this.checked = !this.checked;
                }
            });

            // Update TCGPlayer link
            const tcgplayerLink = document.getElementById('tcgplayerLink');
            if (product.tcgplayer_url) {
                tcgplayerLink.href = product.tcgplayer_url;
                tcgplayerLink.style.display = 'inline-block';
            } else {
                tcgplayerLink.style.display = 'none';
            }

            // Update Shopify link
            const shopifyLink = document.getElementById('shopifyLink');
            if (product.handle) {
                shopifyLink.href = `https://${product.shopify_store}/products/${product.handle}`;
                shopifyLink.style.display = 'inline-block';
            } else if (product.id) {
                shopifyLink.href = `https://${product.shopify_store}/admin/products/${product.id}`;
                shopifyLink.style.display = 'inline-block';
            } else {
                shopifyLink.style.display = 'none';
            }

            // Check if all variants have skuId
            const allVariantsHaveSkuId = document.getElementById('allVariantsHaveSkuId');
            const someVariantsMissingSkuId = document.getElementById('someVariantsMissingSkuId');
            const missingSkuIdCount = document.getElementById('missingSkuIdCount');
            const totalVariantCount = document.getElementById('totalVariantCount');

            if (product.variants && product.variants.length > 0) {
                const totalVariants = product.variants.length;
                const variantsWithSkuId = product.variants.filter(variant => variant.skuId !== undefined).length;
                const missingCount = totalVariants - variantsWithSkuId;

                totalVariantCount.textContent = totalVariants;
                missingSkuIdCount.textContent = missingCount;

                if (missingCount === 0) {
                    allVariantsHaveSkuId.style.display = 'block';
                    someVariantsMissingSkuId.style.display = 'none';
                } else {
                    allVariantsHaveSkuId.style.display = 'none';
                    someVariantsMissingSkuId.style.display = 'block';
                }
            } else {
                allVariantsHaveSkuId.style.display = 'none';
                someVariantsMissingSkuId.style.display = 'none';
            }

            currentProductId = productId;
            console.log("Set currentProductId to:", currentProductId);
            const productImage = document.getElementById('productImage');
            const productTitle = document.getElementById('productTitle');
            const variantGroups = document.getElementById('variantGroups');

            productImage.src = getProductImageSrc(product);
            productTitle.textContent = product.title;
            variantGroups.innerHTML = '';

            // Update price settings display
            document.getElementById('minPrice').textContent = `${getCurrencySymbol(priceSettings.currency)}${priceSettings.minPrice.toFixed(2)}`;
            document.getElementById('userCurrency').textContent = priceSettings.currency;

            const steppingTitle = document.getElementById('steppingTitle');
            if (priceSettings.isAdvancedPricing) {
                steppingTitle.textContent = `Advanced Pricing Rules for ${priceSettings.expansionName}:`;
            } else {
                steppingTitle.textContent = 'Custom Stepping:';
            }

            // Define the desired order for conditions
            const conditionOrder = ['NM', 'LP', 'HP', 'MP', 'DM'];

            // Sort the stepping rules according to the desired order
            const customSteppingList = document.getElementById('customStepping');
            customSteppingList.innerHTML = '';

            // Create an array of entries and sort them according to the condition order
            const steppingEntries = Object.entries(priceSettings.customStepping);
            steppingEntries.sort((a, b) => {
                const aIndex = conditionOrder.indexOf(a[0].toUpperCase());
                const bIndex = conditionOrder.indexOf(b[0].toUpperCase());
                return aIndex - bIndex;
            });

            // Display the sorted stepping rules
            steppingEntries.forEach(([condition, percentage]) => {
                const li = document.createElement('li');
                li.textContent = `${condition.toUpperCase()}: ${percentage}%`;
                customSteppingList.appendChild(li);
            });

            // Rest of the code remains unchanged
            // Display TCGPlayer prices
            const tcgplayerPricesList = document.getElementById('tcgplayerPricesList');
            tcgplayerPricesList.innerHTML = '';

            if (product.tcgplayer_prices && product.tcgplayer_prices.length > 0) {
                // Filter out invalid prices and bulk prices
                const validPrices = product.tcgplayer_prices.filter(price => {
                    // Check if at least one price value is valid
                    const hasValidPrice = price.marketPrice || price.lowPrice || price.midPrice || price.highPrice;
                    // Exclude bulk prices
                    const notBulk = !price.subTypeName?.toLowerCase().includes('bulk');
                    return hasValidPrice && notBulk;
                });

                if (validPrices.length > 0) {
                    // Populate printing type selector
                    const printingTypeSelect = document.getElementById('printingTypeSelect');
                    printingTypeSelect.innerHTML = '<option value="">Auto-select printing</option>';
                    validPrices.forEach(price => {
                        const option = document.createElement('option');
                        option.value = price.subTypeName;
                        option.textContent = price.subTypeName;
                        printingTypeSelect.appendChild(option);
                    });

                    tcgplayerPricesList.innerHTML = validPrices.map(price => `
                        <tr>
                            <td>${price.subTypeName || 'Normal'}</td>
                            <td>${price.marketPrice ? `$${price.marketPrice}` : '-'}</td>
                            <td>${price.lowPrice ? `$${price.lowPrice}` : '-'}</td>
                            <td>${price.midPrice ? `$${price.midPrice}` : '-'}</td>
                            <td>${price.highPrice ? `$${price.highPrice}` : '-'}</td>
                        </tr>
                    `).join('');
                } else {
                    tcgplayerPricesList.innerHTML = '<tr><td colspan="5">No valid TCGPlayer prices available</td></tr>';
                }
            } else {
                tcgplayerPricesList.innerHTML = '<tr><td colspan="5">No TCGPlayer prices available</td></tr>';
            }

            // Group variants by print type (Normal, Foil, etc)
            const printTypeGroups = {};
            product.variants.forEach(variant => {
                // Extract print type from variant title (e.g. "Normal - English" -> "Normal")
                const printType = variant.title.split(' - ')[0];
                if (!printTypeGroups[printType]) {
                    printTypeGroups[printType] = [];
                }
                printTypeGroups[printType].push(variant);
            });

            // Create a table for each print type
            Object.entries(printTypeGroups).forEach(([printType, variants]) => {
                const groupDiv = document.createElement('div');
                groupDiv.className = 'variant-group mb-4';
                groupDiv.innerHTML = `<h5 class="mb-3">${printType}</h5>`;

                const table = document.createElement('table');
                table.className = 'table table-dark table-hover table-bordered';
                table.innerHTML = `
                <thead>
                        <tr>
                            <th class="text-center">Variant</th>
                            <th class="text-center">Quantity</th>
                            <th class="text-center">Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${variants.map(variant => {
                            return `
                            <tr class="variant-row">
                                <td class="text-center">
                                    <span class="badge bg-secondary">${variant.title}</span>
                                </td>
                                <td class="text-center">
                                    <input type="number"
                                        class="form-control form-control-sm quantity-input"
                                        value="${variant.inventory_quantity}"
                                        min="0"
                                        style="width: 80px;"
                                        data-variant-id="${variant.id}"
                                        data-original-quantity="${variant.inventory_quantity}"
                                    >
                                    <button class="btn btn-sm btn-primary save-quantity-btn" style="display: none;">
                                        Save
                                    </button>
                                </td>
                                <td class="text-center">
                                    <input type="number"
                                        class="form-control form-control-sm price-input"
                                        value="${parseFloat(variant.price).toFixed(2)}"
                                        step="0.01"
                                        min="0"
                                        style="width: 100px;"
                                        data-variant-id="${variant.id}"
                                        data-original-price="${parseFloat(variant.price).toFixed(2)}"
                                    >
                                    <button class="btn btn-sm btn-primary save-price-btn" style="display: none;">
                                        Save
                                    </button>
                                </td>
                            </tr>
                            `;
                        }).join('')}
                    </tbody>
                `;

                groupDiv.appendChild(table);
                variantGroups.appendChild(groupDiv);
            });

            // Update currency display from the already fetched priceSettings
            userCurrency = priceSettings.currency;
            document.getElementById('minPrice').textContent = `${getCurrencySymbol(userCurrency)}${priceSettings.minPrice.toFixed(2)}`;
            document.getElementById('userCurrency').textContent = userCurrency;
            document.getElementById('tcgPricesCurrency').textContent = userCurrency;

            // Update last repriced date
            const lastRepricedSpan = document.getElementById('lastRepriced');
            if (product.last_repriced) {
                lastRepricedSpan.textContent = product.last_repriced;
                lastRepricedSpan.classList.remove('text-warning');
                lastRepricedSpan.classList.add('text-info');
            } else {
                lastRepricedSpan.textContent = 'Never';
                lastRepricedSpan.classList.remove('text-info');
                lastRepricedSpan.classList.add('text-warning');
            }

            // Add direct event listener to the View History button
            const viewHistoryButton = document.getElementById('viewHistoryButton');
            viewHistoryButton.onclick = function() {
                console.log("View History button clicked for productId:", currentProductId);
                showProductHistory(currentProductId);
            };

            const productModal = document.getElementById('productModal');
            const bsModal = new bootstrap.Modal(productModal, {
                backdrop: false
            });
            bsModal.show();
        } catch (error) {
            console.error("Error opening product modal:", error);
            showNotification("Error", "Failed to load product details. Please try again or refresh the page.", "error");
        } finally {
            hideLoading();
        }
    }

    function groupVariants(variants) {
        const groups = {};
        variants.forEach(variant => {
            const groupName = variant.title.split(' - ')[0];
            if (!groups[groupName]) {
                groups[groupName] = [];
            }
            groups[groupName].push(variant);
        });
        return groups;
    }


        function updateProductRow(productId) {
            fetch(`/shopify/products/api/product/${productId}`)
                .then(response => response.json())
                .then(product => {
                    const row = document.querySelector(`tr[data-product-id="${productId}"]`);
                    if (row) {
                        const priceRange = row.querySelector('.price-range');
                        const lowestPrice = Math.min(...product.variants.map(v => parseFloat(v.price)));
                        const highestPrice = Math.max(...product.variants.map(v => parseFloat(v.price)));
                        priceRange.textContent = `$${lowestPrice.toFixed(2)} - $${highestPrice.toFixed(2)}`;
                    }
                })
                .catch(error => console.error("Error updating product row:", error));
        }

        document.getElementById('productModal').addEventListener('hidden.bs.modal', function () {
            if (currentProductId) {
                updateProductRow(currentProductId);
            }
        });

        // Remove event listener for bulkEditModal as it's no longer needed

        // Remove ensureLoadingHidden function as it's no longer needed

        // Remove event listeners for all modals


        // Initialize bulkEditModal when needed
        const bulkEditModal = new bootstrap.Modal(document.getElementById('bulkEditModal'));

        document.querySelector('#bulkEditModal .btn-close').addEventListener('click', function() {
            bulkEditModal.hide();
        });

        // Add event listener for the "Close" button in the modal footer
        document.querySelector('#bulkEditModal .modal-footer .btn-secondary').addEventListener('click', function() {
            bulkEditModal.hide();
        });

        // Ensure the modal is hidden when the backdrop is clicked
        document.getElementById('bulkEditModal').addEventListener('click', function(event) {
            if (event.target === this) {
                bulkEditModal.hide();
            }
        });

        document.getElementById('editType').addEventListener('change', async function() {
            try {
                const editType = this.value;
                const originalValueSelect = document.getElementById('originalValue');
                originalValueSelect.innerHTML = '<option value="">Select Original Value</option>';

                let values;
                if (editType === 'product_type') {
                    const response = await fetch('/shopify/products/api/product-types');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();
                    values = data.product_types;
                } else if (editType === 'vendor') {
                    const response = await fetch('/shopify/products/api/vendors');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();
                    values = data.vendors;
                }

                values.forEach(value => {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = value;
                    originalValueSelect.appendChild(option);
                });
            } catch (error) {
                console.error("Error fetching values:", error);
                showToast("Error", "Failed to load values. Please try again.", "danger");
            }
        });

        document.getElementById('originalValue').addEventListener('change', async function() {
            const editType = document.getElementById('editType').value;
            const originalValue = this.value;
            if (!originalValue) return;

            try {
                let endpoint;
                if (editType === 'product_type') {
                    endpoint = `/shopify/products/api/products-by-type?productType=${encodeURIComponent(originalValue)}`;
                } else if (editType === 'vendor') {
                    endpoint = `/shopify/products/api/products-by-vendor?vendor=${encodeURIComponent(originalValue)}`;
                } else {
                    throw new Error('Invalid edit type');
                }

                const response = await fetch(endpoint);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const products = await response.json();
                const productList = document.getElementById('productList');
                productList.innerHTML = `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll">Select All</label>
                    </div>
                    <div class="product-checkboxes">
                        ${products.map(product => `
                            <div class="form-check">
                                <input class="form-check-input product-checkbox" type="checkbox" id="product-${product._id}" value="${product._id}">
                                <label class="form-check-label" for="product-${product._id}">${product.title}</label>
                            </div>
                        `).join('')}
                    </div>
                `;

                // Add event listener for "Select All" checkbox
                document.getElementById('selectAll').addEventListener('change', function() {
                    const checkboxes = document.querySelectorAll('.product-checkbox');
                    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
                });
            } catch (error) {
                console.error("Error fetching products:", error);
                showToast("Error", "Failed to load products. Please try again.", "danger");
            }
        });

        async function applyBulkEdit() {
            const editType = document.getElementById('editType')?.value;
            const originalValue = document.getElementById('originalValue')?.value;
            const newValue = document.getElementById('newValue')?.value;
            if (!editType || !originalValue || !newValue) {
                showNotification("Error", "Please select an edit type, original value, and enter a new value.", "error");
                return;
            }

            const selectAllChecked = document.getElementById('selectAll')?.checked || false;
            let selectedProductIds = [];

            if (selectAllChecked) {
                selectedProductIds = Array.from(document.querySelectorAll('.product-checkbox')).map(checkbox => checkbox.value);
            } else {
                selectedProductIds = Array.from(document.querySelectorAll('.product-checkbox:checked')).map(checkbox => checkbox.value);
            }

            if (selectedProductIds.length === 0) {
                showNotification("Error", "Please select at least one product to edit.", "error");
                return;
            }

            try {
                // Show loading overlay
                const bulkEditLoadingOverlay = document.getElementById('bulkEditLoadingOverlay');
                if (bulkEditLoadingOverlay) {
                    bulkEditLoadingOverlay.style.display = 'flex';
                }

                // Create progress container
                const progressContainer = document.createElement('div');
                progressContainer.className = 'progress-container';
                progressContainer.innerHTML = `
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%;"
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="text-center text-white mb-3">
                        Processing: <span id="processedCount">0</span> of <span id="totalCount">${selectedProductIds.length}</span> products
                    </div>
                    <div id="statusMessages" class="text-white" style="max-height: 200px; overflow-y: auto;"></div>
                `;

                // Add progress container to the modal body
                const modalBody = document.querySelector('#bulkEditModal .modal-body');
                if (!modalBody) {
                    throw new Error("Modal body element not found");
                }
                modalBody.appendChild(progressContainer);

                const statusMessages = document.getElementById('statusMessages');
                const progressBar = document.querySelector('.progress-bar');
                const processedCountElement = document.getElementById('processedCount');

                if (!statusMessages || !progressBar || !processedCountElement) {
                    throw new Error("Required elements not found");
                }

                // Process products one by one
                let processedCount = 0;

                for (let i = 0; i < selectedProductIds.length; i++) {
                    const productId = selectedProductIds[i];

                    try {
                        // Update product in local database
                        const response = await fetch('/shopify/products/api/bulk-edit', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                editType,
                                originalValue,
                                newValue,
                                selectedProductIds: [productId]
                            })
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        // Push to Shopify
                        const pushResponse = await fetch('/shopify/products/api/push_bulk_edit_to_shopify', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                editType,
                                newValue,
                                selectedProductIds: [productId]
                            })
                        });

                        if (!pushResponse.ok) {
                            throw new Error(`HTTP error! status: ${pushResponse.status}`);
                        }

                        // Get product details to show in status message
                        const productResponse = await fetch(`/shopify/products/api/product/${productId}`);
                        const productData = await productResponse.json();
                        const productTitle = productData.title || 'Product';

                        // Update status message
                        if (statusMessages) {
                            const messageDiv = document.createElement('div');
                            messageDiv.className = 'text-success';
                            messageDiv.textContent = `✓ Updated: ${productTitle}`;
                            statusMessages.appendChild(messageDiv);
                            statusMessages.scrollTop = statusMessages.scrollHeight;
                        }

                        // Update progress
                        processedCount++;
                        if (progressBar && processedCountElement) {
                            const progress = processedCount / selectedProductIds.length;
                            const progressPercent = Math.round(progress * 100);
                            progressBar.style.width = `${progressPercent}%`;
                            progressBar.setAttribute('aria-valuenow', progressPercent);
                            progressBar.textContent = `${progressPercent}%`;
                            processedCountElement.textContent = processedCount;
                        }

                    } catch (error) {
                        console.error(`Error processing product ${productId}:`, error);

                        // Update status message with error
                        if (statusMessages) {
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'text-danger';
                            errorDiv.textContent = `✗ Failed: Product ID ${productId} - ${error.message}`;
                            statusMessages.appendChild(errorDiv);
                            statusMessages.scrollTop = statusMessages.scrollHeight;
                        }

                        // Still update progress
                        processedCount++;
                        if (progressBar && processedCountElement) {
                            const progress = processedCount / selectedProductIds.length;
                            const progressPercent = Math.round(progress * 100);
                            progressBar.style.width = `${progressPercent}%`;
                            progressBar.setAttribute('aria-valuenow', progressPercent);
                            progressBar.textContent = `${progressPercent}%`;
                            processedCountElement.textContent = processedCount;
                        }
                    }

                    // Rate limit: process 2 products per second
                    if (i < selectedProductIds.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 500)); // 500ms = 2 per second
                    }
                }

                // All done
                setTimeout(() => {
                    if (bulkEditLoadingOverlay) {
                        bulkEditLoadingOverlay.style.display = 'none';
                    }

                    showNotification(
                        "Bulk Edit Complete",
                        `Successfully processed ${processedCount} products.`,
                        "success"
                    );

                    // Close modal and refresh products
                    const bulkEditModal = bootstrap.Modal.getInstance(document.getElementById('bulkEditModal'));
                    if (bulkEditModal) {
                        bulkEditModal.hide();
                    }
                    fetchProducts(currentPage);
                }, 1000);

            } catch (error) {
                console.error("Error applying bulk edit:", error);
                showNotification("Error", `Failed to apply bulk edit: ${error.message}. Please try again.`, "error");

                // Hide loading overlay
                const bulkEditLoadingOverlay = document.getElementById('bulkEditLoadingOverlay');
                if (bulkEditLoadingOverlay) {
                    bulkEditLoadingOverlay.style.display = 'none';
                }

                // Remove progress container
                const progressContainer = document.querySelector('.progress-container');
                if (progressContainer) {
                    progressContainer.remove();
                }
            }
        }

        function toggleBulkEditButtons(disabled) {
            document.getElementById('applyBulkEditTop').disabled = disabled;
            document.getElementById('applyBulkEditBottom').disabled = disabled;
        }

        async function handleBulkEdit() {
            toggleBulkEditButtons(true);
            await applyBulkEdit();
            toggleBulkEditButtons(false);
        }

        document.getElementById('applyBulkEditTop').addEventListener('click', handleBulkEdit);
        document.getElementById('applyBulkEditBottom').addEventListener('click', handleBulkEdit);

        window.changePage = function(page) {
            currentPage = page;
            fetchProducts(page);
        }

        async function exportToCsv() {
            const itemType = document.getElementById('itemTypeSelect').value;
            const productType = document.getElementById('productTypeSelect').value;
            const expansionName = document.getElementById('expansionNameSelect').value;
            const searchTerm = document.getElementById('searchTermInput').value;
            const inStockOnly = document.getElementById('inStockOnlyToggle').checked;
            const sortPrice = document.getElementById('sortPriceSelect').value;

            try {
                const response = await fetch(`/shopify/products/api/export-csv?itemType=${itemType}&productType=${productType}&expansionName=${expansionName}&searchTerm=${searchTerm}&inStockOnly=${inStockOnly}&sortPrice=${sortPrice}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'shopify_products.csv';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error("Error exporting CSV:", error);
                console.log("Failed to export CSV. Please try again.");
            }
        }

        document.getElementById('repairImagesButton').addEventListener('click', function() {
            const selectedIds = Array.from(document.querySelectorAll('.row-checkbox:checked')).map(checkbox => checkbox.dataset.productId);

            if (selectedIds.length === 0) {
                showNotification('Warning', 'Please select at least one product to repair images', 'warning');
                return;
            }

            repairSelectedImages(selectedIds);
        });

        document.getElementById('standardizeSelectedButton').addEventListener('click', function() {
            const selectedIds = Array.from(document.querySelectorAll('.row-checkbox:checked')).map(checkbox => checkbox.dataset.productId);

            if (selectedIds.length === 0) {
                showNotification('Warning', 'Please select at least one product to standardize', 'warning');
                return;
            }

            standardizeSelectedProducts(selectedIds);
        });

        document.getElementById('standardizeAllMatchingButton').addEventListener('click', async function() {
            try {
                // Show loading indicator while fetching all matching products
                const loadingOverlay = document.createElement('div');
                loadingOverlay.className = 'repricing-overlay';
                loadingOverlay.innerHTML = `
                    <div class="spinner-border text-light" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="loading-text">Fetching all matching products...</div>
                `;
                document.body.appendChild(loadingOverlay);

                // Get current filter parameters
                const itemType = document.getElementById('itemTypeSelect').value;
                const vendor = document.getElementById('vendorSelect').value;
                const productType = document.getElementById('productTypeSelect').value;
                const expansionName = document.getElementById('expansionNameSelect').value;
                const firstLetter = document.getElementById('firstLetterSelect').value;
                const searchTerm = document.getElementById('searchTermInput').value;
                const inStockOnly = document.getElementById('inStockOnlyToggle').checked;
                const quantityFilterType = document.getElementById('quantityFilterType').value;
                const quantityFilterValue = document.getElementById('quantityFilterValue').value;
                const searchNumber = searchNumberValue;

                // Build parameters for API call to get ALL matching products
                const params = new URLSearchParams({
                    itemType,
                    vendor,
                    productType,
                    expansionName,
                    searchTerm,
                    inStockOnly: inStockOnly.toString(),
                    page: 1,
                    per_page: 10000, // Request a large number to get all products
                    ids_only: 'true' // New parameter to only return product IDs
                });

                // Add optional parameters
                if (firstLetter && firstLetter !== "") {
                    params.set('firstLetter', firstLetter);
                }
                if (searchNumber && searchNumber.trim() !== "") {
                    params.set('number', searchNumber);
                }
                if (quantityFilterType && quantityFilterValue) {
                    params.set('quantityFilterType', quantityFilterType);
                    params.set('quantityFilterValue', quantityFilterValue);
                }

                // Fetch all matching product IDs
                const response = await fetch(`/shopify/products/api/products?${params.toString()}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                // Remove loading overlay
                loadingOverlay.remove();

                // Extract product IDs
                let allProductIds = [];
                if (data.products && Array.isArray(data.products)) {
                    allProductIds = data.products.map(product => product._id);
                } else if (data.product_ids && Array.isArray(data.product_ids)) {
                    allProductIds = data.product_ids;
                }

                if (allProductIds.length === 0) {
                    showNotification('Warning', 'No products found to standardize. Please adjust your filters or search criteria.', 'warning');
                    return;
                }

                // Show confirmation dialog with actual total count
                const confirmMessage = `This will analyze and standardize ALL ${allProductIds.length} products that match your current filters (not just the ones displayed on screen). This may take a considerable amount of time. Do you want to continue?`;
                
                if (confirm(confirmMessage)) {
                    standardizeSelectedProducts(allProductIds);
                }

            } catch (error) {
                // Remove loading overlay on error
                const loadingOverlay = document.querySelector('.repricing-overlay');
                if (loadingOverlay) {
                    loadingOverlay.remove();
                }

                console.error('Error fetching all matching products:', error);
                showNotification('Error', `Failed to fetch matching products: ${error.message}`, 'error');
            }
        });

        document.getElementById('exportCsvButton').addEventListener('click', exportToCsv);

        async function repairSelectedImages(productIds) {
            try {
                // Create and show the repair overlay with progress bar
                const repairOverlay = document.createElement('div');
                repairOverlay.className = 'repricing-overlay';
                repairOverlay.style.position = 'fixed';
                repairOverlay.style.top = '0';
                repairOverlay.style.left = '0';
                repairOverlay.style.width = '100%';
                repairOverlay.style.height = '100%';
                repairOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                repairOverlay.style.display = 'flex';
                repairOverlay.style.flexDirection = 'column';
                repairOverlay.style.justifyContent = 'center';
                repairOverlay.style.alignItems = 'center';
                repairOverlay.style.zIndex = '9999';

                repairOverlay.innerHTML = `
                    <div class="spinner-border text-light" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="loading-text mb-3" style="color: white; margin-top: 15px;">Repairing selected images...</div>
                    <div class="progress" style="width: 80%; max-width: 500px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%"
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="text-light mt-2">
                        <span id="repairProgressCount">0</span> of <span id="repairTotalCount">${productIds.length}</span> completed
                    </div>
                `;
                document.body.appendChild(repairOverlay);
                document.body.style.overflow = 'hidden'; // Prevent scrolling while loading

                const progressBar = repairOverlay.querySelector('.progress-bar');
                const progressCount = repairOverlay.querySelector('#repairProgressCount');

                let successCount = 0;
                let errorCount = 0;

                // Process each product one by one
                for (let i = 0; i < productIds.length; i++) {
                    const productId = productIds[i];

                    try {
                        // Call the repair-image API
                        const response = await fetch('/shopify/bulk-edit/api/repair-image', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                product_id: productId
                            })
                        });

                        const data = await response.json();

                        if (response.ok && data.success) {
                            successCount++;

                            // Update the product image in the table if it's visible
                            const productRow = document.querySelector(`tr[data-product-id="${productId}"]`);
                            if (productRow && data.image_url) {
                                const imgElement = productRow.querySelector('img');
                                if (imgElement) {
                                    imgElement.src = data.image_url;
                                }
                            }
                        } else {
                            errorCount++;
                            console.error(`Failed to repair image for product ${productId}: ${data.message || 'Unknown error'}`);
                        }
                    } catch (error) {
                        errorCount++;
                        console.error(`Error repairing image for product ${productId}:`, error);
                    }

                    // Update progress
                    const progress = ((i + 1) / productIds.length) * 100;
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    progressBar.textContent = `${Math.round(progress)}%`;
                    progressCount.textContent = i + 1;

                    // Small delay to prevent UI freezing
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // Show completion notification
                showNotification(
                    'Repair Complete',
                    `Processed ${productIds.length} products: ${successCount} successful, ${errorCount} failed.`,
                    successCount > 0 ? 'success' : 'warning'
                );

                // Refresh the product list to show updated images
                fetchProducts(currentPage);

            } catch (error) {
                console.error('Error in bulk repair process:', error);
                showNotification('Error', `An error occurred during the repair process: ${error.message}`, 'error');
            } finally {
                // Remove the overlay
                const repairOverlay = document.querySelector('.repricing-overlay');
                if (repairOverlay) {
                    repairOverlay.remove();
                }
                document.body.style.overflow = ''; // Restore scrolling
            }
        }

        async function applySingleStandardization(productId, analysisResult) {
            try {
                // Show loading state for this product
                const resultItem = document.querySelector(`[data-product-id="${productId}"]`);
                const applyButton = resultItem.querySelector('.standardize-approve-btn');

                // Disable the button and show loading
                applyButton.disabled = true;
                applyButton.innerHTML = `
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Applying...
                `;

                // Prepare the update data
                const updateData = {};

                // Add title if it has a discrepancy
                if (analysisResult.discrepancies.title) {
                    updateData.title = analysisResult.discrepancies.title.simulated;
                }

                // Add tags if they have a discrepancy
                if (analysisResult.discrepancies.tags) {
                    updateData.tags = analysisResult.discrepancies.tags.simulated;
                }

                // Call the API to update the product
                const response = await fetch('/shopify/standardizer/api/approve', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        update_data: updateData
                    })
                });

                const data = await response.json();

                // Update the UI based on the result
                if (response.ok && data.success) {
                    // Update the result item to show success
                    resultItem.innerHTML = `
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">✓</span>
                            <span>${analysisResult.title}</span>
                        </div>
                        <div class="small text-success ms-4">Standardization applied successfully</div>
                    `;

                    showNotification(
                        'Standardization Applied',
                        `Successfully applied standardization to "${analysisResult.title}"`,
                        'success'
                    );
                } else {
                    // Restore the button
                    applyButton.disabled = false;
                    applyButton.innerHTML = 'Apply Changes';

                    showNotification(
                        'Standardization Failed',
                        `Failed to apply standardization: ${data.error || 'Unknown error'}`,
                        'error'
                    );
                }
            } catch (error) {
                console.error(`Error applying standardization for product ${productId}:`, error);

                // Find the result item and restore the button
                const resultItem = document.querySelector(`[data-product-id="${productId}"]`);
                if (resultItem) {
                    const applyButton = resultItem.querySelector('.standardize-approve-btn');
                    if (applyButton) {
                        applyButton.disabled = false;
                        applyButton.innerHTML = 'Apply Changes';
                    }
                }

                showNotification(
                    'Standardization Error',
                    `An error occurred: ${error.message}`,
                    'error'
                );
            }
        }

        async function applyAllStandardizationChanges(analysisResults) {
            // Filter to only products with discrepancies
            const productsToUpdate = analysisResults.filter(result => result.hasDiscrepancies);

            if (productsToUpdate.length === 0) {
                showNotification('No Changes', 'No products with discrepancies to update', 'info');
                return;
            }

            // Create and show a progress overlay
            const progressOverlay = document.createElement('div');
            progressOverlay.className = 'repricing-overlay';
            progressOverlay.style.position = 'fixed';
            progressOverlay.style.top = '0';
            progressOverlay.style.left = '0';
            progressOverlay.style.width = '100%';
            progressOverlay.style.height = '100%';
            progressOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            progressOverlay.style.display = 'flex';
            progressOverlay.style.flexDirection = 'column';
            progressOverlay.style.justifyContent = 'center';
            progressOverlay.style.alignItems = 'center';
            progressOverlay.style.zIndex = '10000';

            progressOverlay.innerHTML = `
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="loading-text mb-3" style="color: white; margin-top: 15px;">Applying standardization changes...</div>
                <div class="progress" style="width: 80%; max-width: 500px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                         role="progressbar" style="width: 0%"
                         aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div class="text-light mt-2">
                    <span id="updateProgressCount">0</span> of <span id="updateTotalCount">${productsToUpdate.length}</span> completed
                </div>
                <div id="updateResults" class="mt-4 text-light" style="width: 80%; max-width: 600px; max-height: 300px; overflow-y: auto;">
                    <h5 class="text-center mb-3">Results</h5>
                    <div id="updateResultsList"></div>
                </div>
            `;

            document.body.appendChild(progressOverlay);

            const progressBar = progressOverlay.querySelector('.progress-bar');
            const progressCount = progressOverlay.querySelector('#updateProgressCount');
            const resultsList = progressOverlay.querySelector('#updateResultsList');

            let successCount = 0;
            let errorCount = 0;

            // Process each product one by one
            for (let i = 0; i < productsToUpdate.length; i++) {
                const result = productsToUpdate[i];
                const productId = result.productId;

                try {
                    // Prepare the update data
                    const updateData = {};

                    // Add title if it has a discrepancy
                    if (result.discrepancies.title) {
                        updateData.title = result.discrepancies.title.simulated;
                    }

                    // Add tags if they have a discrepancy
                    if (result.discrepancies.tags) {
                        updateData.tags = result.discrepancies.tags.simulated;
                    }

                    // Call the API to update the product
                    const response = await fetch('/shopify/standardizer/api/approve', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            update_data: updateData
                        })
                    });

                    const data = await response.json();

                    // Create result item
                    const resultItem = document.createElement('div');
                    resultItem.className = 'mb-2 p-2 border-bottom border-secondary';

                    if (response.ok && data.success) {
                        successCount++;
                        resultItem.innerHTML = `
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2">✓</span>
                                <span>${result.title}</span>
                            </div>
                            <div class="small text-success ms-4">Standardization applied successfully</div>
                        `;

                        // Also update the item in the analysis results list if it's visible
                        const analysisItem = document.querySelector(`[data-product-id="${productId}"]`);
                        if (analysisItem) {
                            analysisItem.innerHTML = `
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-success me-2">✓</span>
                                    <span>${result.title}</span>
                                </div>
                                <div class="small text-success ms-4">Standardization applied successfully</div>
                            `;
                        }
                    } else {
                        errorCount++;
                        resultItem.innerHTML = `
                            <div class="d-flex align-items-center">
                                <span class="badge bg-danger me-2">✗</span>
                                <span>${result.title}</span>
                            </div>
                            <div class="small text-danger ms-4">Error: ${data.error || 'Unknown error'}</div>
                        `;
                    }

                    resultsList.appendChild(resultItem);
                    resultsList.scrollTop = resultsList.scrollHeight; // Auto-scroll to bottom

                } catch (error) {
                    errorCount++;

                    // Create error item
                    const errorItem = document.createElement('div');
                    errorItem.className = 'mb-2 p-2 border-bottom border-secondary';
                    errorItem.innerHTML = `
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger me-2">✗</span>
                            <span>${result.title}</span>
                        </div>
                        <div class="small text-danger ms-4">Error: ${error.message || 'Unknown error'}</div>
                    `;

                    resultsList.appendChild(errorItem);
                    resultsList.scrollTop = resultsList.scrollHeight; // Auto-scroll to bottom

                    console.error(`Error applying standardization for product ${productId}:`, error);
                }

                // Update progress
                const progress = ((i + 1) / productsToUpdate.length) * 100;
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = `${Math.round(progress)}%`;
                progressCount.textContent = i + 1;

                // Small delay to prevent UI freezing
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Add a "Close" button at the bottom of the overlay
            const closeButton = document.createElement('button');
            closeButton.className = 'btn btn-light mt-3';
            closeButton.textContent = 'Close';
            closeButton.addEventListener('click', function() {
                progressOverlay.remove();
            });
            progressOverlay.appendChild(closeButton);

            // Update the loading text to show completion
            const loadingText = progressOverlay.querySelector('.loading-text');
            loadingText.textContent = 'Updates Complete';

            // Hide the spinner
            const spinner = progressOverlay.querySelector('.spinner-border');
            spinner.style.display = 'none';

            // Show completion notification
            showNotification(
                'Standardization Complete',
                `Applied changes to ${successCount} products with ${errorCount} errors.`,
                errorCount > 0 ? 'warning' : 'success'
            );
        }

        async function standardizeSelectedProducts(productIds) {
            try {
                // Create and show the standardize overlay with progress bar
                const standardizeOverlay = document.createElement('div');
                standardizeOverlay.className = 'repricing-overlay';
                standardizeOverlay.style.position = 'fixed';
                standardizeOverlay.style.top = '0';
                standardizeOverlay.style.left = '0';
                standardizeOverlay.style.width = '100%';
                standardizeOverlay.style.height = '100%';
                standardizeOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                standardizeOverlay.style.display = 'flex';
                standardizeOverlay.style.flexDirection = 'column';
                standardizeOverlay.style.justifyContent = 'center';
                standardizeOverlay.style.alignItems = 'center';
                standardizeOverlay.style.zIndex = '9999';

                standardizeOverlay.innerHTML = `
                    <div class="spinner-border text-light" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="loading-text mb-3" style="color: white; margin-top: 15px;">Analyzing selected products...</div>
                    <div class="progress" style="width: 80%; max-width: 500px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                             role="progressbar" style="width: 0%"
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="text-light mt-2">
                        <span id="standardizeProgressCount">0</span> of <span id="standardizeTotalCount">${productIds.length}</span> completed
                    </div>
                    <div id="standardizeResults" class="mt-4 text-light" style="width: 80%; max-width: 600px; max-height: 300px; overflow-y: auto;">
                        <h5 class="text-center mb-3">Results</h5>
                        <div id="standardizeResultsList"></div>
                    </div>
                `;
                document.body.appendChild(standardizeOverlay);
                document.body.style.overflow = 'hidden'; // Prevent scrolling while loading

                const progressBar = standardizeOverlay.querySelector('.progress-bar');
                const progressCount = standardizeOverlay.querySelector('#standardizeProgressCount');
                const resultsList = standardizeOverlay.querySelector('#standardizeResultsList');

                let productsWithDiscrepancies = 0;
                let productsWithoutDiscrepancies = 0;
                let errorCount = 0;

                // Store analysis results for each product
                const analysisResults = [];

                // Process each product one by one
                for (let i = 0; i < productIds.length; i++) {
                    const productId = productIds[i];

                    try {
                        // Call the analyze_product API
                        const response = await fetch('/shopify/standardizer/api/analyze_product', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                product_id: productId
                            })
                        });

                        const data = await response.json();

                        if (response.ok && data.success) {
                            // Store the analysis result
                            analysisResults.push({
                                productId: productId,
                                title: data.product_title,
                                hasDiscrepancies: data.has_discrepancies,
                                discrepancies: data.discrepancies || {}
                            });

                            // Create result item
                            const resultItem = document.createElement('div');
                            resultItem.className = 'mb-2 p-2 border-bottom border-secondary';
                            resultItem.setAttribute('data-product-id', productId);

                            if (data.has_discrepancies) {
                                productsWithDiscrepancies++;

                                // Format discrepancies for display
                                let discrepanciesHtml = '';
                                if (data.discrepancies) {
                                    discrepanciesHtml = '<div class="mt-2 ms-4 small">';
                                    for (const [field, values] of Object.entries(data.discrepancies)) {
                                        discrepanciesHtml += `
                                            <div class="mb-2">
                                                <strong>${field.toUpperCase()}:</strong>
                                                <div class="text-danger">Current: ${values.existing}</div>
                                                <div class="text-success">Suggested: ${values.simulated}</div>
                                            </div>
                                        `;
                                    }
                                    discrepanciesHtml += '</div>';
                                }

                                resultItem.innerHTML = `
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2">!</span>
                                        <span>${data.product_title}</span>
                                        <button class="btn btn-sm btn-success ms-auto standardize-approve-btn" data-product-id="${productId}">
                                            Apply Changes
                                        </button>
                                    </div>
                                    <div class="small text-warning ms-4">Has discrepancies that need standardization</div>
                                    ${discrepanciesHtml}
                                `;
                            } else {
                                productsWithoutDiscrepancies++;
                                resultItem.innerHTML = `
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2">✓</span>
                                        <span>${data.product_title}</span>
                                    </div>
                                    <div class="small text-success ms-4">Already standardized</div>
                                `;
                            }

                            resultsList.appendChild(resultItem);
                            resultsList.scrollTop = resultsList.scrollHeight; // Auto-scroll to bottom

                        } else {
                            errorCount++;

                            // Create error item
                            const errorItem = document.createElement('div');
                            errorItem.className = 'mb-2 p-2 border-bottom border-secondary';
                            errorItem.innerHTML = `
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-danger me-2">✗</span>
                                    <span>Product ID: ${productId}</span>
                                </div>
                                <div class="small text-danger ms-4">Error: ${data.error || 'Unknown error'}</div>
                            `;

                            resultsList.appendChild(errorItem);
                            resultsList.scrollTop = resultsList.scrollHeight; // Auto-scroll to bottom

                            console.error(`Failed to analyze product ${productId}: ${data.error || 'Unknown error'}`);
                        }
                    } catch (error) {
                        errorCount++;

                        // Create error item
                        const errorItem = document.createElement('div');
                        errorItem.className = 'mb-2 p-2 border-bottom border-secondary';
                        errorItem.innerHTML = `
                            <div class="d-flex align-items-center">
                                <span class="badge bg-danger me-2">✗</span>
                                <span>Product ID: ${productId}</span>
                            </div>
                            <div class="small text-danger ms-4">Error: ${error.message || 'Unknown error'}</div>
                        `;

                        resultsList.appendChild(errorItem);
                        resultsList.scrollTop = resultsList.scrollHeight; // Auto-scroll to bottom

                        console.error(`Error analyzing product ${productId}:`, error);
                    }

                    // Update progress
                    const progress = ((i + 1) / productIds.length) * 100;
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    progressBar.textContent = `${Math.round(progress)}%`;
                    progressCount.textContent = i + 1;

                    // Small delay to prevent UI freezing
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // Add buttons at the bottom of the overlay
                const buttonsContainer = document.createElement('div');
                buttonsContainer.className = 'mt-3 d-flex gap-2';

                // Add "Apply All Changes" button if there are discrepancies
                if (productsWithDiscrepancies > 0) {
                    const applyAllButton = document.createElement('button');
                    applyAllButton.className = 'btn btn-success';
                    applyAllButton.textContent = 'Apply All Changes';
                    applyAllButton.addEventListener('click', async function() {
                        // Confirm before applying all changes
                        if (confirm(`Are you sure you want to apply standardization changes to all ${productsWithDiscrepancies} products with discrepancies?`)) {
                            await applyAllStandardizationChanges(analysisResults);
                        }
                    });
                    buttonsContainer.appendChild(applyAllButton);
                }

                // Add "Close" button
                const closeButton = document.createElement('button');
                closeButton.className = 'btn btn-light';
                closeButton.textContent = 'Close';
                closeButton.addEventListener('click', function() {
                    standardizeOverlay.remove();
                    document.body.style.overflow = ''; // Restore scrolling
                });
                buttonsContainer.appendChild(closeButton);

                standardizeOverlay.appendChild(buttonsContainer);

                // Update the loading text to show completion
                const loadingText = standardizeOverlay.querySelector('.loading-text');
                loadingText.textContent = 'Analysis Complete';

                // Hide the spinner
                const spinner = standardizeOverlay.querySelector('.spinner-border');
                spinner.style.display = 'none';

                // Show completion notification
                showNotification(
                    'Standardization Analysis Complete',
                    `Processed ${productIds.length} products:
                     ${productsWithDiscrepancies} need standardization,
                     ${productsWithoutDiscrepancies} already standardized,
                     ${errorCount} errors.`,
                    errorCount === 0 ? 'success' : 'warning'
                );

                // Add event listener for "Apply Changes" buttons
                document.addEventListener('click', async function(e) {
                    if (e.target.classList.contains('standardize-approve-btn')) {
                        const productId = e.target.getAttribute('data-product-id');
                        const result = analysisResults.find(r => r.productId === productId);

                        if (result && result.hasDiscrepancies) {
                            await applySingleStandardization(productId, result);
                        }
                    }
                });

            } catch (error) {
                console.error('Error in bulk standardize process:', error);
                showNotification('Error', `An error occurred during the standardization process: ${error.message}`, 'error');

                // Remove the overlay
                const standardizeOverlay = document.querySelector('.repricing-overlay');
                if (standardizeOverlay) {
                    standardizeOverlay.remove();
                }
                document.body.style.overflow = ''; // Restore scrolling
            }
        }


        repriceButton.addEventListener('click', async function() {
            // Create and show the repricing overlay
            const repricingOverlay = document.createElement('div');
            repricingOverlay.className = 'repricing-overlay';
            repricingOverlay.innerHTML = `
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="loading-text">Calculating new prices...</div>
            `;
            document.body.appendChild(repricingOverlay);

            try {
                const response = await fetch('/shopify/products/api/reprice', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        productId: currentProductId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (data.message) {
                    // Update TCGPlayer link
                    const tcgplayerLink = document.getElementById('tcgplayerLink');
                    if (data.tcgplayer_url) {
                        tcgplayerLink.href = data.tcgplayer_url;
                        tcgplayerLink.style.display = 'inline-block';
                    } else {
                        tcgplayerLink.style.display = 'none';
                    }

                    // Refresh the product modal to show updated prices
                    await openProductModal(currentProductId);
                    // Update the product row in the main table
                    updateProductRow(currentProductId);

                    // Show the new values
                    if (data.price_changes && data.price_changes.length > 0) {
                        const priceChangesHtml = data.price_changes.map(change =>
                            `<p>${change.variant_title}: ${getCurrencySymbol(userCurrency)}${change.old_price} → ${getCurrencySymbol(userCurrency)}${change.new_price}</p>`
                        ).join('');

                        showNotification("Price Calculation Complete", `
                            <div>
                                <h6>New Prices:</h6>
                                ${priceChangesHtml}
                                <p class="mt-2"><small>Use "Sync to Shopify" to apply these changes</small></p>
                            </div>
                        `, "success");
                    } else {
                        showNotification("Price Calculation Complete", "No price changes were necessary.", "info");
                    }
                } else {
                    throw new Error('No message received from server');
                }
            } catch (error) {
                console.error("Error during price calculation:", error);
                showNotification("Calculation Error", `An error occurred while calculating prices: ${error.message}. Please try again or contact support if the issue persists.`, "error");
            } finally {
                repricingOverlay.remove();
            }
        });

        function removeLoadingOverlay() {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        }

        function showNotification(title, message, type = 'info') {
            const notificationContainer = document.getElementById('notificationContainer') || createNotificationContainer();
            const notification = createNotificationElement(title, message, type);

            notificationContainer.appendChild(notification);
            const bsToast = new bootstrap.Toast(notification, { autohide: true, delay: 5000 });
            bsToast.show();

            notification.addEventListener('hidden.bs.toast', function () {
                notification.remove();
                if (notificationContainer.children.length === 0) {
                    notificationContainer.remove();
                }
            });

            hideLoading();
        }

        function createNotificationContainer() {
            const container = document.createElement('div');
            container.id = 'notificationContainer';
            container.style.position = 'fixed';
            container.style.top = '20px';
            container.style.right = '20px';
            container.style.zIndex = '10000';
            document.body.appendChild(container);
            return container;
        }

        function createNotificationElement(title, message, type) {
            const notification = document.createElement('div');
            notification.className = `toast bg-${type} text-white`;
            notification.setAttribute('role', 'alert');
            notification.setAttribute('aria-live', 'assertive');
            notification.setAttribute('aria-atomic', 'true');

            notification.innerHTML = `
                <div class="toast-header bg-${type} text-white">
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            `;

            return notification;
        }

        function showConfirmDialog(title, message) {
            return new Promise((resolve) => {
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content bg-dark text-white">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="confirmButton">Confirm</button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();

                modal.querySelector('#confirmButton').addEventListener('click', () => {
                    modalInstance.hide();
                    resolve(true);
                });

                modal.addEventListener('hidden.bs.modal', () => {
                    modal.remove();
                    resolve(false);
                });
            });
        }

        // Remove any existing overlay when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            const existingOverlay = document.querySelector('.loading-overlay');
            if (existingOverlay) {
                existingOverlay.remove();
            }
        });

        // Mutation observer removed


        // Remove any existing event listeners by cloning and replacing
        const newItemTypeSelect = itemTypeSelect.cloneNode(true);
        itemTypeSelect.parentNode.replaceChild(newItemTypeSelect, itemTypeSelect);
        itemTypeSelect = newItemTypeSelect;

        const newProductTypeSelect = productTypeSelect.cloneNode(true);
        productTypeSelect.parentNode.replaceChild(newProductTypeSelect, productTypeSelect);
        productTypeSelect = newProductTypeSelect;

        const newExpansionNameSelect = expansionNameSelect.cloneNode(true);
        expansionNameSelect.parentNode.replaceChild(newExpansionNameSelect, expansionNameSelect);
        expansionNameSelect = newExpansionNameSelect;

        // Add new event listeners
        itemTypeSelect.addEventListener('change', async function() {
            console.log("Item type changed to:", this.value);
            try {
                await fetchVendors();
                const vendorSelect = document.getElementById('vendorSelect');
                vendorSelect.innerHTML = '<option value="">Select Vendor</option>';
                vendorSelect.disabled = false;
                productTypeSelect.innerHTML = '<option value="">Select Product Type</option>';
                productTypeSelect.disabled = true;
                expansionNameSelect.innerHTML = '<option value="">Select Expansion Name</option>';
                expansionNameSelect.disabled = true;

                // Fetch products with the new item type
                console.log("Fetching products after item type change");
                fetchProducts(1);
            } catch (error) {
                console.error("Error in item type change handler:", error);
            }
        });

        const vendorSelect = document.getElementById('vendorSelect');
        vendorSelect.addEventListener('change', async function() {
            console.log("Vendor changed to:", this.value);
            try {
                await fetchProductTypes();
                expansionNameSelect.innerHTML = '<option value="">Select Expansion Name</option>';
                expansionNameSelect.disabled = true;

                // Reset stored products to force a fresh fetch
                allProducts = [];

                // Fetch products with the new vendor
                console.log("Fetching products after vendor change");
                fetchProducts(1);
            } catch (error) {
                console.error("Error in vendor change handler:", error);
            }
        });

        productTypeSelect.addEventListener('change', async function() {
            console.log("Product type changed to:", this.value);
            try {
                await fetchExpansionNames();

                // --- First Letter Dropdown logic ---
                const firstLetterSelect = document.getElementById('firstLetterSelect');
                if (this.value) {
                    firstLetterSelect.disabled = false;
                    firstLetterSelect.style.display = "";
                } else {
                    firstLetterSelect.disabled = true;
                    firstLetterSelect.style.display = "none";
                    firstLetterSelect.value = "";
                }

                // Reset stored products to force a fresh fetch
                allProducts = [];

                // Fetch products with the new product type
                console.log("Fetching products after product type change");
                fetchProducts(1);
            } catch (error) {
                console.error("Error in product type change handler:", error);
            }
        });

        expansionNameSelect.addEventListener('change', function() {
            console.log("Expansion name changed to:", this.value);
            try {
                // Reset stored products to force a fresh fetch
                allProducts = [];

                // Fetch products with the new expansion name
                console.log("Fetching products after expansion name change");
                fetchProducts(1);
            } catch (error) {
                console.error("Error in expansion name change handler:", error);
            }
        });
        searchTermInput.addEventListener('input', debounceFetch(() => fetchProducts(currentPage), 300));
        document.getElementById('sortPriceSelect').addEventListener('change', debounceFetch(() => fetchProducts(currentPage), 300));

        // --- Search by Number event handler ---
        document.getElementById('searchNumberBtn').addEventListener('click', function() {
            const input = document.getElementById('searchNumberInput');
            searchNumberValue = input.value.trim();
            fetchProducts(1);
        });

        // Allow pressing Enter in the number field to trigger search
        document.getElementById('searchNumberInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                searchNumberValue = this.value.trim();
                fetchProducts(1);
            }
        });

        // Add event listeners for quantity filter
        const quantityFilterType = document.getElementById('quantityFilterType');
        const quantityFilterValue = document.getElementById('quantityFilterValue');
        const applyQuantityFilter = document.getElementById('applyQuantityFilter');

        // Check if elements exist before adding event listeners
        if (quantityFilterType && quantityFilterValue && applyQuantityFilter) {

        // Function to update the Apply button state
        function updateApplyButtonState() {
            const hasType = quantityFilterType.value && quantityFilterType.value !== '';
            const hasValue = quantityFilterValue.value && quantityFilterValue.value !== '';
            const shouldEnable = hasType && hasValue && parseInt(quantityFilterValue.value) >= 0;

            applyQuantityFilter.disabled = !shouldEnable;

            console.log('Apply button state update:', {
                hasType,
                hasValue,
                value: quantityFilterValue.value,
                shouldEnable,
                disabled: applyQuantityFilter.disabled
            });
        }

        quantityFilterType.addEventListener('change', function() {
            console.log('Quantity filter type changed to:', this.value);

            // Enable/disable the quantity value input based on filter type selection
            if (this.value) {
                quantityFilterValue.disabled = false;
                quantityFilterValue.focus();
            } else {
                quantityFilterValue.disabled = true;
                quantityFilterValue.value = '';
                // If clearing the filter, refresh products
                console.log('Clearing quantity filter, fetching products...');
                fetchProducts(1);
            }
            updateApplyButtonState();
        });

        quantityFilterValue.addEventListener('input', function() {
            console.log('Quantity filter value changed to:', this.value);
            updateApplyButtonState();
        });

        // Apply button click handler
        applyQuantityFilter.addEventListener('click', function() {
            console.log('Apply button clicked!', {
                type: quantityFilterType.value,
                value: quantityFilterValue.value,
                disabled: this.disabled
            });

            if (quantityFilterType.value && quantityFilterValue.value) {
                console.log('Applying quantity filter and fetching products...');

                // Show visual feedback
                this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Applying...';
                this.disabled = true;

                // Reset to first page when applying filter
                currentPage = 1;
                allProducts = []; // Clear cached products to force fresh fetch
                hasMoreProducts = true; // Reset pagination state

                // Fetch products with the filter
                fetchProducts(1).then(() => {
                    // Reset button after fetch completes
                    this.innerHTML = 'Apply';
                    updateApplyButtonState();
                }).catch((error) => {
                    console.error('Error applying quantity filter:', error);
                    this.innerHTML = 'Apply';
                    updateApplyButtonState();
                });
            } else {
                console.warn('Cannot apply filter - missing type or value');
            }
        });

        // Initialize button state
        updateApplyButtonState();

        } else {
            console.error('Quantity filter elements not found:', {
                quantityFilterType: !!quantityFilterType,
                quantityFilterValue: !!quantityFilterValue,
                applyQuantityFilter: !!applyQuantityFilter
            });
        }


    // Add event listener for price and quantity input changes
    document.addEventListener('input', async function(event) {
        if (event.target.classList.contains('price-input')) {
            const saveBtn = event.target.parentElement.querySelector('.save-price-btn');
            const originalPrice = parseFloat(event.target.dataset.originalPrice);
            const newPrice = parseFloat(event.target.value);

            if (originalPrice !== newPrice) {
                saveBtn.style.display = 'inline-block';
            } else {
                saveBtn.style.display = 'none';
            }
        } else if (event.target.classList.contains('quantity-input')) {
            const saveBtn = event.target.parentElement.querySelector('.save-quantity-btn');
            const originalQuantity = parseInt(event.target.dataset.originalQuantity);
            const newQuantity = parseInt(event.target.value);

            if (originalQuantity !== newQuantity) {
                saveBtn.style.display = 'inline-block';
            } else {
                saveBtn.style.display = 'none';
            }
        }
    });

    // Add event listener for save price and quantity button clicks
    document.addEventListener('click', async function(event) {
        if (event.target.classList.contains('save-price-btn') || event.target.classList.contains('save-quantity-btn')) {
            const row = event.target.closest('.variant-row');
            const priceInput = row.querySelector('.price-input');
            const quantityInput = row.querySelector('.quantity-input');
            const variantId = priceInput.dataset.variantId;
            const newPrice = parseFloat(priceInput.value);
            const newQuantity = parseInt(quantityInput.value);

            showLoading();
            try {
                const response = await fetch('/shopify/products/api/update_variant', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        productId: currentProductId,
                        variantId: variantId,
                        price: newPrice,
                        quantity: newQuantity
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Update both price and quantity original values
                priceInput.dataset.originalPrice = newPrice.toFixed(2);
                quantityInput.dataset.originalQuantity = newQuantity;

                // Hide both save buttons
                row.querySelector('.save-price-btn').style.display = 'none';
                row.querySelector('.save-quantity-btn').style.display = 'none';

                showNotification(
                    "Update Successful",
                    `Price updated to ${getCurrencySymbol(userCurrency)}${newPrice.toFixed(2)} and quantity to ${newQuantity}`,
                    "success"
                );

                // Update the product row in the main table
                updateProductRow(currentProductId);

            } catch (error) {
                console.error("Error updating price:", error);
                showNotification(
                    "Error",
                    `Failed to update price: ${error.message}`,
                    "error"
                );
                // Revert the input to its original value
                priceInput.value = priceInput.dataset.originalPrice;
            } finally {
                hideLoading();
            }
        }
    });

        // Function to collect all variant matches
        async function saveAllMatches() {
            const matches = [];
            const variantSelects = document.querySelectorAll('.variant-select');

            for (const select of variantSelects) {
                if (select.value) {
                    const cell = select.closest('.variant-cell');
                    const itemId = cell.dataset.itemId;
                    const variant = {
                        id: select.value,
                        title: select.options[select.selectedIndex].text
                    };
                    matches.push({ itemId, variant });
                }
            }

            if (matches.length === 0) {
                showNotification('Warning', 'No matches to save', 'warning');
                return;
            }

            try {
                const response = await fetch('/shopify/products/api/save_all_matches', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ matches })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                showNotification('Success', data.message, 'success');
                await loadStagedInventory(); // Reload the table
            } catch (error) {
                console.error('Error saving matches:', error);
                showNotification('Error', 'Failed to save matches', 'error');
            }
        }

    // Add event listener for manual override toggles in the table
    document.addEventListener('change', async function(e) {
        if (e.target.classList.contains('manual-override-toggle')) {
            const toggle = e.target;
            const productId = toggle.dataset.productId;
            const newState = toggle.checked;
            const statusSpinner = toggle.parentElement.querySelector('.manual-override-status');

            // Show loading spinner
            toggle.style.display = 'none';
            statusSpinner.style.display = 'block';

            try {
                // First get the current state
                const stateResponse = await fetch(`/shopify/products/api/product/${productId}`);
                if (!stateResponse.ok) {
                    throw new Error(`HTTP error! status: ${stateResponse.status}`);
                }
                const productData = await stateResponse.json();

                // Only update if the state has actually changed
                if (productData.manualOverride !== newState) {
                    const response = await fetch('/shopify/products/api/update_manual_override', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            productId: productId,
                            manualOverride: newState
                        })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        showNotification(
                            "Update Successful",
                            `Price Override ${newState ? 'enabled' : 'disabled'}. This product will ${newState ? 'now be' : 'no longer be'} excluded from automatic pricing updates.`,
                            "success"
                        );
                    } else {
                        throw new Error(data.error || 'Failed to update Price Override');
                    }
                }
            } catch (error) {
                console.error("Error updating manual override:", error);
                showNotification(
                    "Error",
                    `Failed to update Price Override: ${error.message}`,
                    "error"
                );
                // Revert checkbox state on error
                toggle.checked = !toggle.checked;
            } finally {
                // Hide loading spinner and show toggle
                statusSpinner.style.display = 'none';
                toggle.style.display = 'block';
            }
        }
    });

    // Handle select all functionality with counter
    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll('.row-checkbox:checked').length;
        const selectedCountDiv = document.getElementById('selectedCount');

        if (selectedCount > 0) {
            selectedCountDiv.textContent = `${selectedCount} selected`;
        } else {
            selectedCountDiv.textContent = '';
        }
    }

    document.getElementById('selectAllProducts').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // Update select all checkbox and counter when individual checkboxes change
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('row-checkbox')) {
            const selectAllCheckbox = document.getElementById('selectAllProducts');
            const checkboxes = document.querySelectorAll('.row-checkbox');
            const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
            selectAllCheckbox.checked = allChecked;
            updateSelectedCount();
        }
    });

    // Remove bulk action button click handler as the button has been removed

        // Function to fetch and display product history
        async function showProductHistory(productId) {
            const historyModal = document.getElementById('historyModal');
            const historyLoading = document.getElementById('historyLoading');
            const historyContent = document.getElementById('historyContent');
            const noHistoryMessage = document.getElementById('noHistoryMessage');
            const historyRecords = document.getElementById('historyRecords');

            // Show the modal with loading indicator
            const bsModal = new bootstrap.Modal(historyModal);
            bsModal.show();

            historyLoading.style.display = 'block';
            historyContent.style.display = 'none';

            try {
                // We'll use the MongoDB ObjectId directly for querying history
                // This is the _id from the shProducts collection

                // Fetch history data from our API using the MongoDB ObjectId
                const historyResponse = await fetch(`/api/history?itemId=${productId}`);
                if (!historyResponse.ok) {
                    throw new Error(`HTTP error! status: ${historyResponse.status}`);
                }

                const historyData = await historyResponse.json();

                // Hide loading and show content
                historyLoading.style.display = 'none';
                historyContent.style.display = 'block';

                // Check if we have history records
                if (historyData && historyData.history && historyData.history.length > 0) {
                    noHistoryMessage.style.display = 'none';

                    // Format and display the history records
                    let historyHtml = '';

                    historyData.history.forEach(record => {
                        const timestamp = new Date(record.timestamp).toLocaleString();
                        const operationType = record.operationType || 'update';

                        // Format the date in the simplest possible format
                        let formattedDate;
                        try {
                            console.log("Raw timestamp:", JSON.stringify(record.timestamp));

                            // Handle MongoDB date format which might be in { $date: "ISO-string" } format
                            let dateValue;
                            if (record.timestamp && typeof record.timestamp === 'object') {
                                if (record.timestamp.$date) {
                                    // MongoDB format with $date field
                                    dateValue = record.timestamp.$date;
                                    console.log("Extracted $date value:", dateValue);
                                } else {
                                    // Other object format
                                    dateValue = new Date(record.timestamp).toISOString();
                                    console.log("Converted object to ISO string:", dateValue);
                                }
                            } else {
                                // Regular date string
                                dateValue = record.timestamp;
                                console.log("Using timestamp directly:", dateValue);
                            }

                            const date = new Date(dateValue);
                            console.log("Parsed date object:", date);

                            if (isNaN(date.getTime())) {
                                formattedDate = "unknown date";
                                console.error("Invalid date:", dateValue);
                            } else {
                                // Format as "today 4:00pm" or "Mar 30 4:00pm"
                                const today = new Date();
                                const isToday = date.getDate() === today.getDate() &&
                                               date.getMonth() === today.getMonth() &&
                                               date.getFullYear() === today.getFullYear();

                                const hours = date.getHours();
                                const minutes = date.getMinutes();
                                const ampm = hours >= 12 ? 'pm' : 'am';
                                const hour12 = hours % 12 || 12;
                                const timeStr = `${hour12}:${minutes.toString().padStart(2, '0')}${ampm}`;

                                if (isToday) {
                                    formattedDate = `today ${timeStr}`;
                                } else {
                                    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                    formattedDate = `${monthNames[date.getMonth()]} ${date.getDate()} ${timeStr}`;
                                }
                                console.log("Formatted date:", formattedDate);
                            }
                        } catch (e) {
                            console.error("Date parsing error:", e, "Record timestamp:", JSON.stringify(record.timestamp));
                            formattedDate = "unknown date";
                        }

                        // Only show price and quantity changes
                        if (record.fieldsChanged) {
                            // Check if there are any price or quantity changes in this record
                            const hasPriceOrQuantityChanges = Object.entries(record.fieldsChanged).some(([field, change]) => {
                                return field === 'price' || field.endsWith('.price') ||
                                       field === 'inventory_quantity' || field.endsWith('.inventory_quantity');
                            });

                            // Only process if there are price or quantity changes
                            if (hasPriceOrQuantityChanges) {
                                // Process each changed field separately
                                Object.entries(record.fieldsChanged).forEach(([field, change]) => {
                                    // Only show price and quantity changes
                                    if (field === 'price' || field.endsWith('.price')) {
                                        const fromValue = typeof change.from === 'number' ? change.from.toFixed(2) : change.from;
                                        const toValue = typeof change.to === 'number' ? change.to.toFixed(2) : change.to;

                                        historyHtml += `
                                            <div class="history-item mb-1 p-1 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="text-muted me-1">${formattedDate}</span>
                                                    <span class="badge bg-info me-1">${record.username || 'Unknown'}</span>
                                                    <span>price from ${fromValue} to ${toValue}</span>
                                                </div>
                                            </div>
                                        `;
                                    }
                                    // For quantity fields
                                    else if (field === 'inventory_quantity' || field.endsWith('.inventory_quantity')) {
                                        historyHtml += `
                                            <div class="history-item mb-1 p-1 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="text-muted me-1">${formattedDate}</span>
                                                    <span class="badge bg-info me-1">${record.username || 'Unknown'}</span>
                                                    <span>quantity from ${change.from} to ${change.to}</span>
                                                </div>
                                            </div>
                                        `;
                                    }
                                    // Skip all other fields
                                });
                            }
                        }
                    });

                    historyRecords.innerHTML = historyHtml;
                } else {
                    // Show no history message
                    noHistoryMessage.style.display = 'block';
                    historyRecords.innerHTML = '';
                }

            } catch (error) {
                console.error("Error fetching product history:", error);
                historyLoading.style.display = 'none';
                historyContent.style.display = 'block';
                historyRecords.innerHTML = `
                    <div class="alert alert-danger">
                        Error loading history data: ${error.message}. Please try again.
                    </div>
                `;
            }
        }

        // Format field values for display in history
        function formatValue(value) {
            if (value === null || value === undefined) {
                return '<em>null</em>';
            } else if (typeof value === 'object') {
                return JSON.stringify(value);
            } else {
                return String(value);
            }
        }

        // Add scroll event listener for infinite scrolling
        window.addEventListener('scroll', function() {
            if (isLoading || !hasMoreProducts) return;

            // Check if we're near the bottom of the page
            const scrollPosition = window.innerHeight + window.pageYOffset;
            const pageHeight = document.documentElement.scrollHeight;
            const scrollThreshold = pageHeight - 500; // Load more when 500px from bottom

            if (scrollPosition >= scrollThreshold) {
                // Load the next page
                currentPage++;
                fetchProducts(currentPage, true); // true = append mode
            }
        });

document.addEventListener('DOMContentLoaded', async function() {
        const itemTypeSelect = document.getElementById('itemTypeSelect');
        const productTypeSelect = document.getElementById('productTypeSelect');
        const expansionNameSelect = document.getElementById('expansionNameSelect');
        const firstLetterSelect = document.getElementById('firstLetterSelect');
        const searchTermInput = document.getElementById('searchTermInput');
        const inStockOnlyToggle = document.getElementById('inStockOnlyToggle');
        const repriceButton = document.getElementById('repriceButton');
        const productModal = document.getElementById('productModal');
        const productTable = document.getElementById('productTable');
        const setInventoryToZeroButton = document.getElementById('setInventoryToZeroButton');

        // --- First Letter dropdown change triggers new backend search ---
        firstLetterSelect.addEventListener('change', function() {
            console.log("First letter changed to:", this.value);
            // Reset stored products to force a fresh backend fetch
            allProducts = [];
            currentPage = 1; // Reset to first page

            // Clear any existing search number value when changing letter filter
            searchNumberValue = "";
            document.getElementById('searchNumberInput').value = "";

            // Force a new search with the first letter filter
            fetchProducts(1);
        });

        // --- Set Inventory to Zero button handler ---
        setInventoryToZeroButton.addEventListener('click', function() {
            // Get selected products
            const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
            const selectedProductIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-product-id'));

            if (selectedProductIds.length === 0) {
                showNotification('Warning', 'Please select at least one product to set inventory to zero', 'warning');
                return;
            }

            // Show confirmation dialog
            const confirmDialog = document.createElement('div');
            confirmDialog.className = 'modal fade';
            confirmDialog.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content bg-dark text-white">
                        <div class="modal-header">
                            <h5 class="modal-title">Confirm Set Inventory to Zero</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-danger">
                                <strong>WARNING:</strong> This action cannot be undone!
                            </div>
                            <p>You are about to set the inventory quantity to ZERO for <strong>${selectedProductIds.length} products</strong>
                            (including all their variants).</p>
                            <p>This will update both your local database and Shopify store.</p>
                            <div class="alert alert-warning mt-3">
                                <strong>IMPORTANT:</strong> Once started, this operation will run in the background and <u>CANNOT BE STOPPED</u>, even by an administrator.
                            </div>
                            <p class="text-danger font-weight-bold">Please confirm you understand this process will run to completion once initiated.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-danger" id="confirmSetToZero">Yes, Set to Zero</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(confirmDialog);

            const modal = new bootstrap.Modal(confirmDialog);
            modal.show();

            // Handle confirmation
            document.getElementById('confirmSetToZero').addEventListener('click', async function() {
                modal.hide();

                // Create and directly add a full-screen overlay to prevent any interaction
                // First, ensure any existing overlays are removed
                const existingOverlays = document.querySelectorAll('.repricing-overlay, .loading-overlay');
                existingOverlays.forEach(overlay => overlay.remove());

                // Add extra styling to ensure the overlay is visible and on top
                const style = document.createElement('style');
                style.id = 'inventory-overlay-style';
                style.textContent = `
                    .inventory-overlay {
                        position: fixed !important;
                        top: 0 !important;
                        left: 0 !important;
                        width: 100vw !important;
                        height: 100vh !important;
                        background-color: rgba(0, 0, 0, 0.9) !important;
                        display: flex !important;
                        flex-direction: column !important;
                        justify-content: center !important;
                        align-items: center !important;
                        z-index: 100000 !important;
                        backdrop-filter: blur(5px) !important;
                    }
                `;
                document.head.appendChild(style);

                // Create and show progress overlay with the special class
                const progressOverlay = document.createElement('div');
                progressOverlay.className = 'inventory-overlay';
                progressOverlay.innerHTML = `
                    <div class="spinner-border text-light" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div style="color: white; margin-top: 20px; font-size: 1.2rem; font-weight: bold;">Setting inventory to zero...</div>
                    <div class="progress mt-3" style="width: 80%; max-width: 500px; height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-danger"
                             role="progressbar" style="width: 0%"
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div style="color: white; margin-top: 15px; font-size: 1rem;">
                        <span id="zeroProgress">0</span> of <span id="zeroTotal">${selectedProductIds.length}</span> products processed
                    </div>
                    <div id="zeroResults" style="margin-top: 20px; color: white; width: 80%; max-width: 600px; max-height: 300px; overflow-y: auto; background-color: rgba(0,0,0,0.5); padding: 15px; border-radius: 10px;">
                        <h5 style="text-align: center; margin-bottom: 15px; color: #f8f9fa;">Progress</h5>
                        <div id="zeroResultsList"></div>
                    </div>
                `;
                document.body.appendChild(progressOverlay);
                document.body.style.overflow = 'hidden'; // Prevent scrolling

                // Initialize progress tracking
                const progressBar = progressOverlay.querySelector('.progress-bar');
                const progressCount = progressOverlay.querySelector('#zeroProgress');
                const resultsList = progressOverlay.querySelector('#zeroResultsList');

                try {
                    // Call API to set inventory to zero
                    const response = await fetch('/shopify/products/api/set_inventory_to_zero', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            product_ids: selectedProductIds
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    // Update progress to 100%
                    progressBar.style.width = '100%';
                    progressBar.setAttribute('aria-valuenow', 100);
                    progressBar.textContent = '100%';
                    progressCount.textContent = selectedProductIds.length;

                    // Update results list
                    if (data.results && Array.isArray(data.results)) {
                        resultsList.innerHTML = data.results.map(result => {
                            if (result.status === 'success') {
                                return `
                                    <div class="mb-2 p-2 border-bottom border-secondary">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-success me-2">✓</span>
                                            <span>${result.product_title}</span>
                                        </div>
                                        <div class="small text-success ms-4">Successfully set ${result.variants_updated} variants to zero inventory</div>
                                    </div>
                                `;
                            } else if (result.status === 'partial') {
                                return `
                                    <div class="mb-2 p-2 border-bottom border-secondary">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-warning me-2">!</span>
                                            <span>${result.product_title}</span>
                                        </div>
                                        <div class="small text-warning ms-4">Partially updated: ${result.variants_updated} success, ${result.variants_failed} failed</div>
                                    </div>
                                `;
                            } else {
                                return `
                                    <div class="mb-2 p-2 border-bottom border-secondary">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-danger me-2">✗</span>
                                            <span>Product ID: ${result.product_id}</span>
                                        </div>
                                        <div class="small text-danger ms-4">Error: ${result.message || 'Unknown error'}</div>
                                    </div>
                                `;
                            }
                        }).join('');
                    }

                    // Update loading text with success styling
                    const loadingText = progressOverlay.querySelector('.loading-text');
                    loadingText.textContent = 'Inventory Update Complete!';
                    loadingText.style.color = '#28a745';
                    loadingText.style.fontSize = '1.5em';
                    loadingText.style.fontWeight = 'bold';

                    // Hide the spinner
                    const spinner = progressOverlay.querySelector('.spinner-border');
                    if (spinner) {
                        spinner.style.display = 'none';
                    }

                    // Add completion message and close button container
                    const completionContainer = document.createElement('div');
                    completionContainer.className = 'text-center mt-4 p-4';
                    completionContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                    completionContainer.style.borderRadius = '10px';
                    completionContainer.innerHTML = `
                        <div class="alert alert-success mb-4" style="font-size: 1.2em;">
                            <i class="fas fa-check-circle me-2" style="font-size: 1.5em;"></i>
                            <strong>Inventory Update Complete!</strong><br>
                            All selected products have been successfully processed.
                        </div>
                        <button type="button" class="btn btn-success btn-lg px-5 py-3" style="font-size: 1.2em;">
                            <i class="fas fa-times me-2"></i>Close and Refresh
                        </button>
                    `;

                    // Add click handler to the close button
                    const closeButton = completionContainer.querySelector('button');
                    closeButton.addEventListener('click', function() {
                        progressOverlay.remove();
                        document.body.style.overflow = '';
                        // Refresh product list to show updated inventory
                        fetchProducts(currentPage);
                    });

                    progressOverlay.appendChild(completionContainer);

                    // Add fade-in animation to completion container
                    completionContainer.style.opacity = '0';
                    completionContainer.style.transform = 'translateY(20px)';
                    completionContainer.style.transition = 'all 0.5s ease-in-out';

                    // Trigger animation after a brief delay
                    setTimeout(() => {
                        completionContainer.style.opacity = '1';
                        completionContainer.style.transform = 'translateY(0)';
                    }, 100);

                    // Show final notification
                    showNotification(
                        'Inventory Update Complete',
                        data.message,
                        'success'
                    );

                } catch (error) {
                    console.error("Error setting inventory to zero:", error);

                    // Update progress overlay with error message
                    progressOverlay.querySelector('.loading-text').textContent = 'Error Setting Inventory';
                    resultsList.innerHTML = `
                        <div class="alert alert-danger">
                            Error: ${error.message || 'Unknown error occurred'}
                        </div>
                    `;

                    // Add close button
                    const closeButton = document.createElement('button');
                    closeButton.className = 'btn btn-light mt-3';
                    closeButton.textContent = 'Close';
                    closeButton.addEventListener('click', function() {
                        progressOverlay.remove();
                        document.body.style.overflow = '';
                    });
                    progressOverlay.appendChild(closeButton);

                    showNotification(
                        'Error',
                        `Failed to set inventory to zero: ${error.message}`,
                        'error'
                    );
                }
            });

            // Remove modal from DOM when hidden
            confirmDialog.addEventListener('hidden.bs.modal', function() {
                confirmDialog.remove();
            });
        });

        // Show initial message in table
        productTable.querySelector('tbody').innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-white">
                    <p class="my-4">Please select filters or enter a search term to view products</p>
                </td>
            </tr>
        `;

        // Add event listener for search input
        searchTermInput.addEventListener('input', debounceFetch(() => {
            if (searchTermInput.value.trim().length > 0) {
                fetchProducts(1);
            }
        }, 300));

        // Add event listener to product modal
        productModal.addEventListener('hidden.bs.modal', function () {
            hideLoading();
            removeLoadingOverlay();
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            // Refresh the product list with current filters
            fetchProducts(currentPage);
        });

        // Add event delegation for "View More" buttons and expand/collapse buttons
        document.getElementById('productTable').addEventListener('click', function(event) {
            // Handle expand/collapse button clicks
            if (event.target.classList.contains('expand-collapse-btn') ||
                event.target.closest('.expand-collapse-btn')) {
                const button = event.target.classList.contains('expand-collapse-btn') ?
                    event.target : event.target.closest('.expand-collapse-btn');
                const productId = button.getAttribute('data-product-id');
                toggleProductVariants(button, productId);
                return;
            }

            // Handle quantity plus/minus button clicks
            if (event.target.classList.contains('quantity-btn') ||
                event.target.closest('.quantity-btn')) {
                const button = event.target.classList.contains('quantity-btn') ?
                    event.target : event.target.closest('.quantity-btn');
                const variantId = button.getAttribute('data-variant-id');
                const action = button.getAttribute('data-action');
                const inputElement = document.querySelector(`.variant-quantity-input[data-variant-id="${variantId}"]`);

                let currentValue = parseInt(inputElement.value) || 0;

                if (action === 'increase') {
                    currentValue += 1;
                } else if (action === 'decrease' && currentValue > 0) {
                    currentValue -= 1;
                }

                inputElement.value = currentValue;

                // Highlight the save button to indicate changes
                const saveButton = document.querySelector(`.update-variant-btn[data-variant-id="${variantId}"]`);
                if (saveButton) {
                    const originalValue = parseInt(inputElement.getAttribute('data-original-value')) || 0;
                    if (currentValue !== originalValue) {
                        saveButton.classList.add('btn-warning');
                        saveButton.classList.remove('btn-primary');
                    } else {
                        saveButton.classList.add('btn-primary');
                        saveButton.classList.remove('btn-warning');
                    }
                }

                return;
            }

            // Handle variant quantity update button clicks
            if (event.target.classList.contains('update-variant-btn') ||
                event.target.closest('.update-variant-btn')) {
                const button = event.target.classList.contains('update-variant-btn') ?
                    event.target : event.target.closest('.update-variant-btn');
                const productId = button.getAttribute('data-product-id');
                const variantId = button.getAttribute('data-variant-id');
                const inputElement = document.querySelector(`.variant-quantity-input[data-variant-id="${variantId}"]`);
                const quantity = parseInt(inputElement.value);

                if (!isNaN(quantity) && quantity >= 0) {
                    updateVariantQuantity(productId, variantId, quantity, button);
                }
                return;
            }

            // Variant creation functionality removed as requested

            // Handle other button clicks
            const productId = event.target.getAttribute('data-product-id');

            if (event.target.classList.contains('view-more-btn')) {
                openProductModal(productId);
            } else if (event.target.classList.contains('view-summary-btn')) {
                showSummary(productId);
            } else if (event.target.classList.contains('repair-image-btn')) {
                repairImage(productId);
            } else if (event.target.classList.contains('standardize-btn')) {
                standardizeProduct(productId);
            }
        });

        // Add event listeners for Expand All and Collapse All buttons
        document.getElementById('expandAllBtn').addEventListener('click', function() {
            expandAllProducts();
        });

        document.getElementById('collapseAllBtn').addEventListener('click', function() {
            collapseAllProducts();
        });

        // Function to standardize a single product
        async function standardizeProduct(productId) {
            try {
                // Show loading overlay
                const standardizeOverlay = document.createElement('div');
                standardizeOverlay.className = 'repricing-overlay';
                standardizeOverlay.innerHTML = `
                    <div class="spinner-border text-light" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="loading-text">Analyzing product title and tags...</div>
                `;
                document.body.appendChild(standardizeOverlay);

                // Call the analyze_product API endpoint
                const response = await fetch('/shopify/standardizer/api/analyze_product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        product_id: productId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    if (data.has_discrepancies) {
                        // Format discrepancies for display
                        let discrepanciesHtml = '<div class="mt-3">';

                        for (const [field, values] of Object.entries(data.discrepancies)) {
                            discrepanciesHtml += `
                                <div class="mb-3">
                                    <strong>${field.toUpperCase()}:</strong>
                                    <div class="text-danger mb-1">Current: ${values.existing}</div>
                                    <div class="text-success">Suggested: ${values.simulated}</div>
                                </div>
                            `;
                        }

                        discrepanciesHtml += '</div>';

                        // Create a notification with an Apply Changes button
                        const notificationContainer = document.getElementById('notificationContainer') || createNotificationContainer();
                        const notification = document.createElement('div');
                        notification.className = `toast bg-warning text-white`;
                        notification.setAttribute('role', 'alert');
                        notification.setAttribute('aria-live', 'assertive');
                        notification.setAttribute('aria-atomic', 'true');

                        notification.innerHTML = `
                            <div class="toast-header bg-warning text-white">
                                <strong class="me-auto">Standardization Analysis</strong>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                            <div class="toast-body">
                                <div>
                                    <p>Found discrepancies in <strong>${data.product_title}</strong>:</p>
                                    ${discrepanciesHtml}
                                    <button id="apply-standardization-${productId}" class="btn btn-success mt-2">Apply These Changes</button>
                                </div>
                            </div>
                        `;

                        notificationContainer.appendChild(notification);
                        const bsToast = new bootstrap.Toast(notification, { autohide: false });
                        bsToast.show();

                        // Add event listener to the Apply Changes button
                        document.getElementById(`apply-standardization-${productId}`).addEventListener('click', async function() {
                            try {
                                // Disable the button and show loading state
                                this.disabled = true;
                                this.innerHTML = `
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    Applying...
                                `;

                                // Prepare the update data
                                const updateData = {};

                                // Add title if it has a discrepancy
                                if (data.discrepancies.title) {
                                    updateData.title = data.discrepancies.title.simulated;
                                }

                                // Add tags if they have a discrepancy
                                if (data.discrepancies.tags) {
                                    updateData.tags = data.discrepancies.tags.simulated;
                                }

                                // Call the API to update the product
                                const response = await fetch('/shopify/standardizer/api/approve', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({
                                        product_id: productId,
                                        update_data: updateData
                                    })
                                });

                                const result = await response.json();

                                // Close the current notification
                                bsToast.hide();

                                // Show success or error notification
                                if (response.ok && result.success) {
                                    showNotification(
                                        'Standardization Applied',
                                        `Successfully applied standardization to "${data.product_title}"`,
                                        'success'
                                    );

                                    // Refresh the product list to show updated data
                                    fetchProducts(currentPage);
                                } else {
                                    showNotification(
                                        'Standardization Failed',
                                        `Failed to apply standardization: ${result.error || 'Unknown error'}`,
                                        'error'
                                    );
                                }
                            } catch (error) {
                                console.error('Error applying standardization:', error);

                                // Close the current notification
                                bsToast.hide();

                                showNotification(
                                    'Standardization Error',
                                    `An error occurred: ${error.message}`,
                                    'error'
                                );
                            }
                        });
                    } else {
                        showNotification(
                            "Standardization Analysis",
                            `<p>No discrepancies found in <strong>${data.product_title}</strong>.</p>
                             <p>The product title and tags already follow the standardized format.</p>`,
                            "success"
                        );
                    }
                } else {
                    throw new Error(data.error || 'Unknown error during analysis');
                }

            } catch (error) {
                console.error("Error standardizing product:", error);

                showNotification(
                    "Standardization Error",
                    `Failed to standardize product: ${error.message}. Please try again or check the logs.`,
                    "error"
                );
            } finally {
                // Remove loading overlay
                const standardizeOverlay = document.querySelector('.repricing-overlay');
                if (standardizeOverlay) {
                    standardizeOverlay.remove();
                }
            }
        }

        // Function to repair image for a product
        async function repairImage(productId) {
            try {
                // Show loading overlay
                const repairOverlay = document.createElement('div');
                repairOverlay.className = 'repricing-overlay';
                repairOverlay.innerHTML = `
                    <div class="spinner-border text-light" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="loading-text">Repairing image...</div>
                `;
                document.body.appendChild(repairOverlay);

                // Call the repair-image API
                const response = await fetch('/shopify/bulk-edit/api/repair-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        product_id: productId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    // Update the product image in the table
                    const productRow = document.querySelector(`tr[data-product-id="${productId}"]`);
                    if (productRow) {
                        const imgElement = productRow.querySelector('img');
                        if (imgElement && data.image_url) {
                            imgElement.src = data.image_url;
                        }
                    }

                    showNotification(
                        "Image Repair Successful",
                        `The image has been repaired and pushed to Shopify.`,
                        "success"
                    );
                } else {
                    throw new Error(data.message || 'Failed to repair image');
                }
            } catch (error) {
                console.error("Error repairing image:", error);
                showNotification(
                    "Error",
                    `Failed to repair image: ${error.message}`,
                    "error"
                );
            } finally {
                // Remove loading overlay
                const repairOverlay = document.querySelector('.repricing-overlay');
                if (repairOverlay) {
                    repairOverlay.remove();
                }
            }
        }

        async function showSummary(productId) {
            try {
                const response = await fetch(`/shopify/products/api/product/${productId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const product = await response.json();
                if (product.summary) {
                    const summaryContent = document.getElementById('summaryContent');

                    // Format the summary content
                    let formattedSummary = product.summary
                        // Add section headers
                        .replace(/\*{50,}/g, '<div class="section-header">$&</div>')
                        // Highlight price changes
                        .replace(/(\d+\.\d{2}) → (\d+\.\d{2})/g, (match, oldPrice, newPrice) => {
                            const priceClass = parseFloat(newPrice) >= parseFloat(oldPrice) ? 'price-change' : 'price-decrease';
                            return `<span class="${priceClass}">${oldPrice} → ${newPrice}</span>`;
                        })
                        // Highlight important values
                        .replace(/(SKU ID:|Market Price:|Low Price:|Mid Price:|High Price:|Starting price:|Final price:|Change:) ([^\n]+)/g, '$1 <span class="highlight">$2</span>')
                        // Highlight product details
                        .replace(/(Product:|User:|Currency:|Exchange Rate:) ([^\n]+)/g, '<strong>$1</strong> <span class="highlight">$2</span>')
                        // Highlight section titles
                        .replace(/^(STARTING POINT|TCGPLAYER PRICES|PRICE SELECTION|PRICE MODIFIERS|BASE PRICE|CONDITION STEPPING|FINAL RESULT)$/gm, '<div class="section-title">$1</div>')
                        // Highlight variant titles
                        .replace(/^VARIANT: ([^\n]+)$/gm, '<div class="variant-title">VARIANT: <span class="highlight">$1</span></div>')
                        // Wrap calculation steps in styled divs
                        .replace(/^\d+\.\s+([^\n]+)$/gm, '<div class="calculation-step"><span class="step-number">$&</span></div>')
                        // Style price blocks
                        .replace(/^(\s+)(Starting price:|Final price:|Change:)([^\n]+)$/gm, '<div class="price-block">$2<span class="price-info">$3</span></div>')
                        // Convert line breaks to HTML
                        .replace(/\n/g, '<br>');

                    summaryContent.innerHTML = formattedSummary;

                    const summaryModal = new bootstrap.Modal(document.getElementById('summaryModal'));
                    summaryModal.show();
                } else {
                    showNotification("No Summary", "No price calculation summary available for this product.", "info");
                }
            } catch (error) {
                console.error("Error fetching summary:", error);
                showNotification("Error", "Failed to load summary. Please try again.", "error");
            }
        }

        // Initial fetch for vendors
        try {
            await fetchVendors();
        } catch (error) {
            console.error("Error during initial vendors fetch:", error);
            console.log("Failed to load vendors. Please refresh the page.");
        }

        // Reprice button event listener is now handled in the main event listener section

        searchTermInput.addEventListener('input', debounceFetch(() => fetchProducts(1), 300));

        // Ensure the inStockOnly filter is refreshed immediately when the toggle changes
        inStockOnlyToggle.addEventListener('change', function() {
            console.log("In Stock Only toggle changed:", this.checked);
            // Reset to first page and clear cached data when toggling
            currentPage = 1;
            allProducts = []; // Clear cached products to force fresh fetch
            hasMoreProducts = true; // Reset pagination state
            // Force immediate fetch (no debounce) when toggling this filter
            fetchProducts(1);
        });

        // Add event listener for reprice by SKU button
        document.getElementById('repriceBySKUButton').addEventListener('click', async function() {
            // Create and show the repricing overlay
            const repricingOverlay = document.createElement('div');
            repricingOverlay.className = 'repricing-overlay';
            repricingOverlay.innerHTML = `
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="loading-text">Calculating new prices using SKU data...</div>
            `;
            document.body.appendChild(repricingOverlay);

            try {
                const response = await fetch('/shopify/products/api/reprice_by_sku', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        productId: currentProductId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (data.message) {
                    // Refresh the product modal to show updated prices
                    await openProductModal(currentProductId);
                    // Update the product row in the main table
                    updateProductRow(currentProductId);

                    // Show the new values
                    if (data.price_changes && data.price_changes.length > 0) {
                        const priceChangesHtml = data.price_changes.map(change =>
                            `<p>${change.variant_title}: ${getCurrencySymbol(userCurrency)}${change.old_price} → ${getCurrencySymbol(userCurrency)}${change.new_price}</p>`
                        ).join('');

                        showNotification("SKU Price Calculation Complete", `
                            <div>
                                <h6>New Prices:</h6>
                                ${priceChangesHtml}
                                <p class="mt-2"><small>Use "Sync to Shopify" to apply these changes</small></p>
                            </div>
                        `, "success");
                    } else {
                        showNotification("SKU Price Calculation Complete", "No price changes were necessary.", "info");
                    }
                } else {
                    throw new Error('No message received from server');
                }
            } catch (error) {
                console.error("Error during SKU price calculation:", error);
                showNotification("Calculation Error", `An error occurred while calculating prices: ${error.message}. Please try again or contact support if the issue persists.`, "error");
            } finally {
                repricingOverlay.remove();
            }
        });

            // Add event listener for delete button
            const deleteButton = document.getElementById('deleteButton');
            if (deleteButton) {
                deleteButton.onclick = async function() {
                    try {
                        // Use the showConfirmDialog function to ask for confirmation
                        const confirmed = await showConfirmDialog(
                            'Delete Product',
                            'Are you sure you want to delete this product? This will first remove it from Shopify, then from your local database. This action cannot be undone.'
                        );

                        if (confirmed) {
                            showLoading();
                            try {
                                const response = await fetch('/shopify/products/api/delete_product', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({ productId: currentProductId })
                                });

                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }

                                const data = await response.json();

                                if (data.message) {
                                    showNotification("Product Deleted", `${data.message}<br><strong>Shopify Response:</strong><br>${data.shopify_response}`, "success");
                                    // Close the modal
                                    bootstrap.Modal.getInstance(document.getElementById('productModal')).hide();
                                    // Refresh the product list
                                    fetchProducts(currentPage);
                                } else {
                                    throw new Error('No message received from server');
                                }
                            } catch (error) {
                                console.error("Error during product deletion:", error);
                                showNotification("Deletion Error", `An error occurred during product deletion: ${error.message}. Please try again or contact support if the issue persists.`, "error");
                            } finally {
                                hideLoading();
                            }
                        }
                    } catch (error) {
                        console.error("Error in delete button handler:", error);
                        showNotification("Error", `An error occurred: ${error.message}`, "error");
                    }
                };
            }

            // Manual reprice button functionality removed

        // Direct event listener for View History button is now added in openProductModal function

        // Direct event listener for View History button is now added in openProductModal function
    });

    // Function to toggle product variants visibility
    async function toggleProductVariants(button, productId) {
        const isExpanded = button.getAttribute('aria-expanded') === 'true';
        const variantsRow = document.querySelector(`.variants-row[data-product-id="${productId}"]`);

        if (isExpanded) {
            // Collapse the variants
            button.setAttribute('aria-expanded', 'false');
            button.innerHTML = '<i class="fas fa-plus"></i>';
            variantsRow.style.display = 'none';
        } else {
            // Expand and load the variants
            button.setAttribute('aria-expanded', 'true');
            button.innerHTML = '<i class="fas fa-minus"></i>';
            variantsRow.style.display = 'table-row';

            // Check if variants are already loaded
            const variantsContainer = variantsRow.querySelector('.variants-container');
            if (variantsContainer.querySelector('.variant-card')) {
                // Variants already loaded, no need to fetch again
                return;
            }

            // Load variants
            await loadProductVariants(productId, variantsContainer);
        }
    }

    // Function to load product variants
    async function loadProductVariants(productId, container) {
        try {
            const response = await fetch(`/shopify/products/api/product/${productId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const product = await response.json();

            if (!product.variants || product.variants.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No variants found for this product.</div>';
                return;
            }

            // Group variants by print type and condition
            const variantsByType = {};

            // Define condition mappings for better matching
            const conditionMappings = {
                // NM variants
                'NM': 'NM',
                'NEAR MINT': 'NM',
                'NEARMINT': 'NM',
                'NEAR-MINT': 'NM',
                'MINT': 'NM',
                // LP variants
                'LP': 'LP',
                'LIGHTLY PLAYED': 'LP',
                'LIGHTLYPLAYED': 'LP',
                'LIGHTLY-PLAYED': 'LP',
                'LIGHT PLAYED': 'LP',
                'LIGHT-PLAYED': 'LP',
                // MP variants
                'MP': 'MP',
                'MODERATELY PLAYED': 'MP',
                'MODERATELYPLAYED': 'MP',
                'MODERATELY-PLAYED': 'MP',
                'MODERATE PLAYED': 'MP',
                'MODERATE-PLAYED': 'MP',
                // HP variants
                'HP': 'HP',
                'HEAVILY PLAYED': 'HP',
                'HEAVILYPLAYED': 'HP',
                'HEAVILY-PLAYED': 'HP',
                'HEAVY PLAYED': 'HP',
                'HEAVY-PLAYED': 'HP',
                // DM variants
                'DM': 'DM',
                'DAMAGED': 'DM',
                'DMG': 'DM'
            };

            // Define print type mappings
            const printTypeMappings = {
                'NORMAL': 'Normal',
                'REGULAR': 'Normal',
                'NON-FOIL': 'Normal',
                'NONFOIL': 'Normal',
                'NON FOIL': 'Normal',
                'STANDARD': 'Normal',
                'FOIL': 'Foil',
                'ETCHED': 'Etched Foil',
                'ETCHED FOIL': 'Etched Foil',
                'ETCHED-FOIL': 'Etched Foil',
                'EXTENDED': 'Extended Art',
                'EXTENDED ART': 'Extended Art',
                'EXTENDED-ART': 'Extended Art',
                'BORDERLESS': 'Borderless',
                'SHOWCASE': 'Showcase',
                'ALTERNATE': 'Alternate Art',
                'ALTERNATE ART': 'Alternate Art',
                'ALTERNATE-ART': 'Alternate Art'
            };

            // No default print types - only show existing variants

            // Debug information
            console.log("Processing variants:", product.variants);

            // Process each variant
            product.variants.forEach(variant => {
                // Extract print type and condition from variant title
                const title = variant.title || '';
                console.log("Processing variant title:", title);

                // Try different parsing strategies
                let printType = 'Normal';
                let condition = 'NM';

                // Strategy 1: Split by " - " (e.g., "Normal - NM")
                const dashParts = title.split(' - ');
                if (dashParts.length >= 2) {
                    printType = dashParts[0].trim();
                    condition = dashParts[1].trim();
                }
                // Strategy 2: Split by space and check if last word is a condition (e.g., "Normal NM")
                else {
                    const spaceParts = title.split(' ');
                    const lastWord = spaceParts[spaceParts.length - 1];

                    // Check if the last word is a known condition
                    if (conditionMappings[lastWord.toUpperCase()]) {
                        condition = lastWord;
                        printType = spaceParts.slice(0, -1).join(' ') || 'Normal';
                    }
                    // Strategy 3: Look for condition keywords anywhere in the title
                    else {
                        for (const [keyword, mappedCondition] of Object.entries(conditionMappings)) {
                            if (title.toUpperCase().includes(keyword)) {
                                condition = mappedCondition;
                                // Remove the condition from the title to get the print type
                                const conditionRegex = new RegExp(keyword, 'i');
                                printType = title.replace(conditionRegex, '').trim() || 'Normal';
                                break;
                            }
                        }
                    }
                }

                // Normalize print type and condition using mappings
                const normalizedPrintType = printTypeMappings[printType.toUpperCase()] || printType;
                const normalizedCondition = conditionMappings[condition.toUpperCase()] || 'NM';

                console.log(`Parsed: "${title}" → Print Type: "${normalizedPrintType}", Condition: "${normalizedCondition}"`);

                // Initialize print type group if it doesn't exist
                if (!variantsByType[normalizedPrintType]) {
                    variantsByType[normalizedPrintType] = {
                        NM: null,
                        LP: null,
                        MP: null,
                        HP: null,
                        DM: null
                    };
                }

                // Store variant in the appropriate condition slot
                variantsByType[normalizedPrintType][normalizedCondition] = variant;
            });

            // Create container for variant rows
            const variantsContainer = document.createElement('div');
            variantsContainer.className = 'variants-by-type';

            // Create rows for each print type, but only if it has at least one variant
            Object.keys(variantsByType).forEach(printType => {
                // Check if this print type has any variants
                const hasVariants = Object.values(variantsByType[printType]).some(variant => variant !== null);

                // Skip this print type if it has no variants
                if (!hasVariants) {
                    return;
                }

                const typeRow = document.createElement('div');
                typeRow.className = `variant-type-row variant-type-${printType.toLowerCase()}`;

                // Create title for the print type
                const typeTitle = document.createElement('div');
                typeTitle.className = 'variant-type-title';
                typeTitle.textContent = printType;
                typeRow.appendChild(typeTitle);

                // Create container for conditions
                const conditionsContainer = document.createElement('div');
                conditionsContainer.className = 'variant-conditions';

                // Add each condition column
                ['NM', 'LP', 'MP', 'HP', 'DM'].forEach(condition => {
                    const variant = variantsByType[printType][condition];
                    const conditionCol = document.createElement('div');
                    conditionCol.className = `variant-condition variant-condition-${condition.toLowerCase()}`;

                    // Create condition title
                    const conditionTitle = document.createElement('div');
                    conditionTitle.className = 'variant-condition-title';
                    conditionTitle.textContent = condition;
                    conditionCol.appendChild(conditionTitle);

                    if (variant) {
                        // Create input group for quantity
                        const inputGroup = document.createElement('div');
                        inputGroup.className = 'd-flex align-items-center';

                        // Create quantity control group
                        const quantityControlGroup = document.createElement('div');
                        quantityControlGroup.className = 'quantity-control-group';

                        // Create minus button
                        const minusButton = document.createElement('button');
                        minusButton.type = 'button';
                        minusButton.className = 'quantity-btn minus';
                        minusButton.innerHTML = '<i class="fas fa-minus"></i>';
                        minusButton.setAttribute('data-variant-id', variant.id);
                        minusButton.setAttribute('data-action', 'decrease');

                        // Create quantity input for existing variant
                        const quantityInput = document.createElement('input');
                        quantityInput.type = 'number';
                        quantityInput.className = 'variant-quantity-input';
                        quantityInput.value = variant.inventory_quantity || 0;
                        quantityInput.min = 0;
                        quantityInput.setAttribute('data-variant-id', variant.id);
                        quantityInput.setAttribute('data-original-value', variant.inventory_quantity || 0);

                        // Create plus button
                        const plusButton = document.createElement('button');
                        plusButton.type = 'button';
                        plusButton.className = 'quantity-btn plus';
                        plusButton.innerHTML = '<i class="fas fa-plus"></i>';
                        plusButton.setAttribute('data-variant-id', variant.id);
                        plusButton.setAttribute('data-action', 'increase');

                        // Add quantity elements to control group
                        quantityControlGroup.appendChild(minusButton);
                        quantityControlGroup.appendChild(quantityInput);
                        quantityControlGroup.appendChild(plusButton);

                        // Create save button
                        const saveButton = document.createElement('button');
                        saveButton.className = 'btn btn-primary btn-sm update-variant-btn ms-2';
                        saveButton.textContent = 'Save';
                        saveButton.setAttribute('data-product-id', productId);
                        saveButton.setAttribute('data-variant-id', variant.id);

                        // Create status indicator
                        const statusIndicator = document.createElement('div');
                        statusIndicator.className = 'variant-update-status ms-1';
                        statusIndicator.setAttribute('data-variant-id', variant.id);

                        // Add elements to input group
                        inputGroup.appendChild(quantityControlGroup);
                        inputGroup.appendChild(saveButton);
                        inputGroup.appendChild(statusIndicator);

                        // Add input group to condition column
                        conditionCol.appendChild(inputGroup);

                        // Add small price indicator
                        const priceIndicator = document.createElement('div');
                        priceIndicator.className = 'small text-muted mt-1';
                        priceIndicator.textContent = `$${parseFloat(variant.price).toFixed(2)}`;
                        conditionCol.appendChild(priceIndicator);

                        // Add condition column to conditions container
                        conditionsContainer.appendChild(conditionCol);
                    }
                    // Skip this condition if variant doesn't exist (don't add to container)
                });

                // Add conditions container to type row
                typeRow.appendChild(conditionsContainer);

                // Add type row to variants container
                variantsContainer.appendChild(typeRow);
            });

            // Clear loading indicator and append the variants container
            container.innerHTML = '';
            container.appendChild(variantsContainer);

            // Apply current condition filter
            applyVariantFilters();

        } catch (error) {
            console.error("Error loading variants:", error);
            container.innerHTML = `<div class="alert alert-danger">Error loading variants: ${error.message}</div>`;
        }
    }

    // Function to create a new variant - removed as requested

    // Function to update variant quantity
    async function updateVariantQuantity(productId, variantId, quantity, button) {
        console.log(`Updating variant: Product ID=${productId}, Variant ID=${variantId}, New Quantity=${quantity}`);

        const statusElement = document.querySelector(`.variant-update-status[data-variant-id="${variantId}"]`);
        const inputElement = document.querySelector(`.variant-quantity-input[data-variant-id="${variantId}"]`);

        if (!statusElement) {
            console.error(`Status element not found for variant ID ${variantId}`);
            showNotification('Error', 'Could not find status element for this variant', 'danger');
            return;
        }

        if (!inputElement) {
            console.error(`Input element not found for variant ID ${variantId}`);
            showNotification('Error', 'Could not find input element for this variant', 'danger');
            return;
        }

        const originalValue = parseInt(inputElement.getAttribute('data-original-value'));
        console.log(`Original value: ${originalValue}, New value: ${quantity}`);

        // If quantity hasn't changed, do nothing
        if (quantity === originalValue) {
            console.log('Quantity unchanged, no update needed');
            statusElement.innerHTML = '<i class="fas fa-check text-success"></i>';
            setTimeout(() => { statusElement.innerHTML = ''; }, 2000);
            return;
        }

        // Show loading spinner
        statusElement.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
        button.disabled = true;

        try {
            // Get the current price from the price indicator
            const priceText = button.closest('.variant-condition').querySelector('.small.text-muted').textContent;
            const priceMatch = priceText.match(/\$([0-9.]+)/);
            const price = priceMatch ? parseFloat(priceMatch[1]) : 0;

            console.log(`Extracted price: ${price} from text: "${priceText}"`);

            // Prepare request data
            const requestData = {
                productId: productId,
                variantId: variantId,
                price: price,
                quantity: quantity
            };

            console.log('Sending API request with data:', requestData);

            // Call the API to update the variant
            const response = await fetch('/shopify/products/api/update_variant', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            console.log(`API response status: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                const errorData = await response.json();
                console.error('API error response:', errorData);
                throw new Error(errorData.error || 'Failed to update variant');
            }

            const data = await response.json();
            console.log('API success response:', data);

            // Update the input's data-original-value attribute
            inputElement.setAttribute('data-original-value', quantity);

            // Show success icon
            statusElement.innerHTML = '<i class="fas fa-check text-success"></i>';

            // Update the total quantity in the main product row
            updateProductTotalQuantity(productId);

            // Show notification
            showNotification('Success', 'Variant quantity updated successfully', 'success');

        } catch (error) {
            console.error("Error updating variant:", error);
            statusElement.innerHTML = '<i class="fas fa-times text-danger"></i>';
            showNotification('Error', `Failed to update variant: ${error.message}`, 'danger');
        } finally {
            button.disabled = false;

            // Clear status after a delay
            setTimeout(() => {
                if (statusElement.querySelector('.fa-check')) {
                    statusElement.innerHTML = '';
                }
            }, 3000);
        }
    }

    // Function to update the total quantity in the main product row
    function updateProductTotalQuantity(productId) {
        const variantsContainer = document.querySelector(`.variants-container[data-product-id="${productId}"]`);
        const quantityInputs = variantsContainer.querySelectorAll('.variant-quantity-input');

        let totalQuantity = 0;
        quantityInputs.forEach(input => {
            totalQuantity += parseInt(input.value) || 0;
        });

        // Update the total quantity cell in the main product row
        const productRow = document.querySelector(`.product-row[data-product-id="${productId}"]`);
        const quantityCell = productRow.querySelector('td:nth-child(6)');
        quantityCell.textContent = totalQuantity;

        // Also update the price range if needed
        // This would require fetching all variant prices and calculating min/max
        // For now, we'll leave the price range as is since we're focusing on quantities
    }

    // Function to expand all products
    async function expandAllProducts() {
        const expandButtons = document.querySelectorAll('.expand-collapse-btn[aria-expanded="false"]');

        // Show loading notification if there are many products to expand
        if (expandButtons.length > 5) {
            showNotification('Loading', `Expanding ${expandButtons.length} products...`, 'info');
        }

        // Process in batches to avoid overwhelming the browser
        const batchSize = 5;
        for (let i = 0; i < expandButtons.length; i += batchSize) {
            const batch = Array.from(expandButtons).slice(i, i + batchSize);
            await Promise.all(batch.map(button => {
                const productId = button.getAttribute('data-product-id');
                return toggleProductVariants(button, productId);
            }));

            // Small delay between batches
            if (i + batchSize < expandButtons.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
    }

    // Function to collapse all products
    function collapseAllProducts() {
        const collapseButtons = document.querySelectorAll('.expand-collapse-btn[aria-expanded="true"]');
        collapseButtons.forEach(button => {
            const productId = button.getAttribute('data-product-id');
            toggleProductVariants(button, productId);
        });
    }

    // Variable to track current condition filter
    let currentConditionFilter = 'all'; // 'all', 'nm', 'lp', 'mp', 'hp', or 'dm'

    // Function to apply variant filters
    function applyVariantFilters() {
        // Get all variant condition elements
        const variantConditions = document.querySelectorAll('.variant-condition');

        // Show/hide based on condition filter
        variantConditions.forEach(conditionEl => {
            // Get the condition from the class name
            const conditionClasses = Array.from(conditionEl.classList)
                .find(cls => cls.startsWith('variant-condition-'));

            if (!conditionClasses) return;

            const condition = conditionClasses.replace('variant-condition-', '').toUpperCase();

            // Show all conditions if filter is 'all', otherwise only show matching condition
            if (currentConditionFilter === 'all' || condition === currentConditionFilter) {
                conditionEl.style.display = '';
            } else {
                conditionEl.style.display = 'none';
            }
        });

        // Check if any conditions are visible in each type row
        document.querySelectorAll('.variant-type-row').forEach(typeRow => {
            const hasVisibleConditions = Array.from(typeRow.querySelectorAll('.variant-condition'))
                .some(condition => condition.style.display !== 'none');

            // Show/hide the type row based on whether it has visible conditions
            typeRow.style.display = hasVisibleConditions ? '' : 'none';
        });
    }

    // Add event listeners for filter buttons
    document.addEventListener('DOMContentLoaded', function() {
        // Condition filter buttons
        document.getElementById('filterAllConditionsBtn').addEventListener('click', function() {
            setActiveFilterButton(this);
            currentConditionFilter = 'all';
            applyVariantFilters();
        });

        document.getElementById('filterNMBtn').addEventListener('click', function() {
            setActiveFilterButton(this);
            currentConditionFilter = 'NM';
            applyVariantFilters();
        });

        document.getElementById('filterLPBtn').addEventListener('click', function() {
            setActiveFilterButton(this);
            currentConditionFilter = 'LP';
            applyVariantFilters();
        });

        document.getElementById('filterMPBtn').addEventListener('click', function() {
            setActiveFilterButton(this);
            currentConditionFilter = 'MP';
            applyVariantFilters();
        });

        document.getElementById('filterHPBtn').addEventListener('click', function() {
            setActiveFilterButton(this);
            currentConditionFilter = 'HP';
            applyVariantFilters();
        });

        document.getElementById('filterDMBtn').addEventListener('click', function() {
            setActiveFilterButton(this);
            currentConditionFilter = 'DM';
            applyVariantFilters();
        });
    });

    // Helper function to set active filter button
    function setActiveFilterButton(button) {
        // Remove active class from all buttons in the same group
        const buttons = button.closest('.btn-group').querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('active'));

        // Add active class to the clicked button
        button.classList.add('active');
    }
</script>
{% endblock %}
