from flask_login import current_user
import logging
from pymongo import MongoClient
from models.user_model import User
from services.currency_service import currency_service

logger = logging.getLogger(__name__)

# MongoDB connection for collections that still use PyMongo
client = MongoClient("mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin")
db = client.test
users_collection = db['user']

def get_exchange_rate(from_currency: str, to_currency: str) -> float:
    """Get current exchange rate using the currency service"""
    return currency_service.get_exchange_rate(from_currency, to_currency)

def get_user_currency(username):
    try:
        user = User.objects(username=username).first()
        if not user:
            return 'USD'
        
        currency = getattr(user, 'currency', None)
        
        if not currency:
            raw_user = users_collection.find_one({"username": username})
            if raw_user and 'currency' in raw_user:
                currency = raw_user['currency']
                user.currency = currency
                user.save()
        
        if not currency:
            user.currency = 'USD'
            user.save()
            currency = 'USD'
            
            users_collection.update_one(
                {"username": username},
                {"$set": {"currency": "USD"}},
                upsert=False
            )
        
        return currency
        
    except Exception as e:
        logger.error(f"Error getting user currency: {str(e)}")
        return 'USD'

def ensure_user_currency():
    try:
        user = User.objects(username=current_user.username).first()
        if not user:
            return
            
        current_currency = getattr(user, 'currency', None)
        
        if not current_currency:
            raw_user = users_collection.find_one({"username": current_user.username})
            if raw_user and 'currency' in raw_user:
                current_currency = raw_user['currency']
                user.currency = current_currency
                user.save()
        
        if not current_currency:
            user.currency = 'USD'
            user.save()
            
            users_collection.update_one(
                {"username": current_user.username},
                {"$set": {"currency": "USD"}},
                upsert=False
            )
    except Exception as e:
        logger.error(f"Error ensuring user currency: {str(e)}")

def get_low_price(variant, product, user, tcgplayer_key_collection, prices_collection):
    """Get lowest valid price for a variant based on user preferences"""
    try:
        import requests
        from utils.pricing_utils import determine_printing_type
        
        price_preference_order = getattr(user, 'price_preference_order', 
            ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])

        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            return None, None

        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        headers = {
            'Authorization': f'Bearer {tcgplayer_api_key}',
            'Accept': 'application/json',
        }

        product_id = product.get('productId')
        if not product_id:
            return None, None

        # If using SKU ID pricing, get the SKU price directly
        if getattr(user, 'use_skuid_pricing', False) and variant.get('sku'):
            sku_url = f"https://api.tcgplayer.com/pricing/sku/{variant['sku']}"
            sku_response = requests.get(sku_url, headers=headers)
            sku_response.raise_for_status()
            sku_data = sku_response.json().get('results', [])
            if sku_data:
                return sku_data[0], 'tcgplayer_sku'

        # Fall back to regular product pricing if SKU pricing fails or is disabled
        pricing_url = f"https://api.tcgplayer.com/pricing/product/{product_id}"
        pricing_response = requests.get(pricing_url, headers=headers)
        pricing_response.raise_for_status()
        pricing_data = pricing_response.json().get('results', [])

        if not pricing_data:
            return None, None

        valid_subtypes = [price_info['subTypeName'] for price_info in pricing_data 
                        if 'subTypeName' in price_info]

        printing_type = determine_printing_type(variant['title'], valid_subtypes)

        # First try exact match for printing type
        for price_info in pricing_data:
            if price_info['subTypeName'].lower() == printing_type.lower():
                # Check if all prices are null in TCGPlayer response
                all_null = all(price_info.get(price_type) is None for price_type in price_preference_order)
                if all_null:
                    # Try to get price from collection
                    collection_price = prices_collection.find_one({
                        'productId': product_id,
                        'subTypeName': price_info['subTypeName']
                    })
                    if collection_price:
                        # Convert collection price to same format as TCGPlayer pricing data
                        collection_pricing_data = {
                            'lowPrice': collection_price.get('lowPrice'),
                            'marketPrice': collection_price.get('marketPrice'),
                            'midPrice': collection_price.get('midPrice'),
                            'highPrice': collection_price.get('highPrice'),
                            'subTypeName': price_info['subTypeName']
                        }
                        return collection_pricing_data, 'collection'
                else:
                    return price_info, 'tcgplayer'

        # If cold foil not found, check for holofoil
        if 'cold foil' in printing_type.lower():
            for price_info in pricing_data:
                if 'holofoil' in price_info['subTypeName'].lower():
                    # Check if all prices are null in TCGPlayer response
                    all_null = all(price_info.get(price_type) is None for price_type in price_preference_order)
                    if all_null:
                        # Try to get price from collection
                        collection_price = prices_collection.find_one({
                            'productId': product_id,
                            'subTypeName': price_info['subTypeName']
                        })
                        if collection_price:
                            # Convert collection price to same format as TCGPlayer pricing data
                            collection_pricing_data = {
                                'lowPrice': collection_price.get('lowPrice'),
                                'marketPrice': collection_price.get('marketPrice'),
                                'midPrice': collection_price.get('midPrice'),
                                'highPrice': collection_price.get('highPrice'),
                                'subTypeName': price_info['subTypeName']
                            }
                            return collection_pricing_data, 'collection'
                    else:
                        return price_info, 'tcgplayer'

        # Finally fall back to normal if no other matches found
        if printing_type != 'Normal':
            for price_info in pricing_data:
                if price_info['subTypeName'].lower() == 'normal':
                    # Check if all prices are null in TCGPlayer response
                    all_null = all(price_info.get(price_type) is None for price_type in price_preference_order)
                    if all_null:
                        # Try to get price from collection
                        collection_price = prices_collection.find_one({
                            'productId': product_id,
                            'subTypeName': price_info['subTypeName']
                        })
                        if collection_price:
                            # Convert collection price to same format as TCGPlayer pricing data
                            collection_pricing_data = {
                                'lowPrice': collection_price.get('lowPrice'),
                                'marketPrice': collection_price.get('marketPrice'),
                                'midPrice': collection_price.get('midPrice'),
                                'highPrice': collection_price.get('highPrice'),
                                'subTypeName': price_info['subTypeName']
                            }
                            return collection_pricing_data, 'collection'
                    else:
                        return price_info, 'tcgplayer'

        return None, None

    except Exception as e:
        logger.error(f"Error getting low price: {str(e)}")
        return None, None
