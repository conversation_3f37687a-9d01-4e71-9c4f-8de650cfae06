from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required
from pymongo import MongoClient
from datetime import datetime, timedelta
import json
import os
import logging

# Set up logging
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = os.environ.get('MONGO_URI', 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin')
mongo_client = MongoClient(mongo_uri, maxPoolSize=50)
db = mongo_client[os.environ.get('MONGO_DBNAME', 'test')]
catalog_collection = db['catalog']

# Create blueprint
calendar_bp = Blueprint('calendar', __name__, url_prefix='/calendar')

@calendar_bp.route('/')
@login_required
def calendar_dashboard():
    """Display list view of upcoming expansion releases."""
    try:
        # Get query parameters for filtering
        selected_game = request.args.get('game', '')

        # Get current date for filtering future releases
        current_date = datetime.now()

        # Build aggregation pipeline for optimal performance
        pipeline = [
            # Match future releases with expansion names
            {
                '$match': {
                    'releasedOn': {'$gte': current_date.strftime('%Y-%m-%dT%H:%M:%S')},
                    'expansionName': {'$exists': True, '$ne': None, '$ne': ''},
                    'gameName': {'$exists': True, '$ne': None, '$ne': ''}
                }
            }
        ]

        # Add game filter if specified
        if selected_game:
            pipeline[0]['$match']['gameName'] = selected_game

        # Group by expansion and game to get unique releases with groupId
        pipeline.extend([
            {
                '$group': {
                    '_id': {
                        'gameName': '$gameName',
                        'expansionName': '$expansionName',
                        'groupId': '$groupId'
                    },
                    'releasedOn': {'$first': '$releasedOn'},
                    'name': {'$first': '$name'},
                    'groupId': {'$first': '$groupId'}
                }
            },
            # Sort by release date
            {
                '$sort': {'releasedOn': 1}
            },
            # Project final structure
            {
                '$project': {
                    '_id': 0,
                    'gameName': '$_id.gameName',
                    'expansionName': '$_id.expansionName',
                    'releasedOn': 1,
                    'name': 1,
                    'groupId': 1
                }
            }
        ])

        # Execute aggregation
        releases = list(catalog_collection.aggregate(pipeline))

        # Get all unique groupIds for counting
        group_ids = [release.get('groupId') for release in releases if release.get('groupId')]

        # Get catalog counts for all groupIds in one aggregation
        count_pipeline = [
            {
                '$match': {
                    'groupId': {'$in': group_ids}
                }
            },
            {
                '$group': {
                    '_id': '$groupId',
                    'count': {'$sum': 1}
                }
            }
        ]

        # Execute count aggregation
        count_results = list(catalog_collection.aggregate(count_pipeline))
        count_dict = {result['_id']: result['count'] for result in count_results}

        # Process releases for display
        processed_releases = []
        for release in releases:
            try:
                # Parse release date - handle different formats
                release_date_str = release.get('releasedOn', '')
                if not release_date_str:
                    continue

                # Try different date formats
                release_date = None
                date_formats = [
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%d',
                    '%Y-%m-%dT%H:%M:%S.%f'
                ]

                for date_format in date_formats:
                    try:
                        release_date = datetime.strptime(release_date_str, date_format)
                        break
                    except ValueError:
                        continue

                if not release_date:
                    logger.warning(f"Could not parse date: {release_date_str}")
                    continue

                # Add formatted date for display
                release['formatted_date'] = release_date.strftime('%d %B %Y')
                release['sort_date'] = release_date

                # Add catalog count from pre-computed dictionary
                group_id = release.get('groupId')
                release['catalog_count'] = count_dict.get(group_id, 0)

                processed_releases.append(release)

            except (ValueError, KeyError) as e:
                logger.warning(f"Error processing release: {e}")
                continue

        # Sort by date again after processing
        processed_releases.sort(key=lambda x: x['sort_date'])

        # Get unique games for filter dropdown using aggregation
        games_pipeline = [
            {
                '$match': {
                    'releasedOn': {'$gte': current_date.strftime('%Y-%m-%dT%H:%M:%S')},
                    'expansionName': {'$exists': True, '$ne': None, '$ne': ''},
                    'gameName': {'$exists': True, '$ne': None, '$ne': ''}
                }
            },
            {
                '$group': {
                    '_id': '$gameName'
                }
            },
            {
                '$sort': {'_id': 1}
            }
        ]

        games = [game['_id'] for game in catalog_collection.aggregate(games_pipeline) if game['_id']]

        return render_template(
            'calendar/releases.html',
            releases=processed_releases,
            games=games,
            selected_game=selected_game
        )

    except Exception as e:
        logger.error(f"Error in calendar dashboard: {str(e)}")
        return render_template('calendar/releases.html',
                             releases=[],
                             games=[],
                             selected_game='',
                             error="Error loading calendar data")

@calendar_bp.route('/api/releases')
@login_required
def get_releases():
    """API endpoint to get upcoming releases with filtering."""
    try:
        # Get query parameters
        game = request.args.get('game', '')
        start_date = request.args.get('start', '')
        end_date = request.args.get('end', '')
        
        # Build query
        query = {
            'expansionName': {'$exists': True, '$ne': None, '$ne': ''}
        }
        
        # Add date filters
        if start_date and end_date:
            query['releasedOn'] = {
                '$gte': start_date,
                '$lte': end_date
            }
        else:
            # Default to future releases
            current_date = datetime.now()
            query['releasedOn'] = {'$gte': current_date.strftime('%Y-%m-%dT%H:%M:%S')}
        
        # Add game filter
        if game:
            query['gameName'] = game
        
        # Get releases
        releases = list(catalog_collection.find(
            query,
            {
                'expansionName': 1,
                'gameName': 1,
                'releasedOn': 1,
                'name': 1,
                'image': 1,
                'gameAbbreviation': 1
            }
        ).sort('releasedOn', 1))
        
        # Group by expansion to avoid duplicates
        unique_releases = {}
        for release in releases:
            key = f"{release.get('gameName', 'Unknown')}_{release.get('expansionName', 'Unknown')}"
            if key not in unique_releases:
                unique_releases[key] = release
        
        # Format for calendar
        events = []
        for release in unique_releases.values():
            try:
                # Parse release date - handle different formats
                release_date_str = release.get('releasedOn', '')
                if not release_date_str:
                    continue

                # Try different date formats
                release_date = None
                date_formats = [
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%d',
                    '%Y-%m-%dT%H:%M:%S.%f'
                ]

                for date_format in date_formats:
                    try:
                        release_date = datetime.strptime(release_date_str, date_format)
                        break
                    except ValueError:
                        continue

                if not release_date:
                    continue

                event = {
                    'id': f"{release.get('gameName', 'Unknown')}_{release.get('expansionName', 'Unknown')}",
                    'title': f"{release.get('gameName', 'Unknown')}: {release.get('expansionName', 'Unknown')}",
                    'start': release_date.strftime('%Y-%m-%d'),
                    'allDay': True,
                    'backgroundColor': get_game_color(release.get('gameName', 'Unknown')),
                    'borderColor': get_game_color(release.get('gameName', 'Unknown')),
                    'textColor': '#ffffff',
                    'extendedProps': {
                        'gameName': release.get('gameName', 'Unknown'),
                        'expansionName': release.get('expansionName', 'Unknown'),
                        'image': release.get('image', ''),
                        'gameAbbreviation': release.get('gameAbbreviation', '')
                    }
                }
                events.append(event)
            except (ValueError, KeyError):
                continue
        
        return jsonify(events)
        
    except Exception as e:
        logger.error(f"Error in get_releases API: {str(e)}")
        return jsonify([])

def get_game_color(game_name):
    """Get a color for a game based on its name."""
    colors = {
        'Magic: The Gathering': '#FF6B35',
        'Pokemon': '#FFCC02',
        'Yu-Gi-Oh!': '#8E44AD',
        'Dragon Ball Super': '#E74C3C',
        'One Piece': '#3498DB',
        'Digimon': '#2ECC71',
        'Flesh and Blood': '#E67E22',
        'Lorcana': '#9B59B6'
    }
    return colors.get(game_name, '#6C757D')  # Default gray color
