import os
import logging
from pymongo import MongoClient, ReadPreference
from pymongo.server_api import Server<PERSON><PERSON>
from datetime import timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variable to store MongoDB client instance
_mongo_client_instance = None

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'b5f4d28e8f9c4a6c9d8f6a9d2c3a4b5e8f6a9d2c3a4b5e8f6a9d2c3a4b5e8f6a'
    MONGO_URI = os.environ.get('MONGO_URI', 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin')
    MONGO_DBNAME = os.environ.get('MONGO_DBNAME', 'test')
    MAILGUN_API_KEY = os.environ.get('MAILGUN_API_KEY', '**************************************************')
    MAILGUN_DOMAIN = os.environ.get('MAILGUN_DOMAIN', 'tcgsync.com')
    XIMILAR_API_KEY = os.environ.get('XIMILAR_API_KEY', '5247b7f9456e965d3dbeb13f35ac49633e632721')
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'app/static/uploads')
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY', '********************************************************************************************************************************************************************')

    # Stripe configuration
    STRIPE_PUBLIC_KEY = 'pk_live_51Mk2mKIh9K9cw3GroxSDcv6lPVCGPieOKxtPL2wEOalMBIjSngKbgzpnD55eJCWolNeFWYgRFVPBCDzeLlKohfh8004bflPSSM'
    STRIPE_SECRET_KEY = '***********************************************************************************************************'
    STRIPE_WEBHOOK_SECRET = 'whsec_sXstdZDqNN8yEV9fwF6fkd1ugFVEdYGP'  # Stripe webhook signing secret
    STRIPE_WEBHOOK_URL = 'https://login.tcgsync.com/stripe/webhook'

    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(days=1)  # Session expires after 1 day
    SESSION_COOKIE_SECURE = os.environ.get('FLASK_ENV') == 'production'  # Secure in production
    SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access to session cookie
    SESSION_COOKIE_SAMESITE = 'Lax'  # Protect against CSRF
    SESSION_TYPE = 'filesystem'  # Store sessions in filesystem
    REMEMBER_COOKIE_DURATION = timedelta(days=14)  # Remember me cookie duration
    REMEMBER_COOKIE_SECURE = os.environ.get('FLASK_ENV') == 'production'
    REMEMBER_COOKIE_HTTPONLY = True
    REMEMBER_COOKIE_SAMESITE = 'Lax'

    # MongoDB connection settings
    MONGO_CONNECTION_SETTINGS = {
        'server_api': ServerApi('1'),
        'serverSelectionTimeoutMS': 30000,
        'connectTimeoutMS': 20000,
        'read_preference': ReadPreference.PRIMARY_PREFERRED,
        'w': 'majority',  # Write concern
        'retryWrites': True
    }

    @staticmethod
    def get_mongo_client():
        """
        Get a MongoDB client with the configured connection settings.
        
        Returns:
            MongoClient: A configured MongoDB client instance
        """
        global _mongo_client_instance
        
        # Return existing client if already initialized
        if _mongo_client_instance is not None:
            return _mongo_client_instance
            
        try:
            client = MongoClient(
                Config.MONGO_URI,
                **Config.MONGO_CONNECTION_SETTINGS
            )
            
            # Force a connection to verify it works
            client.server_info()
            logger.info("Successfully connected to MongoDB cluster")
            
            # Store the client instance
            _mongo_client_instance = client
            return client
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB cluster: {str(e)}")
            raise

    MONGODB_SETTINGS = {
        'host': MONGO_URI,
        'db': MONGO_DBNAME
    }
