import logging
from pymongo import MongoClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('shopify_products.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
MONGO_URI = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
MONGO_POOL_SIZE = 200

# Initialize MongoDB client with connection pooling
mongo_client = MongoClient(
    MONGO_URI,
    maxPoolSize=MONGO_POOL_SIZE,
    waitQueueTimeoutMS=5000,
    connectTimeoutMS=5000,
    serverSelectionTimeoutMS=5000,
    retryWrites=True,
    w='majority'
)

# Get database instance
db = mongo_client['test']

# Collection references
shopify_collection = db['shProducts']
user_collection = db['user']
tcgplayer_key_collection = db['tcgplayerKey']
autopricer_collection = db['autopricerShopify']
prices_collection = db['prices']
staged_inventory_collection = db['staged_inventory']

# Create optimized indexes
shopify_collection.create_index([
    ("username", 1),
    ("product_type", 1),
    ("manualOverride", 1),
    ("productId", 1)
])

# Constants
EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"
BATCH_SIZE = 500
DEFAULT_MIN_PRICE_USD = 0.2

# Condition mapping
CONDITION_MAP = {
    'near mint': 'nm', 'near-mint': 'nm', 'nearmint': 'nm', 'nm': 'nm',
    'lightly played': 'lp', 'lightly-played': 'lp', 'lightlyplayed': 'lp', 'lp': 'lp',
    'moderately played': 'mp', 'moderately-played': 'mp', 'moderatelyplayed': 'mp', 'mp': 'mp',
    'heavily played': 'hp', 'heavily-played': 'hp', 'heavilyplayed': 'hp', 'hp': 'hp',
    'damaged': 'dm', 'dm': 'dm'
}

# Default settings
DEFAULT_SETTINGS = {
    'minPrice': 0.50,
    'price_point': 'Low Price',
    'price_rounding_enabled': False,
    'price_rounding_thresholds': [49, 99],
    'use_highest_price': False,
    'price_comparison_pairs': [],
    'price_modifiers': {},
    'price_preference_order': ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'],
    'game_minimum_prices': {},
    'advancedPricingRules': {},
    'customStepping': {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50},
    'tcg_trend_increasing': 0.0,
    'tcg_trend_decreasing': 0.0
}

# API endpoints
TCGPLAYER_PRICING_URL = "https://api.tcgplayer.com/pricing/product/{product_id}"
EXCHANGE_RATE_URL = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
