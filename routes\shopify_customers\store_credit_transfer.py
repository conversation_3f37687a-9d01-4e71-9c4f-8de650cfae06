from flask import request, jsonify
from flask_login import current_user, login_required
from datetime import datetime
import logging
import requests
from models.user_model import User
from models.customer_notes_model import CustomerNote

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_routes(bp, mongo_client):
    @bp.route('/shopify/customers/api/customer/<customer_id>/store-credit', methods=['GET'])
    @login_required
    def get_customer_store_credit(customer_id):
        """Get store credit balance for a customer"""
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            # Get Shopify credentials
            shopify_store_name = user.shopifyStoreName
            shopify_access_token = user.shopifyAccessToken

            # Format customer ID as a Shopify GraphQL ID if it's not already
            if not customer_id.startswith('gid://'):
                customer_gid = f"gid://shopify/Customer/{customer_id}"
            else:
                customer_gid = customer_id

            # GraphQL query to get store credit balance
            graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"

            # Query to get store credit balance
            query = """
            query getStoreCredit($customerId: ID!) {
              customer(id: $customerId) {
                storeCreditAccounts(first: 10) {
                  edges {
                    node {
                      id
                      balance {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
            """

            variables = {
                "customerId": customer_gid
            }

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            response = requests.post(
                graphql_endpoint,
                headers=headers,
                json={"query": query, "variables": variables}
            )

            if response.status_code != 200:
                logger.error(f"Error from Shopify API: {response.status_code} - {response.text}")
                return jsonify({"error": f"Error from Shopify API: {response.status_code}"}), 500

            data = response.json()

            # Check for GraphQL errors
            if 'errors' in data:
                error_message = data['errors'][0]['message']
                logger.error(f"GraphQL error: {error_message}")
                return jsonify({"error": f"GraphQL error: {error_message}"}), 400

            # Extract store credit accounts
            accounts = []
            if 'data' in data and 'customer' in data['data']:
                store_credit_accounts = data['data']['customer']['storeCreditAccounts']['edges']
                for account_edge in store_credit_accounts:
                    account = account_edge.get('node', {})
                    account_id = account.get('id')
                    balance = account.get('balance', {})
                    amount = balance.get('amount')
                    currency = balance.get('currencyCode')

                    if amount and currency:
                        accounts.append({
                            'id': account_id,
                            'balance': {
                                'amount': amount,
                                'currency': currency
                            }
                        })

            return jsonify({
                "success": True,
                "accounts": accounts
            })
        except Exception as e:
            logger.error(f"Error getting store credit: {str(e)}")
            return jsonify({"error": f"Error getting store credit: {str(e)}"}), 500

    @bp.route('/shopify/customers/api/search', methods=['GET'])
    @login_required
    def search_customers():
        """Search customers by name or email"""
        try:
            search_term = request.args.get('term', '').strip()
            if not search_term:
                return jsonify({"error": "Search term is required"}), 400

            # Create a case-insensitive regex pattern
            import re
            pattern = re.compile(f".*{re.escape(search_term)}.*", re.IGNORECASE)

            # Search by email, first_name, or last_name
            customers = list(mongo_client['test']['shCustomers'].find({
                "username": current_user.username,
                "$or": [
                    {"email": pattern},
                    {"first_name": pattern},
                    {"last_name": pattern}
                ]
            }).limit(10))

            # Convert ObjectId to string for JSON serialization and ensure id field is present
            for customer in customers:
                if '_id' in customer:
                    customer['_id'] = str(customer['_id'])

                # Make sure the id field is a string
                if 'id' in customer:
                    customer['id'] = str(customer['id'])

                # Log customer details for debugging
                logger.info(f"Found customer: {customer.get('first_name')} {customer.get('last_name')}, ID: {customer.get('id')}, _id: {customer.get('_id')}")

            return jsonify({"customers": customers})
        except Exception as e:
            logger.error(f"Error searching customers: {str(e)}")
            return jsonify({"error": f"Error searching customers: {str(e)}"}), 500

    @bp.route('/shopify/customers/api/store-credit/transfer', methods=['POST'])
    @login_required
    def transfer_store_credit():
        """Transfer store credit from one customer to another"""
        try:
            data = request.json
            if not data:
                return jsonify({"error": "No data provided"}), 400

            source_customer_id = data.get('source_customer_id')
            dest_customer_id = data.get('dest_customer_id')
            amount = float(data.get('amount', 0))
            staff_name = data.get('staff_name', '')
            note = data.get('note', '')

            if not source_customer_id or not dest_customer_id:
                return jsonify({"error": "Source and destination customer IDs are required"}), 400

            if not amount or amount <= 0:
                return jsonify({"error": "Amount must be greater than zero"}), 400

            if not staff_name:
                return jsonify({"error": "Staff name is required"}), 400

            if source_customer_id == dest_customer_id:
                return jsonify({"error": "Source and destination customers cannot be the same"}), 400

            # Get user's Shopify credentials
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            # Get user's currency
            currency = user.currency if hasattr(user, 'currency') and user.currency else 'USD'

            # Get Shopify credentials
            shopify_store_name = user.shopifyStoreName
            shopify_access_token = user.shopifyAccessToken

            # Format customer IDs as Shopify GraphQL IDs if they're not already
            if not source_customer_id.startswith('gid://'):
                source_customer_gid = f"gid://shopify/Customer/{source_customer_id}"
            else:
                source_customer_gid = source_customer_id
                source_customer_id = source_customer_id.split('/')[-1]

            if not dest_customer_id.startswith('gid://'):
                dest_customer_gid = f"gid://shopify/Customer/{dest_customer_id}"
            else:
                dest_customer_gid = dest_customer_id
                dest_customer_id = dest_customer_id.split('/')[-1]

            # Log the customer IDs for debugging
            logger.info(f"Looking for source customer with ID: {source_customer_id}")
            logger.info(f"Looking for destination customer with ID: {dest_customer_id}")

            # First, let's check what fields are available in the collection
            sample_customer = mongo_client['test']['shCustomers'].find_one({"username": current_user.username})
            if sample_customer:
                logger.info(f"Sample customer fields: {list(sample_customer.keys())}")

            # Get all customers for this user to debug
            all_customers = list(mongo_client['test']['shCustomers'].find(
                {"username": current_user.username}
            ).limit(5))

            logger.info(f"Found {len(all_customers)} customers for user {current_user.username}")
            for idx, cust in enumerate(all_customers[:2]):  # Log first 2 customers
                logger.info(f"Customer {idx+1}: ID={cust.get('id')}, Name={cust.get('first_name')} {cust.get('last_name')}")

            # Try a more flexible approach for source customer
            source_customer = None
            # First try the exact match
            source_customer = mongo_client['test']['shCustomers'].find_one({
                "username": current_user.username,
                "id": source_customer_id
            })

            # If not found, try as string
            if not source_customer:
                source_customer = mongo_client['test']['shCustomers'].find_one({
                    "username": current_user.username,
                    "id": str(source_customer_id)
                })

            # If still not found, try a regex match (in case of formatting differences)
            if not source_customer:
                import re
                source_id_pattern = re.compile(f".*{source_customer_id}.*", re.IGNORECASE)
                source_customer = mongo_client['test']['shCustomers'].find_one({
                    "username": current_user.username,
                    "id": source_id_pattern
                })

            # Same approach for destination customer
            dest_customer = None
            # First try the exact match
            dest_customer = mongo_client['test']['shCustomers'].find_one({
                "username": current_user.username,
                "id": dest_customer_id
            })

            # If not found, try as string
            if not dest_customer:
                dest_customer = mongo_client['test']['shCustomers'].find_one({
                    "username": current_user.username,
                    "id": str(dest_customer_id)
                })

            # If still not found, try a regex match
            if not dest_customer:
                import re
                dest_id_pattern = re.compile(f".*{dest_customer_id}.*", re.IGNORECASE)
                dest_customer = mongo_client['test']['shCustomers'].find_one({
                    "username": current_user.username,
                    "id": dest_id_pattern
                })

            # If customers are not found in the database, try to get them from Shopify directly
            if not source_customer or not dest_customer:
                logger.info("Customers not found in database, attempting to fetch from Shopify API")

                # Get Shopify API credentials
                shopify_store_name = user.shopifyStoreName
                shopify_access_token = user.shopifyAccessToken

                headers = {
                    'X-Shopify-Access-Token': shopify_access_token,
                    'Content-Type': 'application/json'
                }

                # If source customer not found, fetch from Shopify
                if not source_customer:
                    try:
                        # GraphQL query to get customer details
                        query = """
                        query {
                          customer(id: "%s") {
                            id
                            firstName
                            lastName
                            email
                          }
                        }
                        """ % (source_customer_gid)

                        graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"

                        response = requests.post(
                            graphql_endpoint,
                            headers=headers,
                            json={"query": query}
                        )

                        if response.status_code == 200:
                            data = response.json()
                            if 'data' in data and 'customer' in data['data'] and data['data']['customer']:
                                customer_data = data['data']['customer']

                                # Create a customer record
                                source_customer = {
                                    'id': source_customer_id,
                                    'first_name': customer_data.get('firstName', ''),
                                    'last_name': customer_data.get('lastName', ''),
                                    'email': customer_data.get('email', ''),
                                    'username': current_user.username
                                }

                                # Save to database for future use
                                mongo_client['test']['shCustomers'].insert_one(source_customer)
                                logger.info(f"Created source customer in database: {source_customer['first_name']} {source_customer['last_name']}")
                            else:
                                logger.error("Source customer not found in Shopify API response")
                                return jsonify({"error": "Source customer not found in Shopify"}), 404
                        else:
                            logger.error(f"Error fetching source customer from Shopify: {response.status_code}")
                            return jsonify({"error": "Error fetching source customer from Shopify"}), 500
                    except Exception as e:
                        logger.error(f"Exception fetching source customer: {str(e)}")
                        return jsonify({"error": f"Error fetching source customer: {str(e)}"}), 500

                # If destination customer not found, fetch from Shopify
                if not dest_customer:
                    try:
                        # GraphQL query to get customer details
                        query = """
                        query {
                          customer(id: "%s") {
                            id
                            firstName
                            lastName
                            email
                          }
                        }
                        """ % (dest_customer_gid)

                        graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"

                        response = requests.post(
                            graphql_endpoint,
                            headers=headers,
                            json={"query": query}
                        )

                        if response.status_code == 200:
                            data = response.json()
                            if 'data' in data and 'customer' in data['data'] and data['data']['customer']:
                                customer_data = data['data']['customer']

                                # Create a customer record
                                dest_customer = {
                                    'id': dest_customer_id,
                                    'first_name': customer_data.get('firstName', ''),
                                    'last_name': customer_data.get('lastName', ''),
                                    'email': customer_data.get('email', ''),
                                    'username': current_user.username
                                }

                                # Save to database for future use
                                mongo_client['test']['shCustomers'].insert_one(dest_customer)
                                logger.info(f"Created destination customer in database: {dest_customer['first_name']} {dest_customer['last_name']}")
                            else:
                                logger.error("Destination customer not found in Shopify API response")
                                return jsonify({"error": "Destination customer not found in Shopify"}), 404
                        else:
                            logger.error(f"Error fetching destination customer from Shopify: {response.status_code}")
                            return jsonify({"error": "Error fetching destination customer from Shopify"}), 500
                    except Exception as e:
                        logger.error(f"Exception fetching destination customer: {str(e)}")
                        return jsonify({"error": f"Error fetching destination customer: {str(e)}"}), 500

            # Final check to make sure we have both customers
            if not source_customer:
                return jsonify({"error": "Source customer not found"}), 404

            if not dest_customer:
                return jsonify({"error": "Destination customer not found"}), 404

            # Format customer names
            source_name = f"{source_customer.get('first_name', '')} {source_customer.get('last_name', '')}".strip() or "No name"
            dest_name = f"{dest_customer.get('first_name', '')} {dest_customer.get('last_name', '')}".strip() or "No name"

            # Step 1: Decrease store credit from source customer
            graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"

            # GraphQL mutation to decrease store credit
            debit_mutation = """
            mutation storeCreditAccountDebit($customerId: ID!, $amount: Decimal!, $currencyCode: CurrencyCode!) {
              storeCreditAccountDebit(
                id: $customerId,
                debitInput: {
                  debitAmount: {
                    amount: $amount
                    currencyCode: $currencyCode
                  }
                }
              ) {
                storeCreditAccountTransaction {
                  id
                  amount {
                    amount
                    currencyCode
                  }
                }
                userErrors {
                  field
                  message
                }
              }
            }
            """

            debit_variables = {
                "customerId": source_customer_gid,
                "amount": amount,
                "currencyCode": currency.upper()
            }

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            # Execute the debit mutation
            debit_response = requests.post(
                graphql_endpoint,
                headers=headers,
                json={"query": debit_mutation, "variables": debit_variables}
            )

            if debit_response.status_code != 200:
                logger.error(f"Error from Shopify API (debit): {debit_response.status_code} - {debit_response.text}")
                return jsonify({"error": f"Error from Shopify API: {debit_response.status_code}"}), 500

            debit_data = debit_response.json()

            # Check for GraphQL errors
            if 'errors' in debit_data:
                error_message = debit_data['errors'][0]['message']
                logger.error(f"GraphQL error (debit): {error_message}")
                return jsonify({"error": f"GraphQL error: {error_message}"}), 400

            # Check for user errors
            user_errors = debit_data.get('data', {}).get('storeCreditAccountDebit', {}).get('userErrors', [])
            if user_errors:
                error_message = user_errors[0]['message']
                logger.error(f"User error (debit): {error_message}")
                return jsonify({"error": f"Error: {error_message}"}), 400

            # Step 2: Increase store credit for destination customer
            credit_mutation = """
            mutation storeCreditAccountCredit($customerId: ID!, $amount: Decimal!, $currencyCode: CurrencyCode!) {
              storeCreditAccountCredit(
                id: $customerId,
                creditInput: {
                  creditAmount: {
                    amount: $amount
                    currencyCode: $currencyCode
                  }
                }
              ) {
                storeCreditAccountTransaction {
                  id
                  amount {
                    amount
                    currencyCode
                  }
                }
                userErrors {
                  field
                  message
                }
              }
            }
            """

            credit_variables = {
                "customerId": dest_customer_gid,
                "amount": amount,
                "currencyCode": currency.upper()
            }

            # Execute the credit mutation
            credit_response = requests.post(
                graphql_endpoint,
                headers=headers,
                json={"query": credit_mutation, "variables": credit_variables}
            )

            if credit_response.status_code != 200:
                logger.error(f"Error from Shopify API (credit): {credit_response.status_code} - {credit_response.text}")
                return jsonify({"error": f"Error from Shopify API: {credit_response.status_code}"}), 500

            credit_data = credit_response.json()

            # Check for GraphQL errors
            if 'errors' in credit_data:
                error_message = credit_data['errors'][0]['message']
                logger.error(f"GraphQL error (credit): {error_message}")
                return jsonify({"error": f"GraphQL error: {error_message}"}), 400

            # Check for user errors
            user_errors = credit_data.get('data', {}).get('storeCreditAccountCredit', {}).get('userErrors', [])
            if user_errors:
                error_message = user_errors[0]['message']
                logger.error(f"User error (credit): {error_message}")
                return jsonify({"error": f"Error: {error_message}"}), 400

            # Step 3: Get updated balances
            # Query to get store credit balance for source customer
            query = """
            query getStoreCredit($customerId: ID!) {
              customer(id: $customerId) {
                storeCreditAccounts(first: 10) {
                  edges {
                    node {
                      id
                      balance {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
            """

            # Get source customer balance
            source_variables = {
                "customerId": source_customer_gid
            }

            source_balance_response = requests.post(
                graphql_endpoint,
                headers=headers,
                json={"query": query, "variables": source_variables}
            )

            source_balance_data = source_balance_response.json()
            source_balance = 0

            if 'data' in source_balance_data and 'customer' in source_balance_data['data']:
                accounts = source_balance_data['data']['customer']['storeCreditAccounts']['edges']
                for account in accounts:
                    source_balance += float(account['node']['balance']['amount'])

            # Get destination customer balance
            dest_variables = {
                "customerId": dest_customer_gid
            }

            dest_balance_response = requests.post(
                graphql_endpoint,
                headers=headers,
                json={"query": query, "variables": dest_variables}
            )

            dest_balance_data = dest_balance_response.json()
            dest_balance = 0

            if 'data' in dest_balance_data and 'customer' in dest_balance_data['data']:
                accounts = dest_balance_data['data']['customer']['storeCreditAccounts']['edges']
                for account in accounts:
                    dest_balance += float(account['node']['balance']['amount'])

            # Step 4: Add notes to both customers
            transfer_note = f"Store credit transfer: {formatCurrency(amount, currency)}"
            if note:
                transfer_note += f"\nNote: {note}"

            source_note_text = f"Transferred {formatCurrency(amount, currency)} to {dest_name}\n{transfer_note}"
            dest_note_text = f"Received {formatCurrency(amount, currency)} from {source_name}\n{transfer_note}"

            # Add note to source customer
            source_customer_note = CustomerNote()
            source_customer_note.create_note(
                mongo_client['test'],
                source_customer_id,
                source_note_text,
                current_user.username,
                staff_name
            )

            # Add note to destination customer
            dest_customer_note = CustomerNote()
            dest_customer_note.create_note(
                mongo_client['test'],
                dest_customer_id,
                dest_note_text,
                current_user.username,
                staff_name
            )

            # Step 5: Record the transfer in the database
            transfer_record = {
                'source_customer_id': source_customer_id,
                'source_customer_name': source_name,
                'dest_customer_id': dest_customer_id,
                'dest_customer_name': dest_name,
                'amount': amount,
                'currency': currency.upper(),
                'staff_name': staff_name,
                'note': note,
                'username': current_user.username,
                'created_at': datetime.utcnow()
            }

            mongo_client['test']['storeCreditTransfers'].insert_one(transfer_record)

            # Return success response with updated balances
            return jsonify({
                "success": True,
                "message": "Store credit transfer completed successfully",
                "source_balance": source_balance,
                "dest_balance": dest_balance,
                "amount": amount,
                "currency": currency.upper()
            })
        except Exception as e:
            logger.error(f"Error transferring store credit: {str(e)}")
            return jsonify({"error": f"Error transferring store credit: {str(e)}"}), 500

def formatCurrency(amount, currency='USD'):
    """Format amount as currency string"""
    try:
        return f"{amount:.2f}"
    except:
        return "0.00"
