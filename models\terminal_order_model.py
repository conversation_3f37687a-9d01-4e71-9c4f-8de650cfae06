from mongoengine import Document, <PERSON><PERSON>ield, ListField, DictField, FloatField, DateTimeField
from datetime import datetime

class TerminalOrder(Document):
    username = StringField(required=True)  # Store the logged in username
    customerName = StringField(required=True)
    items = ListField(DictField(), required=True)  # Store item details as dictionaries
    total = FloatField(required=True)
    status = StringField(default='completed', choices=['pending', 'processing', 'completed', 'cancelled'])
    created_at = DateTimeField(default=datetime.utcnow)
    order_type = StringField(default='terminal_purchase')

    meta = {
        'collection': 'terminalOrders',  # Explicitly set collection name
        'indexes': [
            'username',
            'customerName',
            'created_at',
            'status',
            'order_type'
        ]
    }

    def __str__(self):
        return f"Terminal Order - {self.customerName} - ${self.total} - {self.created_at}"
