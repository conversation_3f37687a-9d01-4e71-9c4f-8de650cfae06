<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-11543958767"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'AW-11543958767');
    </script>
    <!-- Meta Pixel Code -->
    <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '1273560210584274');
    fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=1273560210584274&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Meta Pixel Code -->
    <title>{% block title %}Dashboard{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&family=Roboto:wght@400;600&display=swap" rel="stylesheet">
    <!-- Base styles for desktop -->
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <!-- Mobile-specific styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile.css') }}" media="screen and (max-width: 768px)">
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
    {% if current_user.is_authenticated and current_user.cardmarket_trial %}
    <style>

        .trial-nav {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
            display: flex;
            gap: 0.5rem;
        }

        .trial-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .trial-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            text-decoration: none;
        }

        .trial-dashboard {
            background: #2ecc71;
            color: white;
        }

        .trial-dashboard:hover {
            background: #27ae60;
            color: white;
        }



        .trial-profile {
            background: #3498db;
            color: white;
        }

        .trial-profile:hover {
            background: #2980b9;
            color: white;
        }

        .trial-logout {
            background: #e74c3c;
            color: white;
        }

        .trial-logout:hover {
            background: #c0392b;
            color: white;
        }
    </style>

    <!-- Trial Navigation Buttons -->
    <div class="trial-nav">
        <a href="{{ url_for('direct_staff_dashboard') }}" class="trial-btn trial-dashboard">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>

        <a href="{{ url_for('profile.profile') }}" class="trial-btn trial-profile">
            <i class="fas fa-user-circle"></i>
            Profile
        </a>
        <a href="{{ url_for('auth.logout') }}" class="trial-btn trial-logout">
            <i class="fas fa-sign-out-alt"></i>
            Logout
        </a>
    </div>
    {% endif %}

    <style>
        /* Custom Tooltip Styling */
        .tooltip {
            font-size: 0.95rem;
            font-family: 'Poppins', sans-serif;
        }

        .tooltip .tooltip-inner {
            max-width: 300px;
            padding: 8px 12px;
            background-color: rgba(44, 62, 80, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            line-height: 1.4;
        }

        .tooltip.show {
            opacity: 1;
        }

        .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before,
        .bs-tooltip-top .tooltip-arrow::before {
            border-top-color: rgba(44, 62, 80, 0.95);
        }

        .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before,
        .bs-tooltip-bottom .tooltip-arrow::before {
            border-bottom-color: rgba(44, 62, 80, 0.95);
        }

        :root {
            /* Base colors */
            --primary-color: #e91e63; /* Magenta */
            --primary-color-dark: #c2185b;
            --secondary-color: #ff4081;
            --background-color: #1a2b3c;
            --sidebar-bg: #191927; /* Dark navy/purple background */
            --text-color: #ffffff;
            --sidebar-text: #ecf0f1;
            --nav-hover: rgba(233, 30, 99, 0.15);

            /* Status colors */
            --danger-color: #e74c3c;
            --success-color: #2ecc71;
            --warning-color: #f1c40f;
            --info-color: #3498db;

            /* Section-specific colors */
            --dashboard-color: #ff4081;
            --advanced-color: #9b59b6;
            --shopify-color: #2ecc71;
            --buylist-color: #e67e22;
            --pos-color: #e74c3c;
            --cardmarket-color: #1abc9c;
            --automations-color: #f1c40f;
            --admin-color: #34495e;
        }



        .main-content {
            margin-left: 0;
            padding: 2rem;
            width: 100%;
            height: 100vh;
            overflow-y: auto;
            /* Container width is overridden in specific pages that need wider content */
        }

        /* App logo */
        .app-logo {
            color: #ffffff;
            font-size: 24px;
            font-weight: 700;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* Top bar styles - Enhanced for sleek appearance */
        .top-bar {
            position: fixed;
            top: 0;
            right: 0;
            width: 100%;
            height: 64px;
            background: linear-gradient(90deg, rgba(26, 32, 44, 0.95), rgba(45, 55, 72, 0.95));
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            z-index: 100;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
        }

        .top-bar-left {
            display: flex;
            align-items: center;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;
        }

        .user-info::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 50%;
            height: 24px;
            width: 1px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
            transform: translateY(-50%);
        }

        .home-btn {
            background: rgba(52, 152, 219, 0.15);
            color: #3498db;
            border: none;
            border-radius: 12px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .home-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(52, 152, 219, 0.2) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .home-btn:hover {
            background: rgba(52, 152, 219, 0.25);
            transform: translateY(-2px);
            color: #3498db;
            box-shadow: 0 6px 12px rgba(52, 152, 219, 0.25);
        }

        .home-btn:hover::after {
            opacity: 1;
        }

        .back-btn {
            background: rgba(155, 89, 182, 0.15);
            color: #9b59b6;
            border: none;
            border-radius: 12px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
            margin-right: 12px;
        }

        .back-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(155, 89, 182, 0.2) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(155, 89, 182, 0.25);
            transform: translateY(-2px);
            color: #9b59b6;
            box-shadow: 0 6px 12px rgba(155, 89, 182, 0.25);
        }

        .back-btn:hover::after {
            opacity: 1;
        }

        .username {
            color: #ffffff;
            font-weight: 600;
            font-size: 14px;
            letter-spacing: 0.5px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        .username::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(90deg, #3498db, transparent);
            transition: width 0.3s ease;
        }

        .username:hover::after {
            width: 100%;
        }

        .logout-btn {
            background: rgba(231, 76, 60, 0.15);
            color: #e74c3c;
            border: none;
            border-radius: 10px;
            padding: 7px 16px;
            font-size: 13px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .logout-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(231, 76, 60, 0.2) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(231, 76, 60, 0.25);
            transform: translateY(-2px);
            color: #e74c3c;
            box-shadow: 0 6px 12px rgba(231, 76, 60, 0.25);
        }

        .logout-btn:hover::after {
            opacity: 1;
        }

        /* Subscription badge styles */
        .subscription-info {
            margin-right: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .subscription-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .subscription-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .subscription-badge:hover::before {
            left: 100%;
        }

        .subscription-free {
            background: rgba(108, 117, 125, 0.15);
            color: #6c757d;
            border-color: rgba(108, 117, 125, 0.3);
        }

        .subscription-basic {
            background: rgba(52, 152, 219, 0.15);
            color: #3498db;
            border-color: rgba(52, 152, 219, 0.3);
        }

        .subscription-premium {
            background: rgba(155, 89, 182, 0.15);
            color: #9b59b6;
            border-color: rgba(155, 89, 182, 0.3);
        }

        .subscription-enterprise {
            background: rgba(241, 196, 15, 0.15);
            color: #f1c40f;
            border-color: rgba(241, 196, 15, 0.3);
        }

        .subscription-pro {
            background: rgba(46, 204, 113, 0.15);
            color: #2ecc71;
            border-color: rgba(46, 204, 113, 0.3);
        }



        /* Support bar styles */
        .global-support-bar {
            position: fixed;
            top: 64px; /* Below the top bar */
            left: 0;
            right: 0;
            background: rgba(25, 25, 39, 0.95);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 8px 20px;
            z-index: 99;
            transition: all 0.3s ease;
            border-left: 4px solid #f1c40f;
        }

        .global-support-bar.available {
            border-left-color: #2ecc71;
            background: rgba(46, 204, 113, 0.1);
        }

        .global-support-bar.unavailable {
            border-left-color: #f1c40f;
            background: rgba(241, 196, 15, 0.1);
        }

        .global-support-bar .support-content {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-size: 0.9rem;
            font-weight: 500;
            text-align: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .global-support-bar .support-content i {
            margin-right: 10px;
            font-size: 1rem;
        }

        .global-support-bar a {
            color: #3498db;
            text-decoration: underline;
        }

        .global-support-bar a:hover {
            color: #2980b9;
        }

        /* Adjust main content to account for top bar and support bar */
        .main-content {
            padding-top: 120px !important; /* 64px top bar + 40px support bar + 16px spacing */
        }

        /* [Previous styles remain the same] */
    </style>
</head>
<body>
    <div class="d-flex">

        <main role="main" class="main-content">
            {% if current_user.is_authenticated %}
            <!-- Top bar with username and logout -->
            <div class="top-bar">
                <div class="top-bar-left">
                    <button onclick="goBack()" class="back-btn" title="Go back to previous page">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <a href="{{ url_for('dashboard.dashboard') }}" class="home-btn" title="Go to dashboard">
                        <i class="fas fa-home"></i>
                    </a>
                </div>

                <div class="user-info">
                    <div class="subscription-info">
                        {% if current_user.subscription %}
                            <span class="subscription-badge subscription-{{ current_user.subscription.name.lower() }}">
                                <i class="fas fa-crown"></i> {{ current_user.subscription.name }}
                            </span>
                        {% else %}
                            <span class="subscription-badge subscription-free">
                                <i class="fas fa-user"></i> Free
                            </span>
                        {% endif %}

                    </div>
                    <span class="username">{{ current_user.username }}</span>
                    <a href="{{ url_for('auth.logout') }}" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>

            <!-- Global Support Availability Bar -->
            <div class="global-support-bar" id="globalSupportAvailabilityBar">
                <div class="support-content">
                    <i class="fas fa-headset"></i>
                    <div>
                        <strong>Live Support: </strong>
                        <span id="globalSupportStatus">Checking availability...</span>
                    </div>
                </div>
            </div>
            {% endif %}
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Reprice Confirmation Modal -->
    <div class="modal fade" id="repriceConfirmationModal" tabindex="-1" aria-labelledby="repriceConfirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="background-color: var(--sidebar-bg); color: var(--sidebar-text); border: 1px solid rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title" id="repriceConfirmationModalLabel">
                        <i class="fas fa-sync-alt me-2"></i>Confirm Repricing
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex align-items-start">
                        <i class="fas fa-info-circle text-info me-3 mt-1"></i>
                        <p class="mb-0">Are you sure you want to reprice your inventory? This process may take up to 15 minutes depending on your inventory size.</p>
                    </div>
                </div>
                <div class="modal-footer border-top border-secondary">
                    <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="confirmReprice()" style="background-color: var(--primary-color); border-color: var(--primary-color);">
                        <i class="fas fa-check me-2"></i>Start Repricing
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reprice Processing Modal -->
    <div class="modal fade" id="repriceProcessingModal" tabindex="-1" aria-labelledby="repriceProcessingModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content" style="background-color: var(--sidebar-bg); color: var(--sidebar-text); border: 1px solid rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-4" role="status" style="width: 3.5rem; height: 3.5rem; border-width: 0.25rem; color: var(--primary-color);">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h4 class="mb-3">
                        <i class="fas fa-sync-alt me-2"></i>Repricing Your Inventory
                    </h4>
                    <p class="mb-4" style="color: rgba(255, 255, 255, 0.8);">This process is running in the background and may take several minutes to complete depending on your inventory size.</p>
                    <div class="progress" style="height: 8px; background-color: rgba(255, 255, 255, 0.1);">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%; background-color: var(--primary-color);"></div>
                    </div>
                    <p class="mt-3 mb-0" style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem;">
                        <i class="fas fa-info-circle me-2"></i>You will be redirected when the process is complete
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Modal -->
    <div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contactModalLabel">Contact Support</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="contactForm">
                        <div class="mb-3">
                            <label for="contactSubject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="contactSubject" required>
                        </div>
                        <div class="mb-3">
                            <label for="contactMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="contactMessage" rows="5" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="sendContactBtn">Send Message</button>
                </div>
            </div>
        </div>
    </div>


    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- Main site scripts -->
    <script>
        // Back button functionality
        function goBack() {
            // Check if there's a previous page in history
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // If no history, go to dashboard as fallback
                window.location.href = "{{ url_for('dashboard.dashboard') }}";
            }
        }

        // Optional: Hide back button on dashboard page
        document.addEventListener('DOMContentLoaded', function() {
            const backBtn = document.querySelector('.back-btn');
            const currentPath = window.location.pathname;

            // Hide back button on dashboard page
            if (currentPath === '/dashboard' || currentPath === '/') {
                if (backBtn) {
                    backBtn.style.display = 'none';
                }
            }

            // Initialize global support availability
            updateGlobalSupportAvailability();

            // Update support availability every minute
            setInterval(updateGlobalSupportAvailability, 60000);
        });

        // Global Support availability check script
        // Function to check if current time is within business hours (9am-5pm Monday-Friday) in UK timezone
        function isBusinessHours() {
            // Get current time in UK timezone
            const now = new Date();
            const ukTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/London"}));
            const day = ukTime.getDay(); // 0 is Sunday, 1-5 is Monday-Friday, 6 is Saturday
            const hour = ukTime.getHours();

            // Check if it's a weekday (Monday-Friday)
            if (day >= 1 && day <= 5) {
                // Check if it's between 9am and 5pm
                if (hour >= 9 && hour < 17) {
                    return true;
                }
            }

            return false;
        }

        // Function to update the global support availability bar
        function updateGlobalSupportAvailability() {
            const supportBar = document.getElementById('globalSupportAvailabilityBar');
            const supportStatus = document.getElementById('globalSupportStatus');

            if (!supportBar || !supportStatus) return; // Exit if elements don't exist

            // Get current UK time - ensure it's always UK timezone regardless of user's location
            const now = new Date();
            const ukTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/London"}));
            const ukTimeString = ukTime.toLocaleTimeString('en-GB', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
                timeZone: 'Europe/London'
            });

            if (isBusinessHours()) {
                supportBar.classList.remove('unavailable');
                supportBar.classList.add('available');
                supportStatus.innerHTML = `Available (9am-5pm Monday-Friday) - Current UK Time: ${ukTimeString} - <a href="https://discord.gg/7tJdxehgTm" target="_blank">Join Discord Support</a>`;
            } else {
                supportBar.classList.remove('available');
                supportBar.classList.add('unavailable');
                supportStatus.innerHTML = `Unavailable (Available 9am-5pm Monday-Friday) - Current UK Time: ${ukTimeString} - Response may take up to 12 hours - <a href="https://discord.gg/7tJdxehgTm" target="_blank">Join Discord Support</a>`;
            }
        }

        // Site functionality scripts go here
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
