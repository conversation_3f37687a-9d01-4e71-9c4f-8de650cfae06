from flask import Blueprint, current_app
from pymongo import MongoClient
from bson import ObjectId
from json import JSONEncoder
import logging
from datetime import datetime
from routes.buylist import buylist_bp

# Custom JSON encoder for datetime objects
class CustomJSONEncoder(JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, ObjectId):
            return str(obj)
        return super().default(obj)

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the MongoDB client
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_client = MongoClient(mongo_uri)
db = mongo_client['test']
catalog_collection = db.catalog
user_collection = db.user
tcgplayer_key_collection = db.tcgplayerKey
order_collection = db.buylistOrders
price_history_collection = db.priceHistories
staged_inventory_collection = db.staged_inventory
inventory_collection = db.inventory
price_cache_collection = db.tcgplayer_price_cache  # Collection for caching TCGplayer prices

# Configure the blueprint to use the custom JSON encoder
buylist_bp.json_encoder = CustomJSONEncoder
