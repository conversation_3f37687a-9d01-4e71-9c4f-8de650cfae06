from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
import logging
from pymongo import MongoClient
from models.user_model import User

logger = logging.getLogger(__name__)

# MongoDB connection
client = MongoClient("mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin")
db = client.test
rarities_collection = db['rarities']
printings_collection = db['printings']

def get_category_id_for_game(game_name):
    """Get the categoryId for a given game name from the rarities collection"""
    try:
        rarity_doc = rarities_collection.find_one({"gameName": game_name})
        if rarity_doc:
            return rarity_doc.get("categoryId")
        return None
    except Exception as e:
        logger.error(f"Error getting categoryId for game {game_name}: {str(e)}")
        return None

def create_games_routes(bp):
    @bp.route('/api/games-test')
    def test_games_api():
        """Test endpoint to check if the games API routes are accessible."""
        return jsonify({"message": "Shopify autopricing games API is working!"})
    @bp.route('/api/games', methods=['GET'])
    @login_required
    def get_catalog_games():
        """Get list of unique games from catalog"""
        try:

            # Get games from catalog
            try:
                catalog_games = db['catalog'].distinct('gameName')
                catalog_games = [g for g in catalog_games if g]
            except Exception as catalog_error:
                logger.error(f"Error getting catalog games: {str(catalog_error)}")
                catalog_games = []

            # Also get games from rarities collection
            try:
                rarities_games = rarities_collection.distinct('gameName')
            except Exception as rarities_error:
                logger.error(f"Error getting rarities games: {str(rarities_error)}")
                rarities_games = []

            # Combine and deduplicate
            all_games = set(catalog_games + rarities_games)

            # Filter out None/empty values and sort
            games = sorted([g for g in all_games if g])
            
            logger.info(f"Successfully retrieved {len(games)} games")
            return jsonify({'games': games})
        except Exception as e:
            logger.error(f"Error getting games list: {str(e)}")
            return jsonify({'error': 'Failed to get games list'}), 500

    @bp.route('/api/game-data/<game_name>', methods=['GET'])
    @login_required
    def get_game_data(game_name):
        """Get game-specific data including rarities and print types"""
        try:
            catalog_coll = db['catalog']

            # Get rarities from the rarities collection first
            category_id = get_category_id_for_game(game_name)
            rarity_data = []

            if category_id:
                rarity_doc = rarities_collection.find_one({"categoryId": category_id})
                if rarity_doc and "rarities" in rarity_doc:
                    rarity_data = rarity_doc["rarities"]
                    rarities = [r["displayText"] for r in rarity_data if "displayText" in r]
                    logger.info(f"Found {len(rarities)} rarities from rarities collection for {game_name}")
                    # Log the actual rarities for debugging
                    logger.info(f"Rarities for {game_name}: {rarities}")

            # If no rarities found in rarities collection, fall back to catalog
            if not rarity_data:
                catalog_rarities = catalog_coll.distinct('rarity', {'gameName': game_name})
                rarities = [r for r in catalog_rarities if r]  # Filter out None/empty values
                logger.info(f"Using {len(rarities)} rarities from catalog for {game_name}")

            # Get print types from the printings collection first
            category_id = get_category_id_for_game(game_name)
            printing_data = []
            print_types = []

            if category_id:
                printing_doc = printings_collection.find_one({"categoryId": category_id})
                if printing_doc and "printings" in printing_doc:
                    printing_data = printing_doc["printings"]
                    print_types = [p["name"] for p in printing_data if "name" in p]
                    logger.info(f"Found {len(print_types)} print types from printings collection for {game_name}")
                    # Log the actual print types for debugging
                    logger.info(f"Print types for {game_name}: {print_types}")

            # If no print types found in printings collection, fall back to catalog
            if not print_types:
                # Get unique print types from skus
                print_types = catalog_coll.distinct('skus.printingName', {'gameName': game_name})
                print_types = [pt for pt in print_types if pt]  # Filter out None/empty values
                logger.info(f"Using {len(print_types)} print types from catalog skus for {game_name}")

                # If no print types found in skus, try subtype field
                if not print_types:
                    print_types = catalog_coll.distinct('subtype', {'gameName': game_name})
                    print_types = [pt for pt in print_types if pt]
                    logger.info(f"Using {len(print_types)} print types from catalog subtype for {game_name}")

            return jsonify({
                'rarities': sorted(rarities),
                'print_types': sorted(print_types),
                'rarity_data': rarity_data,      # Include full rarity data for advanced usage
                'printing_data': printing_data,  # Include full printing data for advanced usage
                'category_id': category_id       # Include category ID for reference
            })
        except Exception as e:
            logger.error(f"Error getting game data: {str(e)}")
            return jsonify({'error': 'Failed to get game data'}), 500

    @bp.route('/api/game-minimum-prices/<game_name>', methods=['GET'])
    @login_required
    def get_game_minimum_prices(game_name):
        try:

            user = User.objects(username=current_user.username).first()
            if not user:
                return jsonify({'error': 'User not found'}), 404

            # Get the base game settings
            game_settings = user.game_minimum_prices.get(game_name, {
                'print_types': {},
                'rarities': {}
            })

            # Enhance with rarity and printing data if available
            category_id = get_category_id_for_game(game_name)
            if category_id:
                # Add rarity data
                rarity_doc = rarities_collection.find_one({"categoryId": category_id})
                if rarity_doc and "rarities" in rarity_doc:
                    game_settings['rarity_data'] = rarity_doc["rarities"]
                    game_settings['category_id'] = category_id

                # Add printing data
                printing_doc = printings_collection.find_one({"categoryId": category_id})
                if printing_doc and "printings" in printing_doc:
                    game_settings['printing_data'] = printing_doc["printings"]

            return jsonify(game_settings)
        except Exception as e:
            logger.error(f"Error getting game minimum prices: {str(e)}")
            return jsonify({'error': 'Failed to get game minimum prices'}), 500

    @bp.route('/api/game-minimum-prices/<game_name>', methods=['POST'])
    @login_required
    def save_game_minimum_prices(game_name):
        try:

            data = request.json
            logger.info(f"Saving game minimum prices for {game_name}. User: {current_user.username}")
            logger.debug(f"Request data: {data}")

            # Validate request format
            if not isinstance(data, dict):
                logger.error(f"Invalid request format - not a dict: {type(data)}")
                return jsonify({'error': 'Invalid request format - must be JSON object'}), 400

            required_fields = ['print_types', 'rarities']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                logger.error(f"Missing required fields: {missing_fields}")
                return jsonify({'error': f'Missing required fields: {", ".join(missing_fields)}'}), 400

            # Validate data types
            if not isinstance(data['print_types'], dict) or not isinstance(data['rarities'], dict):
                logger.error("Print types and rarities must be objects")
                return jsonify({'error': 'Print types and rarities must be objects'}), 400

            # Validate any provided print type prices
            for print_type, price in data['print_types'].items():
                if not isinstance(price, (int, float)) or price <= 0:
                    logger.error(f"Invalid price for print type {print_type}: {price}")
                    return jsonify({'error': f'Invalid price for print type "{print_type}": must be greater than 0'}), 400

            # Validate any provided rarity prices
            for rarity, price in data['rarities'].items():
                if not isinstance(price, (int, float)) or price <= 0:
                    logger.error(f"Invalid price for rarity {rarity}: {price}")
                    return jsonify({'error': f'Invalid price for rarity "{rarity}": must be greater than 0'}), 400

            # If no print types or rarities are provided, require a valid default minimum price
            if not data['print_types'] and not data['rarities']:
                if 'default_min_price' not in data or data['default_min_price'] is None or not isinstance(data['default_min_price'], (int, float)) or data['default_min_price'] <= 0:
                    logger.error("Must provide either specific print type/rarity prices or a valid default minimum price")
                    return jsonify({'error': 'Must provide either specific print type/rarity prices or a default minimum price greater than 0'}), 400

            user = User.objects(username=current_user.username).first()
            if not user:
                logger.error(f"User not found: {current_user.username}")
                return jsonify({'error': 'User not found'}), 404

            # Initialize game_minimum_prices if it doesn't exist
            if not hasattr(user, 'game_minimum_prices'):
                user.game_minimum_prices = {}
                logger.info(f"Initialized game_minimum_prices for user {current_user.username}")

            # Update the settings for the specific game
            user.game_minimum_prices[game_name] = {
                'print_types': data['print_types'],
                'rarities': data['rarities']
            }
            if 'default_min_price' in data:
                user.game_minimum_prices[game_name]['default_min_price'] = data['default_min_price']

            # Save and verify
            try:
                user.save()
                # Verify the save by reloading the user
                updated_user = User.objects(username=current_user.username).first()
                if game_name not in updated_user.game_minimum_prices:
                    raise Exception("Settings not found after save")

                saved_settings = updated_user.game_minimum_prices[game_name]
                if (saved_settings['print_types'] != data['print_types'] or
                    saved_settings['rarities'] != data['rarities']):
                    raise Exception("Saved settings do not match input")

                logger.info(f"Successfully saved and verified game minimum prices for {game_name}")
                return jsonify({
                    'message': 'Game minimum prices saved and verified successfully',
                    'settings': saved_settings
                })
            except Exception as save_error:
                logger.error(f"Error verifying saved settings: {str(save_error)}")
                return jsonify({'error': 'Failed to verify saved settings'}), 500

        except Exception as e:
            logger.error(f"Error saving game minimum prices: {str(e)}", exc_info=True)
            return jsonify({'error': 'Failed to save game minimum prices'}), 500

    @bp.route('/api/game-minimum-prices/<game_name>', methods=['DELETE'])
    @login_required
    def delete_game_minimum_prices(game_name):
        try:
            # No need to get MongoDB collections for this operation
            # as we're only using the User model

            user = User.objects(username=current_user.username).first()
            if not user:
                return jsonify({'error': 'User not found'}), 404

            if game_name in user.game_minimum_prices:
                del user.game_minimum_prices[game_name]
                user.save()

            return jsonify({'message': 'Game minimum prices deleted successfully'})
        except Exception as e:
            logger.error(f"Error deleting game minimum prices: {str(e)}")
            return jsonify({'error': 'Failed to delete game minimum prices'}), 500
