from flask import jsonify, request
from flask_login import login_required, current_user
from models.user_model import User
from datetime import datetime, timezone
import requests
import logging

# Set up logging
logger = logging.getLogger(__name__)

def init_routes(bp, mongo_client):
    @bp.route('/shopify/customers/api/customer/<customer_id>/refresh_data', methods=['GET', 'POST'])
    @login_required
    def refresh_customer(customer_id):
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            # First, check if the customer exists in our database
            existing_customer = mongo_client['test']['shCustomers'].find_one({
                'id': customer_id,
                'username': current_user.username
            })

            if not existing_customer:
                return jsonify({"error": "Customer not found in database"}), 404

            # Fetch the customer from Shopify
            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers/{customer_id}.json"

            logger.info(f"Fetching customer {customer_id} from Shopify")
            response = requests.get(url, headers=headers)

            if response.status_code != 200:
                logger.error(f"Failed to fetch customer from Shopify: {response.text}")
                return jsonify({"error": f"Failed to fetch customer from Shopify: {response.status_code}"}), 500

            try:
                response_data = response.json()
                logger.info(f"Shopify response data: {response_data}")
            except Exception as e:
                logger.error(f"Failed to parse Shopify response: {str(e)}")
                return jsonify({"error": f"Failed to parse Shopify response: {str(e)}"}), 500

            customer_data = response_data.get('customer', {})
            if not customer_data:
                logger.error("No customer data in Shopify response")
                return jsonify({"error": "No customer data in Shopify response"}), 500

            # Update the customer in MongoDB
            customer_doc = {
                'first_name': customer_data.get('first_name', existing_customer.get('first_name', '')),
                'last_name': customer_data.get('last_name', existing_customer.get('last_name', '')),
                'email': customer_data.get('email', existing_customer.get('email', '')),
                'total_spent': float(customer_data.get('total_spent', 0)),
                'orders_count': int(customer_data.get('orders_count', 0)),
                'accepts_marketing': customer_data.get('accepts_marketing', False),
                'email_subscribed': customer_data.get('accepts_marketing', False),
                'updated_at': datetime.now(timezone.utc)
            }

            logger.info(f"Updating customer in MongoDB: {customer_doc}")
            update_result = mongo_client['test']['shCustomers'].update_one(
                {'id': customer_id, 'username': current_user.username},
                {'$set': customer_doc}
            )

            if update_result.modified_count == 0:
                logger.warning(f"Customer {customer_id} was not modified in the database")
                # This is not an error, the customer might just not have any changes

            # Return success
            return jsonify({
                "success": True,
                "message": "Customer refreshed successfully from Shopify",
                "customer": {
                    "id": customer_id,
                    **customer_doc,
                    "username": current_user.username
                }
            }), 200
        except Exception as e:
            logger.error(f"Error refreshing customer: {str(e)}", exc_info=True)
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500
