from flask import jsonify, request
from flask_login import login_required, current_user
from models.user_model import User
from models.customer_notes_model import CustomerNote
from datetime import datetime
import requests
import json
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('shopify_store_credit.log')
    ]
)
logger = logging.getLogger(__name__)

def init_routes(bp, mongo_client):
    # Helper function to add initial store credit for a new customer
    def add_initial_store_credit(customer_id, amount, note="Initial store credit"):
        """Add initial store credit to a newly created customer"""
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                logger.error("Shopify credentials not found")
                return False, "Shopify credentials not found"
            
            # Get Shopify credentials
            shopify_store_name = user.shopifyStoreName
            # Use the hardcoded API key as in the provided script
            shopify_access_token = "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"
            
            logger.info(f"Using store name: {shopify_store_name} and API key: {shopify_access_token}")
            
            # Get user's currency
            currency = user.currency if hasattr(user, 'currency') and user.currency else 'USD'
            
            # Format customer ID as a Shopify GraphQL ID if it's not already
            if not customer_id.startswith('gid://'):
                customer_gid = f"gid://shopify/Customer/{customer_id}"
            else:
                customer_gid = customer_id
            
            # GraphQL mutation to add store credit
            graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"
            
            mutation = """
            mutation storeCreditAccountCredit($customerId: ID!, $amount: Decimal!, $currencyCode: CurrencyCode!) {
              storeCreditAccountCredit(
                id: $customerId, 
                creditInput: {
                  creditAmount: {
                    amount: $amount
                    currencyCode: $currencyCode
                  }
                }
              ) {
                storeCreditAccountTransaction {
                  id
                  amount {
                    amount
                    currencyCode
                  }
                }
                userErrors {
                  field
                  message
                }
              }
            }
            """
            
            variables = {
                "customerId": customer_gid,
                "amount": amount,  # Pass as a number, not a string
                "currencyCode": currency.upper()
            }
            
            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                graphql_endpoint, 
                headers=headers,
                json={"query": mutation, "variables": variables}
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check for user errors
                user_errors = data.get('data', {}).get('storeCreditAccountCredit', {}).get('userErrors', [])
                if user_errors:
                    error_messages = [error.get('message') for error in user_errors]
                    logger.error(f"Shopify API errors: {error_messages}")
                    return False, f"Shopify API errors: {', '.join(error_messages)}"
                
                # Check if credit was added successfully
                transaction = data.get('data', {}).get('storeCreditAccountCredit', {}).get('storeCreditAccountTransaction', {})
                if transaction:
                    amount_obj = transaction.get('amount', {})
                    credited_amount = amount_obj.get('amount')
                    currency_code = amount_obj.get('currencyCode')
                    transaction_id = transaction.get('id')
                    
                    logger.info(f"Successfully added {credited_amount} {currency_code} initial credit")
                    
                    # Store the transaction in MongoDB
                    store_credit_doc = {
                        'id': transaction_id,
                        'customer_id': customer_id,
                        'amount': float(credited_amount),
                        'currency': currency_code,
                        'created_at': datetime.utcnow(),
                        'note': note,
                        'username': current_user.username,
                        'transaction_type': 'credit'
                    }
                    
                    mongo_client['test']['shStoreCredit'].insert_one(store_credit_doc)
                    return True, store_credit_doc
                else:
                    logger.error(f"Unexpected response format: {data}")
                    return False, "Unexpected response format from Shopify"
            else:
                error_text = response.text
                logger.error(f"Error from Shopify API: {response.status_code} - {error_text}")
                return False, f"Error from Shopify API: {response.status_code} - {error_text}"
        except Exception as e:
            logger.error(f"Exception while adding initial store credit: {str(e)}")
            return False, f"Exception while adding initial store credit: {str(e)}"
    @bp.route('/shopify/customers/api/customer/<customer_id>/store-credit', methods=['POST'])
    @login_required
    def add_store_credit(customer_id):
        """Add store credit to customer's Shopify account using GraphQL API"""
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            data = request.json
            if not data or 'amount' not in data:
                return jsonify({"error": "Amount is required"}), 400

            amount = float(data['amount'])
            note = data.get('note', "Added via API")
            
            # Get user's currency
            currency = user.currency if hasattr(user, 'currency') and user.currency else 'USD'
            
            # Get Shopify credentials
            shopify_store_name = user.shopifyStoreName
            # Use the hardcoded API key as in the provided script
            shopify_access_token = "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"
            
            logger.info(f"Using store name: {shopify_store_name} and API key: {shopify_access_token}")
            
            if not shopify_store_name:
                return jsonify({"error": "Shopify store name not found"}), 400
            
            # Find the customer in MongoDB to get additional details
            customer = mongo_client['test']['shCustomers'].find_one({
                "username": current_user.username,
                "id": customer_id
            })
            
            if not customer:
                return jsonify({"error": f"Customer with ID {customer_id} not found"}), 404
            
            first_name = customer.get('first_name', '')
            last_name = customer.get('last_name', '')
            full_name = f"{first_name} {last_name}".strip() or "No name"
            
            logger.info(f"Adding store credit for customer: {full_name} with ID: {customer_id}")
            
            # Create store credit using GraphQL mutation
            graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"
            
            # GraphQL mutation to add store credit
            mutation = """
            mutation storeCreditAccountCredit($customerId: ID!, $amount: Decimal!, $currencyCode: CurrencyCode!) {
              storeCreditAccountCredit(
                id: $customerId, 
                creditInput: {
                  creditAmount: {
                    amount: $amount
                    currencyCode: $currencyCode
                  }
                }
              ) {
                storeCreditAccountTransaction {
                  id
                  amount {
                    amount
                    currencyCode
                  }
                }
                userErrors {
                  field
                  message
                }
              }
            }
            """
            
            # Format customer ID as a Shopify GraphQL ID if it's not already
            if not customer_id.startswith('gid://'):
                customer_gid = f"gid://shopify/Customer/{customer_id}"
            else:
                customer_gid = customer_id
            
            variables = {
                "customerId": customer_gid,
                "amount": amount,  # Pass as a number, not a string
                "currencyCode": currency.upper()
            }
            
            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }
            
            try:
                response = requests.post(
                    graphql_endpoint, 
                    headers=headers,
                    json={"query": mutation, "variables": variables}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Check for user errors
                    user_errors = data.get('data', {}).get('storeCreditAccountCredit', {}).get('userErrors', [])
                    if user_errors:
                        error_messages = [error.get('message') for error in user_errors]
                        logger.error(f"Shopify API errors: {error_messages}")
                        return jsonify({"error": f"Shopify API errors: {', '.join(error_messages)}"}), 400
                    
                    # Check if credit was added successfully
                    transaction = data.get('data', {}).get('storeCreditAccountCredit', {}).get('storeCreditAccountTransaction', {})
                    if transaction:
                        amount_obj = transaction.get('amount', {})
                        credited_amount = amount_obj.get('amount')
                        currency_code = amount_obj.get('currencyCode')
                        transaction_id = transaction.get('id')
                        
                        logger.info(f"Successfully added {credited_amount} {currency_code} credit to {full_name}'s account")
                        
                        # Store the transaction in MongoDB
                        store_credit_doc = {
                            'id': transaction_id,
                            'customer_id': customer_id,
                            'amount': float(credited_amount),
                            'currency': currency_code,
                            'created_at': datetime.utcnow(),
                            'note': note,
                            'username': current_user.username,
                            'transaction_type': 'credit'
                        }
                        
                        mongo_client['test']['shStoreCredit'].insert_one(store_credit_doc)
                        
                        # Add a note about the store credit
                        if note:
                            staff_name = data.get('staff_name', current_user.username)
                            note_text = f"Added store credit: {credited_amount} {currency_code}\nNote: {note}"
                            
                            customer_note = CustomerNote()
                            customer_note.create_note(
                                mongo_client['test'],
                                customer_id,
                                note_text,
                                current_user.username,
                                staff_name
                            )
                        
                        return jsonify({
                            "success": True,
                            "message": f"Successfully added {credited_amount} {currency_code} store credit",
                            "transaction": {
                                "id": transaction_id,
                                "amount": credited_amount,
                                "currency": currency_code
                            }
                        }), 201
                    else:
                        logger.error(f"Unexpected response format: {data}")
                        return jsonify({"error": "Unexpected response format from Shopify"}), 500
                else:
                    error_text = response.text
                    logger.error(f"Error from Shopify API: {response.status_code} - {error_text}")
                    return jsonify({"error": f"Error from Shopify API: {response.status_code} - {error_text}"}), response.status_code
            except Exception as e:
                logger.error(f"Exception while adding store credit: {str(e)}")
                return jsonify({"error": f"Exception while adding store credit: {str(e)}"}), 500
        except Exception as e:
            logger.error(f"Error in add_store_credit: {str(e)}")
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @bp.route('/shopify/customers/api/customer/<customer_id>/refresh-store-credit', methods=['GET'])
    @login_required
    def refresh_store_credit(customer_id):
        """Refresh store credit balance for a customer using GraphQL API"""
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400
            
            # Get Shopify credentials
            shopify_store_name = user.shopifyStoreName
            # Use the hardcoded API key as in the provided script
            shopify_access_token = "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"
            
            logger.info(f"Using store name: {shopify_store_name} and API key: {shopify_access_token}")
            
            # Format customer ID as a Shopify GraphQL ID if it's not already
            if not customer_id.startswith('gid://'):
                customer_gid = f"gid://shopify/Customer/{customer_id}"
            else:
                customer_gid = customer_id
            
            # GraphQL query to get store credit balance
            graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"
            
            # Updated query to handle multiple store credit accounts
            query = """
            query {
              customer(id: "%s") {
                id
                firstName
                lastName
                email
                storeCreditAccounts(first: 10) {
                  edges {
                    node {
                      id
                      balance {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
            """ % (customer_gid)
            
            logger.info(f"Refreshing store credit for customer {customer_id}")
            logger.info(f"GraphQL endpoint: {graphql_endpoint}")
            logger.info(f"GraphQL query: {query}")
            
            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                graphql_endpoint, 
                headers=headers,
                json={"query": query}
            )
            
            logger.info(f"GraphQL response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Shopify GraphQL API response: {data}")
                
                # Extract customer and store credit data
                customer_data = data.get('data', {}).get('customer', {})
                store_credit_accounts = customer_data.get('storeCreditAccounts', {}).get('edges', [])
                
                if not store_credit_accounts:
                    return jsonify({
                        "customer_id": customer_id,
                        "balance": {
                            "amount": "0.00",
                            "currency_code": user.currency or "USD"
                        },
                        "accounts": [],
                        "transactions": []
                    })
                
                # Handle multiple store credit accounts
                balances = []
                total_balance = 0.0
                currency_code = user.currency or "USD"
                
                for account_edge in store_credit_accounts:
                    account = account_edge.get('node', {})
                    account_id = account.get('id')
                    balance = account.get('balance', {})
                    amount = balance.get('amount')
                    currency = balance.get('currencyCode')
                    
                    logger.info(f"Store credit account {account_id}: {amount} {currency}")
                    
                    if amount and currency:
                        amount_float = float(amount)
                        total_balance += amount_float
                        currency_code = currency  # Use the currency from the account
                        
                        balances.append({
                            "account_id": account_id,
                            "amount": amount,
                            "currency_code": currency
                        })
                
                return jsonify({
                    "customer_id": customer_id,
                    "balance": {
                        "amount": str(total_balance),
                        "currency_code": currency_code
                    },
                    "accounts": balances,
                    "transactions": []  # We don't have transaction data in this query
                })
            else:
                error_text = response.text
                logger.error(f"Error from Shopify API: {response.status_code} - {error_text}")
                return jsonify({"error": f"Error from Shopify API: {response.status_code} - {error_text}"}), response.status_code
        except Exception as e:
            logger.error(f"Error in refresh_store_credit: {str(e)}")
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500
    
    @bp.route('/shopify/customers/api/customer/<customer_id>/store-credit', methods=['GET'])
    @login_required
    def get_store_credit_balance(customer_id):
        """Get store credit balance for a customer using GraphQL API"""
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400
            
            # Get Shopify credentials
            shopify_store_name = user.shopifyStoreName
            # Use the hardcoded API key as in the provided script
            shopify_access_token = "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"
            
            logger.info(f"Using store name: {shopify_store_name} and API key: {shopify_access_token}")
            
            # Format customer ID as a Shopify GraphQL ID if it's not already
            if not customer_id.startswith('gid://'):
                customer_gid = f"gid://shopify/Customer/{customer_id}"
            else:
                customer_gid = customer_id
            
            # GraphQL query to get store credit balance
            graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"
            
            # Updated query to handle multiple store credit accounts
            query = """
            query {
              customer(id: "%s") {
                id
                firstName
                lastName
                email
                storeCreditAccounts(first: 10) {
                  edges {
                    node {
                      id
                      balance {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
            """ % (customer_gid)
            
            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }
            
            try:
                response = requests.post(
                    graphql_endpoint, 
                    headers=headers,
                    json={"query": query}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Extract customer and store credit data
                    customer_data = data.get('data', {}).get('customer', {})
                    store_credit_accounts = customer_data.get('storeCreditAccounts', {}).get('edges', [])
                    
                    if not store_credit_accounts:
                        return jsonify({
                            "customer_id": customer_id,
                            "balance": {
                                "amount": "0.00",
                                "currency_code": user.currency or "USD"
                            },
                            "accounts": [],
                            "transactions": []
                        })
                    
                    # Handle multiple store credit accounts
                    balances = []
                    total_balance = 0.0
                    currency_code = user.currency or "USD"
                    
                    for account_edge in store_credit_accounts:
                        account = account_edge.get('node', {})
                        account_id = account.get('id')
                        balance = account.get('balance', {})
                        amount = balance.get('amount')
                        currency = balance.get('currencyCode')
                        
                        if amount and currency:
                            amount_float = float(amount)
                            total_balance += amount_float
                            currency_code = currency  # Use the currency from the account
                            
                            balances.append({
                                "account_id": account_id,
                                "amount": amount,
                                "currency_code": currency
                            })
                    
                    return jsonify({
                        "customer_id": customer_id,
                        "balance": {
                            "amount": str(total_balance),
                            "currency_code": currency_code
                        },
                        "accounts": balances,
                        "transactions": []  # We don't have transaction data in this query
                    })
                else:
                    error_text = response.text
                    logger.error(f"Error from Shopify API: {response.status_code} - {error_text}")
                    return jsonify({"error": f"Error from Shopify API: {response.status_code} - {error_text}"}), response.status_code
            except Exception as e:
                logger.error(f"Exception while getting store credit balance: {str(e)}")
                return jsonify({"error": f"Exception while getting store credit balance: {str(e)}"}), 500
        except Exception as e:
            logger.error(f"Error in get_store_credit_balance: {str(e)}")
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500
