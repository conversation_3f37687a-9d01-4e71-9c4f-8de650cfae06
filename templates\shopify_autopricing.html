{% extends "base.html" %}

{% block title %}Shopify Autopricing{% endblock %}

{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }

    /* Section header styling */
    .section-header {
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    }

    .section-title {
        color: #ffffff;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .section-subtitle {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.875rem;
        margin-bottom: 0;
    }

    /* Modern card styling to match inventory dashboard */
    .autopricing-card {
        border-radius: 12px;
        background-color: rgba(25, 25, 39, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        overflow: hidden;
    }

    .autopricing-card .card-header {
        background: linear-gradient(135deg, rgba(230, 126, 34, 0.8), rgba(230, 126, 34, 0.6));
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 1rem 1.5rem;
    }

    .autopricing-card .card-body {
        background-color: rgba(25, 25, 39, 0.9);
        color: #ffffff;
    }

    /* Form controls styling */
    .form-control, .form-select {
        background-color: rgba(45, 55, 72, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }

    .form-control:focus, .form-select:focus {
        background-color: rgba(45, 55, 72, 0.9);
        border-color: #e67e22;
        box-shadow: 0 0 0 0.2rem rgba(230, 126, 34, 0.25);
        color: #ffffff;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    /* Button styling */
    .btn-primary {
        background: linear-gradient(135deg, #e67e22, #d35400);
        border: none;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #d35400, #e67e22);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
    }

    .btn-outline-primary {
        border-color: #e67e22;
        color: #e67e22;
        background: transparent;
    }

    .btn-outline-primary:hover {
        background-color: #e67e22;
        border-color: #e67e22;
        color: #ffffff;
    }



    /* Alert styling */
    .alert-info {
        background-color: rgba(52, 152, 219, 0.1);
        border: 1px solid rgba(52, 152, 219, 0.3);
        color: #3498db;
    }

    .alert-warning {
        background-color: rgba(243, 156, 18, 0.1);
        border: 1px solid rgba(243, 156, 18, 0.3);
        color: #f39c12;
    }

    /* List group styling */
    .list-group-item {
        background-color: rgba(45, 55, 72, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
    }

    /* Input group styling */
    .input-group-text {
        background-color: rgba(45, 55, 72, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }

    /* Switch styling */
    .form-check-input:checked {
        background-color: #e67e22;
        border-color: #e67e22;
    }

    .form-check-input:focus {
        border-color: #e67e22;
        box-shadow: 0 0 0 0.25rem rgba(230, 126, 34, 0.25);
    }
</style>

<div class="container mt-5">
    <!-- Page Header -->
    <div class="section-header">
        <div>
            <h2 class="section-title">
                <i class="fas fa-tags me-2" style="color: #e67e22;"></i>
                Shopify Autopricing
            </h2>
            <p class="section-subtitle">Configure automated pricing rules, condition percentages, and game-specific minimum prices</p>
        </div>
    </div>

    <!-- Price Type Preference Section -->
    <div class="card autopricing-card mb-4">
        <div class="card-header">
            <h5 class="mb-0 text-white">
                <i class="fas fa-sort-amount-down me-2"></i>
                Price Type Preference
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Use the arrow buttons to arrange price types in your preferred order. The system will try to use prices in this order.
            </div>
            <div id="priceTypeList" class="list-group">
                <div class="list-group-item bg-dark text-light d-flex justify-content-between align-items-center" data-type="lowPrice">
                    <span>Low Price</span>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-light move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                        <button class="btn btn-sm btn-outline-light move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                    </div>
                </div>
                <div class="list-group-item bg-dark text-light d-flex justify-content-between align-items-center" data-type="marketPrice">
                    <span>Market Price</span>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-light move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                        <button class="btn btn-sm btn-outline-light move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                    </div>
                </div>
                <div class="list-group-item bg-dark text-light d-flex justify-content-between align-items-center" data-type="midPrice">
                    <span>Mid Price</span>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-light move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                        <button class="btn btn-sm btn-outline-light move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                    </div>
                </div>
                <div class="list-group-item bg-dark text-light d-flex justify-content-between align-items-center" data-type="highPrice">
                    <span>High Price</span>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-light move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                        <button class="btn btn-sm btn-outline-light move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                    </div>
                </div>
            </div>
            <button class="btn btn-primary mt-3" id="savePriceTypePreference">Save Preference</button>
        </div>
    </div>

    <!-- Default Condition Settings -->
    <div class="card autopricing-card mb-4">
        <div class="card-header">
            <h5 class="mb-0 text-white">
                <i class="fas fa-cogs me-2"></i>
                Default Pricing Settings
            </h5>
        </div>
        <div class="card-body">
            <div class="form-check form-switch mb-3">
                <input type="checkbox" class="form-check-input" id="useSkuIdPricing">
                <label class="form-check-label text-light" for="useSkuIdPricing">Use SKU ID Pricing</label>
            </div>
            <div class="alert alert-warning" id="skuIdWarning" style="display: none;">
                Warning: SKU ID pricing can result in lower condition variants being priced higher than higher conditions. If SKU ID pricing is unavailable for a product, it will automatically revert to using your standard condition settings, so please ensure all settings are configured.
            </div>

            <div class="card autopricing-card mb-3">
                <div class="card-header">
                    <div class="form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="priceRoundingEnabled">
                        <label class="form-check-label text-light" for="priceRoundingEnabled">
                            <i class="fas fa-calculator me-2"></i>Enable Price Rounding
                        </label>
                    </div>
                </div>
                <div class="card-body" id="priceRoundingSettings" style="display: none;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Enter decimal values (0-99) to round prices to. For example, entering 49 and 99 will round prices to the nearest .49 or .99
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-light">Rounding Thresholds</label>
                        <div id="thresholdInputs" class="d-flex gap-2 align-items-center">
                            <input type="number" class="form-control threshold-input" min="0" max="99" placeholder="e.g. 49">
                            <input type="number" class="form-control threshold-input" min="0" max="99" placeholder="e.g. 99">
                            <button class="btn btn-outline-primary" id="addThreshold">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="card autopricing-card">
                        <div class="card-body">
                            <label for="nmInput" class="text-light">
                                <i class="fas fa-gem me-2" style="color: #2ecc71;"></i>Near Mint (%):
                            </label>
                            <input type="number" id="nmInput" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card autopricing-card">
                        <div class="card-body">
                            <label for="lpInput" class="text-light">
                                <i class="fas fa-star me-2" style="color: #3498db;"></i>Lightly Played (%):
                            </label>
                            <input type="number" id="lpInput" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card autopricing-card">
                        <div class="card-body">
                            <label for="mpInput" class="text-light">
                                <i class="fas fa-star-half-alt me-2" style="color: #f39c12;"></i>Moderately Played (%):
                            </label>
                            <input type="number" id="mpInput" class="form-control">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row g-3 mt-2">
                <div class="col-md-4">
                    <div class="card autopricing-card">
                        <div class="card-body">
                            <label for="hpInput" class="text-light">
                                <i class="fas fa-exclamation-triangle me-2" style="color: #e67e22;"></i>Heavily Played (%):
                            </label>
                            <input type="number" id="hpInput" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card autopricing-card">
                        <div class="card-body">
                            <label for="dmInput" class="text-light">
                                <i class="fas fa-times-circle me-2" style="color: #e74c3c;"></i>Damaged (%):
                            </label>
                            <input type="number" id="dmInput" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card autopricing-card">
                        <div class="card-body">
                            <label for="minPriceInput" class="text-light">
                                <i class="fas fa-dollar-sign me-2" style="color: #2ecc71;"></i>Minimum Price ({{ user_currency }}):
                            </label>
                            <input type="number" id="minPriceInput" class="form-control">
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <button id="saveAutopricingSettings" class="btn btn-primary">Save Default Settings</button>
            </div>
        </div>
    </div>

    <!-- Price Comparison Settings -->
    <div class="card autopricing-card mb-4">
        <div class="card-header">
            <h5 class="mb-0 text-white">
                <i class="fas fa-balance-scale me-2"></i>
                Price Comparison Settings
            </h5>
        </div>
        <div class="card-body">
            <div class="form-check form-switch mb-3">
                <input type="checkbox" class="form-check-input" id="useHighestPrice">
                <label class="form-check-label text-light" for="useHighestPrice" data-bs-toggle="tooltip" data-bs-placement="right" title="When enabled, the system will compare each pair of prices and use the highest value">
                    Use Highest Price from Pairs
                </label>
            </div>

            <div id="priceComparisonPairs" class="mb-3">
                <label class="form-label text-light">Price Comparison Pairs</label>
                <div class="price-pair-container">
                    <!-- Price pairs will be added here -->
                </div>
                <button class="btn btn-outline-primary mt-2" id="addPricePair">
                    <i class="fas fa-plus"></i> Add Price Pair
                </button>
                <div class="mt-2 text-muted small">
                    Compare prices between pairs and use the highest value when enabled above.
                </div>
            </div>

            <div id="priceModifiers" class="mb-3">
                <label class="form-label text-light">Price Modifiers</label>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Enter the percentage to increase prices. For example, enter "10" to increase price by 10%. Enter "0" for no change.
                </div>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <div class="input-group">
                            <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="Enter percentage to increase price. E.g. 10 = +10%">Low Price</span>
                            <input type="number" class="form-control price-modifier" data-type="lowPrice" min="0" step="1" value="0">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-2">
                        <div class="input-group">
                            <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="Enter percentage to increase price. E.g. 10 = +10%">Market Price</span>
                            <input type="number" class="form-control price-modifier" data-type="marketPrice" min="0" step="1" value="0">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-2">
                        <div class="input-group">
                            <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="Enter percentage to increase price. E.g. 10 = +10%">Mid Price</span>
                            <input type="number" class="form-control price-modifier" data-type="midPrice" min="0" step="1" value="0">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-2">
                        <div class="input-group">
                            <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="Enter percentage to increase price. E.g. 10 = +10%">High Price</span>
                            <input type="number" class="form-control price-modifier" data-type="highPrice" min="0" step="1" value="0">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                </div>
            </div>

            <button class="btn btn-primary" id="savePriceComparisonSettings">Save Price Comparison Settings</button>
        </div>
    </div>

    <!-- Game-Specific Minimum Prices Section -->
    <div class="card autopricing-card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-gamepad me-2"></i>
                    Game-Specific Minimum Prices
                </h5>
                <button class="btn btn-sm btn-outline-light" data-bs-toggle="collapse" data-bs-target="#gameMinPricesContent">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
        <div class="card-body collapse show" id="gameMinPricesContent">
            <div class="alert alert-info mb-4">
                <i class="fas fa-info-circle me-2"></i>
                Set minimum prices for different print types and rarities within each game. These settings will override the default minimum price when repricing cards.
            </div>
            
            <!-- Game Selection -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text bg-dark text-light border-secondary">
                            <i class="fas fa-gamepad"></i>
                        </span>
                        <select id="gameSelect" class="form-select">
                            <option value="">Select Game</option>
                        </select>
                        <button id="addNewGame" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> Add Game
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text bg-dark text-light border-secondary">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                        <input type="number" id="defaultMinPrice" class="form-control" placeholder="Default Min Price" step="0.01">
                    </div>
                </div>
            </div>

            <!-- Game Settings Container -->
            <div id="gameSettingsContainer" style="display: none;">
                <div class="row">
                    <!-- Print Type Minimum Prices -->
                    <div class="col-md-6">
                        <div class="card autopricing-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 text-white">
                                    <i class="fas fa-print me-2"></i>
                                    Print Type Minimums
                                </h6>
                                <button id="addPrintType" class="btn btn-sm btn-outline-light">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <div class="card-body">
                    <div id="printTypeInputs" class="row g-3">
                        <!-- Print type inputs will be dynamically added here -->
                    </div>
                    <div id="noPrintTypesMessage" class="alert alert-info" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        No print types have been added yet. Click the "+" button above to add a print type.
                    </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rarity Minimum Prices -->
                    <div class="col-md-6">
                        <div class="card autopricing-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 text-white">
                                    <i class="fas fa-star me-2"></i>
                                    Rarity Minimums
                                </h6>
                                <button id="addRarity" class="btn btn-sm btn-outline-light">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <div class="card-body">
                    <div id="rarityInputs" class="row g-3">
                        <!-- Rarity inputs will be dynamically added here -->
                    </div>
                    <div id="noRaritiesMessage" class="alert alert-info" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        No rarities have been added yet. Click the "+" button above to add a rarity.
                    </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <button id="saveGameSettings" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>
                        Save Game Settings
                    </button>
                    <button id="deleteGameSettings" class="btn btn-outline-danger btn-lg ms-3">
                        <i class="fas fa-trash me-2"></i>
                        Delete Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Test Settings Modal -->
<div class="modal fade" id="testSettingsModal" tabindex="-1" aria-labelledby="testSettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="background-color: rgba(25, 25, 39, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); color: #ffffff;">
            <div class="modal-header" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="testSettingsModalLabel">
                    <i class="fas fa-flask me-2" style="color: #e67e22;"></i>
                    Test Results
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center mb-3">
                    <img id="testProductImage" src="" alt="Product Image" class="img-thumbnail me-3" style="width: 80px;">
                    <div>
                        <h5 id="testProductTitle" class="mb-0"></h5>
                        <button id="tryAnotherButton" class="btn btn-primary btn-sm mt-2">
                            <i class="fas fa-random me-1"></i>Try Another
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover" style="color: #ffffff;">
                        <thead style="background-color: rgba(230, 126, 34, 0.2);">
                            <tr>
                                <th style="border-color: rgba(255, 255, 255, 0.1);">Variant</th>
                                <th style="border-color: rgba(255, 255, 255, 0.1);">Old Price</th>
                                <th style="border-color: rgba(255, 255, 255, 0.1);">New Price</th>
                                <th style="border-color: rgba(255, 255, 255, 0.1);">Difference</th>
                            </tr>
                        </thead>
                        <tbody id="priceChangesTable" style="background-color: rgba(45, 55, 72, 0.3);">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Price Details Modal -->
<div class="modal fade" id="priceDetailsModal" tabindex="-1" aria-labelledby="priceDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="background-color: rgba(25, 25, 39, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); color: #ffffff;">
            <div class="modal-header" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="priceDetailsModalLabel">
                    <i class="fas fa-info-circle me-2" style="color: #3498db;"></i>
                    Price Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Content will be dynamically populated -->
            </div>
            <div class="modal-footer" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notification -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="settingsToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true" style="background-color: rgba(25, 25, 39, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); color: #ffffff;">
        <div class="d-flex">
            <div class="toast-body" id="toastMessage"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/shopify_autopricing.js') }}"></script>
<script src="{{ url_for('static', filename='js/game_minimum_prices.js') }}"></script>
<script>
    // Currency symbol helper function
    function getCurrencySymbol(currency) {
        const symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            // Add more currency symbols as needed
        };
        return symbols[currency] || currency;
    }

    // Initialize user currency
    const userCurrency = '{{ user_currency }}';



    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize price comparison settings and game settings as usual.
    });
</script>
{% endblock %}
