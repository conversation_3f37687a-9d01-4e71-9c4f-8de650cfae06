{% extends "base.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">

<style>
    /* Admin Dashboard Styles */
    body {
        background-color: #6b21a8; /* Purple background */
    }

    .main-content {
        background-color: #6b21a8;
    }

    .dashboard-container {
        padding: 10px;
    }

    /* Quick Access Section */
    .quick-access-section {
        background: linear-gradient(135deg, #1a1a2e, #16213e); /* Modern gradient background */
        border: none; /* Remove border for cleaner look */
        border-radius: 16px; /* Rounded corners */
        overflow: hidden;
        margin-bottom: 20px;
        margin-top: 5px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
        position: relative;
    }

    .quick-access-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(to right, #ff4081, #7e57c2, #2196f3); /* Colorful top accent */
        z-index: 2;
    }

    /* Quick Access Cards */
    .quick-access-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0;
        padding: 15px;
        transform-origin: top center;
        margin: 0 auto;
        max-width: 95%;
    }

    @media (max-width: 1200px) {
        .quick-access-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            padding: 20px;
        }
    }

    @media (max-width: 768px) {
        .quick-access-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 20px 15px;
        }
    }

    .quick-access-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        background-color: rgba(255, 255, 255, 0.03); /* Subtle background */
        padding: 15px; /* Reduced padding */
        cursor: pointer;
        height: 100%;
        border-radius: 10px; /* Slightly smaller rounded corners */
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1); /* Smooth transition */
        position: relative;
        overflow: hidden;
        margin: 4px; /* Reduced margin */
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1); /* Smaller shadow */
        text-decoration: none;
        transform: scale(0.75); /* Reduce overall size by 25% */
    }

    .quick-access-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at center, rgba(255, 64, 129, 0.15) 0%, transparent 70%);
        opacity: 0;
        transition: opacity 0.4s ease;
        z-index: 0;
    }

    .quick-access-card:hover {
        transform: translateY(-5px);
        background-color: rgba(255, 255, 255, 0.07);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    .quick-access-card:hover::before {
        opacity: 1;
    }

    .quick-access-card:active {
        transform: translateY(-2px);
        transition: transform 0.1s;
    }

    .quick-access-card-icon {
        width: 45px; /* Reduced size */
        height: 45px; /* Reduced size */
        background: rgba(255, 64, 129, 0.1);
        border-radius: 10px; /* Slightly smaller rounded corners */
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px; /* Reduced margin */
        position: relative;
        z-index: 1;
        transition: all 0.4s ease;
    }

    .quick-access-card:hover .quick-access-card-icon {
        background: rgba(255, 64, 129, 0.2);
        transform: scale(1.05) rotate(5deg);
    }

    .quick-access-card i {
        font-size: 22px; /* Reduced font size */
        color: #ff4081; /* Base icon color */
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy animation */
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 3px 4px rgba(0, 0, 0, 0.3)); /* Smaller shadow */
    }

    .quick-access-card:hover i {
        transform: scale(1.15);
        color: #ff4081; /* Brighter color on hover */
    }

    .quick-access-card-content {
        position: relative;
        z-index: 1;
    }

    .quick-access-card span {
        color: #ffffff;
        font-size: 14px; /* Reduced font size */
        font-weight: 600;
        margin-bottom: 2px; /* Reduced margin */
        letter-spacing: 0.5px;
        position: relative;
        z-index: 1;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Smaller shadow */
        transition: all 0.3s ease;
    }

    .quick-access-card:hover .quick-access-card-content {
        transform: translateY(2px);
    }

    /* Admin Cards Section */
    .admin-cards-section {
        margin-top: 20px;
    }

    .section-title {
        color: #ffffff;
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .dashboard-card {
        background-color: #1e293b;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        height: 100%;
    }

    .dashboard-card-header {
        padding: 15px 20px;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        display: flex;
        align-items: center;
    }

    .dashboard-card-header i {
        margin-right: 10px;
    }

    .dashboard-card-body {
        padding: 20px;
    }

    /* Card specific styling */
    .quick-access-card.users .quick-access-card-icon {
        background: rgba(52, 152, 219, 0.1);
    }

    .quick-access-card.users i {
        color: #3498db;
    }

    .quick-access-card.users:hover .quick-access-card-icon {
        background: rgba(52, 152, 219, 0.2);
    }

    .quick-access-card.payments .quick-access-card-icon {
        background: rgba(46, 204, 113, 0.1);
    }

    .quick-access-card.payments i {
        color: #2ecc71;
    }

    .quick-access-card.payments:hover .quick-access-card-icon {
        background: rgba(46, 204, 113, 0.2);
    }

    .quick-access-card.subscriptions .quick-access-card-icon {
        background: rgba(155, 89, 182, 0.1);
    }

    .quick-access-card.subscriptions i {
        color: #9b59b6;
    }

    .quick-access-card.subscriptions:hover .quick-access-card-icon {
        background: rgba(155, 89, 182, 0.2);
    }
</style>

<div class="container-fluid dashboard-container">
    {% if session.get('admin_user_id') %}
    <button class="btn btn-warning mb-3" onclick="returnToAdmin()">Return to Admin</button>
    {% endif %}

    <!-- Quick Access Section -->
    <div class="quick-access-section">
        <div class="quick-access-grid">
            <a href="javascript:void(0);" class="quick-access-card users" id="usersQuickAccess">
                <div class="quick-access-card-icon" style="width: 60px; height: 60px;">
                    <i class="fas fa-users" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Users</span>
                </div>
            </a>

            <a href="javascript:void(0);" class="quick-access-card payments" id="paymentsQuickAccess">
                <div class="quick-access-card-icon" style="width: 60px; height: 60px;">
                    <i class="fas fa-credit-card" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>FBT Payments</span>
                </div>
            </a>
        </div>
    </div>

    <div id="paymentsDetails" style="display: none;">
        <button class="btn btn-primary mb-4" onclick="showMainDashboard()"><i class="fas fa-arrow-left me-2"></i>Back to Dashboard</button>
        <h2 class="text-white mb-4">FBT Payments Details</h2>
         <button class="btn btn-primary mb-2" onclick="window.print()"><i class="fas fa-print me-2"></i>Print Table</button>
        <table class="table table-striped table-dark" id="paymentsTable">
            <thead>
                <tr>
                    <th>User</th>
                    <th>FBT Username</th>
                    <th>Total Value</th>
                    <th>Total Count</th>
                    <th>Platform Fee</th>
                    <th>FBT Fee</th>
                    <th>Take Home</th>
                    <th>Total Paid</th>
                    <th>Balance Due</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="paymentsTableBody">
                <!-- FBT total data will be dynamically inserted here -->
            </tbody>
        </table>
    </div>

    <div id="userDetailsModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h3>Transactions</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody id="transactionsBody">
                                <!-- Transactions will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                    <h3>Sales Records</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Article</th>
                                    <th>Price</th>
                                    <th>Count</th>
                                    <th>Value</th>
                                    <th>Platform Fee</th>
                                    <th>FBT Fee</th>
                                    <th>Take Home</th>
                                </tr>
                            </thead>
                            <tbody id="salesRecordsBody">
                                <!-- Sales records will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                    <h3>Add Transaction</h3>
                    <form id="addTransactionForm">
                        <input type="hidden" id="transactionFbtUsername">
                        <div class="form-group">
                            <label for="transactionAmount">Amount</label>
                            <input type="number" step="0.01" class="form-control" id="transactionAmount" required>
                        </div>
                        <div class="form-group">
                            <label for="transactionDate">Date</label>
                            <input type="date" class="form-control" id="transactionDate" required>
                        </div>
                        <div class="form-group">
                            <label for="transactionType">Type</label>
                            <select class="form-control" id="transactionType" required>
                                <option value="payment">Payment</option>
                                <option value="refund">Refund</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Add Transaction</button>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div id="fbtDetails" style="display: none;">
        <button class="btn btn-primary mb-4" onclick="showMainDashboard()"><i class="fas fa-arrow-left me-2"></i>Back to Dashboard</button>
        <h2 class="text-white mb-4">FBT Product Details</h2>
        <div class="row" id="commentCards">
            <!-- Comment cards will be dynamically inserted here -->
        </div>
    </div>

    <div id="userDetails" style="display: none;">
        <button class="btn btn-primary mb-4" onclick="showMainDashboard()"><i class="fas fa-arrow-left me-2"></i>Back to Dashboard</button>
        <h2 class="text-white mb-4">User & Subscription Management</h2>

        <div class="row mb-3">
            <div class="col-md-4">
                <label for="usernameFilter">Username</label>
                <input type="text" id="usernameFilter" class="form-control" placeholder="Filter by username" onkeyup="filterUsers()">
            </div>
            <div class="col-md-4">
                <label for="subscriptionFilter">Subscription</label>
                <select id="subscriptionFilter" class="form-control" onchange="filterUsers()">
                    <option value="">All</option>
                    <option value="Free">Free</option>
                    <option value="Trial">Trial</option>
                    <option value="Monthly">Monthly</option>
                    <option value="Monthly Basic">Monthly Basic</option>
                    <option value="Annual">Annual</option>
                    <option value="Annual Basic">Annual Basic</option>
                    <option value="Enterprise">Enterprise</option>
                    <option value="Lifetime">Lifetime</option>
                    <option value="Suspended">Suspended</option>
                </select>
            </div>
            <div class="col-md-4">
                <button class="btn btn-primary mt-4" id="composeEmailBtn"><i class="fas fa-envelope me-2"></i>Compose Email</button>
                <button class="btn btn-success mt-4 ms-2" id="exportUsersBtn"><i class="fas fa-file-export me-2"></i>Export</button>
            </div>
        </div>

        <table class="table table-striped table-dark" id="userTable">
            <thead>
                <tr>
                    <th onclick="sortUsers('username')" style="cursor: pointer;">Username ↕</th>
                    <th>Email</th>
                    <th>Subscription</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="userTableBody">
                <!-- User details will be dynamically inserted here -->
            </tbody>
        </table>
    </div>

    <div id="commentItems" style="display: none;">
        <button class="btn btn-primary mb-4" onclick="showFBTDetails()"><i class="fas fa-arrow-left me-2"></i>Back to Comment Details</button>
        <h2 id="commentTitle" class="text-white mb-4">Comment Items</h2>

        <div class="form-row mb-3">
            <div class="form-group col-md-6">
                <label for="product_idGame">Product Game</label>
                <select id="product_idGame" class="form-control">
                    <option value="">Select Game</option>
                    <!-- Options will be dynamically added here -->
                </select>
            </div>

            <div class="form-group col-md-6">
                <label for="isFoil">Is Foil</label>
                <select id="isFoil" class="form-control">
                    <option value="">Select Foil Status</option>
                    <option value="true">True</option>
                    <option value="false">False</option>
                </select>
            </div>
        </div>

        <div class="input-group mb-3" style="width: auto;">
            <input type="text" id="itemFilter" class="form-control" style="width: auto;" placeholder="Filter by product name">
            <button class="btn btn-primary mb-3" id="filterBtn" onclick="applyFilter()">Filter</button>
        </div>

        <div class="mb-3">
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('A')">A</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('B')">B</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('C')">C</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('D')">D</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('E')">E</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('F')">F</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('G')">G</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('H')">H</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('I')">I</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('J')">J</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('K')">K</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('L')">L</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('M')">M</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('N')">N</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('O')">O</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('P')">P</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('Q')">Q</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('R')">R</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('S')">S</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('T')">T</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('U')">U</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('V')">V</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('W')">W</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('X')">X</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('Y')">Y</button>
            <button class="btn btn-outline-secondary" onclick="applyLetterFilter('Z')">Z</button>
        </div>

        <button class="btn btn-primary mb-3" id="printBtn" onclick="printFilteredResults()">Print</button>
        <button class="btn btn-warning mb-3" id="stockCheckBtn" onclick="launchStockCheckMode()">Stock Check Mode</button>

        <table class="table table-striped table-dark" id="printTable">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Number</th>
                    <th>Set Code</th>
                    <th>QTY</th>
                    <th>Foil</th>
                    <th>Update Stock</th>
                </tr>
            </thead>
            <tbody id="commentItemsTable">
                <!-- Table body will be populated dynamically -->
            </tbody>
        </table>
        <div class="popup-navigation">
            <button class="btn btn-secondary" id="prevBtn" onclick="prevRecord()">Previous</button>
            <button class="btn btn-primary" id="loadMoreBtn" onclick="loadMoreItems()">Next</button>
        </div>
    </div>
</div> <!-- End of dashboard-container -->

<style>
    /* Main Dashboard Styles */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
        color: #f8f9fa;
    }

    .main-content {
        background-color: #6b21a8;
    }

    /* Card Styling */
    .card-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #ffffff;
    }

    /* Table Styling */
    .table {
        margin-top: 10px;
        color: #ffffff;
        font-size: 0.875rem;
        background-color: #1e293b;
        border-radius: 12px;
        overflow: hidden;
    }

    .table th, .table td {
        color: #ffffff;
        vertical-align: middle;
        padding: 0.75rem;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .table-dark {
        background-color: #1e293b;
    }

    /* Form Controls */
    .form-control, .btn {
        background-color: rgba(255, 255, 255, 0.1);
        color: #f8f9fa;
        border-color: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
    }

    .form-control:focus {
        background-color: rgba(255, 255, 255, 0.15);
        color: #ffffff;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(255, 64, 129, 0.25);
    }

    /* Dropdown styling */
    select.form-control {
        color: #f8f9fa;
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
    }

    select.form-control option {
        color: #f8f9fa;
        background-color: #1e293b;
    }

    .subscription-select {
        color: #f8f9fa !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    .subscription-select option {
        color: #f8f9fa !important;
        background-color: #1e293b !important;
    }

    .btn-primary {
        background: linear-gradient(135deg, #6b21a8, #4c1d95);
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        color: white;
        font-weight: 500;
        padding: 8px 16px;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        background: linear-gradient(135deg, #7c3adb, #5a24b5);
        color: white;
    }

    .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Input Groups */
    .input-group {
        display: flex;
        align-items: center;
    }

    .input-group .form-control {
        width: auto;
        flex-grow: 0;
    }

    .input-group .btn {
        margin-left: 10px;
    }

    .update-stock-input {
        width: 25px;
    }

    #userTable td {
        font-size: 0.8rem;
    }

    /* Quick Access Cards Styling */
    .section-title {
        color: #ffffff;
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    /* Users card styling */
    .quick-access-card.users .quick-access-card-icon {
        background: rgba(52, 152, 219, 0.1);
    }

    .quick-access-card.users i {
        color: #3498db;
    }

    .quick-access-card.users:hover .quick-access-card-icon {
        background: rgba(52, 152, 219, 0.2);
    }

    /* Payments card styling */
    .quick-access-card.payments .quick-access-card-icon {
        background: rgba(46, 204, 113, 0.1);
    }

    .quick-access-card.payments i {
        color: #2ecc71;
    }

    .quick-access-card.payments:hover .quick-access-card-icon {
        background: rgba(46, 204, 113, 0.2);
    }

    /* Activation card styling */
    .quick-access-card.activation .quick-access-card-icon {
        background: rgba(241, 196, 15, 0.1);
    }

    .quick-access-card.activation i {
        color: #f1c40f;
    }

    .quick-access-card.activation:hover .quick-access-card-icon {
        background: rgba(241, 196, 15, 0.2);
    }

    /* Subscriptions card styling */
    .quick-access-card.subscriptions .quick-access-card-icon {
        background: rgba(155, 89, 182, 0.1);
    }

    .quick-access-card.subscriptions i {
        color: #9b59b6;
    }

    .quick-access-card.subscriptions:hover .quick-access-card-icon {
        background: rgba(155, 89, 182, 0.2);
    }
    @media print {
        body * {
            visibility: hidden;
        }
        #commentTitle,
        #printTable,
        #printTable * {
            visibility: visible;
            color: black !important;
            border-color: black !important;
        }
        #printTable th, #printTable td {
            border: 1px solid black !important;
        }
        #printTable {
            position: absolute;
            left: 0;
            top: 0;
            background-color: white !important;
        }
        #commentTitle {
            position: absolute;
            left: 0;
            top: -40px;
            color: black !important;
            background-color: white !important;
        }
    }
    @media print {
        body * {
            visibility: hidden;
        }
        #paymentsDetails, #paymentsDetails * {
            visibility: visible;
            color: black !important;
            border-color: black !important;
        }
        #paymentsTable th, #paymentsTable td {
            border: 1px solid black !important;
            background-color: white !important;
        }
        #paymentsTable {
            position: absolute;
            left: 0;
            top: 0;
            background-color: white !important;
        }
        #paymentsTable tbody tr {
            background-color: white !important;
        }
        #paymentsTable tbody tr:nth-of-type(odd) {
            background-color: white !important;
        }
    }
    .popup-navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }
    .popup img {
        width: 300px;
        height: auto;
    }
    .popup-content {
        font-family: Arial, sans-serif;
        padding: 20px;
    }
    .popup-header {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 15px;
    }
    .popup-details {
        margin-bottom: 15px;
    }
    .popup-navigation {
        display: flex;
        justify-content: space-between;
    }
    .popup-navigation button {
        width: 48%;
    }
    .modal-content {
        background-color: #1e293b !important;
        color: #ffffff !important;
        border-radius: 12px !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4) !important;
    }

    .modal-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        padding: 15px 20px !important;
    }

    .modal-footer {
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        padding: 15px 20px !important;
    }

    .modal-title {
        color: #ffffff !important;
        font-weight: 600 !important;
    }

    .btn-close-white {
        filter: invert(1) grayscale(100%) brightness(200%);
    }

    /* Pagination Styles */
    .pagination-container {
        margin-top: 20px;
    }

    .pagination .page-link {
        background-color: #1e293b;
        color: #ffffff;
        border-color: #2d3748;
    }

    .pagination .page-item.active .page-link {
        background-color: #4a5568;
        border-color: #4a5568;
    }

    .pagination .page-item.disabled .page-link {
        background-color: #1a202c;
        color: #718096;
    }

    /* Loading indicator */
    #loadingTimeIndicator {
        padding: 8px;
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.2);
        margin-bottom: 15px;
        font-size: 14px;
    }
</style>

<script>
    // Global variables
    let currentComment = '';
    let currentPage = 1;
    let perPage = 20;
    let selectedGame = '';
    let selectedFoil = '';
    let selectedLetter = '';
    let stockCheckData = [];
    let currentStockIndex = 0;
    let stockCheckPopup = null;
    let users = [];
    let currentSort = {
        field: 'username',
        direction: 'asc'
    };

    // No initial data fetching - data will only be loaded when a specific section is clicked

    // Show FBT Payments details
    function showPaymentsDetails() {
        document.querySelector('.quick-access-section').style.display = 'none';
        document.getElementById('userDetails').style.display = 'none';
        document.getElementById('commentItems').style.display = 'none';
        document.getElementById('fbtDetails').style.display = 'none';
        document.getElementById('paymentsDetails').style.display = 'block';

        // Show loading indicator
        const paymentsTableBody = document.getElementById('paymentsTableBody');
        paymentsTableBody.innerHTML = '<tr><td colspan="10" class="text-center"><div class="spinner-border text-light" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>';

        // Fetch FBT payments data
        fetch('/admin/api/fbt-payments')
            .then(response => response.json())
            .then(data => {
                paymentsTableBody.innerHTML = '';

                if (data.length === 0) {
                    paymentsTableBody.innerHTML = '<tr><td colspan="10" class="text-center">No payment data found</td></tr>';
                    return;
                }

                data.forEach(payment => {
                    const row = `
                        <tr>
                            <td>${payment.username || 'N/A'}</td>
                            <td>${payment.fbtusername || 'N/A'}</td>
                            <td>£${payment.total_value.toFixed(2)}</td>
                            <td>${payment.total_count}</td>
                            <td>£${payment.platform_fee.toFixed(2)}</td>
                            <td>£${payment.fbt_fee.toFixed(2)}</td>
                            <td>£${payment.take_home.toFixed(2)}</td>
                            <td>£${payment.total_paid.toFixed(2)}</td>
                            <td>£${payment.balance_due.toFixed(2)}</td>
                            <td>
                                <button class="btn btn-sm btn-info" onclick="showUserPaymentDetails('${payment.fbtusername}')">
                                    <i class="fas fa-info-circle"></i> Details
                                </button>
                            </td>
                        </tr>
                    `;
                    paymentsTableBody.innerHTML += row;
                });
            })
            .catch(error => {
                console.error('Error fetching FBT payments:', error);
                paymentsTableBody.innerHTML = '<tr><td colspan="10" class="text-center text-danger">Error loading payment data. Please try again.</td></tr>';
            });
    }

    function sortUsers(field) {
        currentSort.direction = currentSort.field === field && currentSort.direction === 'asc' ? 'desc' : 'asc';
        currentSort.field = field;

        users.sort((a, b) => {
            let valueA = (a[field] || '').toLowerCase();
            let valueB = (b[field] || '').toLowerCase();

            if (currentSort.direction === 'asc') {
                return valueA.localeCompare(valueB);
            } else {
                return valueB.localeCompare(valueA);
            }
        });

        displayUsers(users);
    }

    // Global variables for pagination
    let currentUserPage = 1;
    let totalUserPages = 1;
    let userPageSize = 100;

    // Show user details - only loads data when clicked
    function showUserDetails(page = 1) {
        // Hide all other sections
        document.querySelector('.quick-access-section').style.display = 'none';
        document.getElementById('fbtDetails').style.display = 'none';
        document.getElementById('commentItems').style.display = 'none';
        document.getElementById('paymentsDetails').style.display = 'none';

        // Show user details section
        document.getElementById('userDetails').style.display = 'block';

        // Update current page
        currentUserPage = page;

        // Show loading indicator
        const userTableBody = document.getElementById('userTableBody');
        userTableBody.innerHTML = '<tr><td colspan="4" class="text-center"><div class="spinner-border text-light" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>';

        // Get filter values
        const subscriptionFilter = document.getElementById('subscriptionFilter').value || '';
        const usernameFilter = document.getElementById('usernameFilter').value || '';

        // Build query parameters
        const params = new URLSearchParams();
        if (usernameFilter) params.append('username', usernameFilter);
        if (subscriptionFilter) params.append('subscription', subscriptionFilter);
        params.append('page', currentUserPage);
        params.append('limit', userPageSize);



        // Fetch data with filters applied
        fetch(`/admin/api/users-with-subscriptions?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                // Update pagination variables
                totalUserPages = data.pagination.pages;

                // Store users data
                users = data.users;

                // Display the users
                displayUsers(users);

                // Update pagination UI
                updatePaginationUI();
            })
            .catch(error => {
                console.error('Error fetching user data:', error);
                userTableBody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">Error loading users. Please try again.</td></tr>';
            });
    }

    // Update pagination UI
    function updatePaginationUI() {
        // Check if pagination container exists, create if not
        let paginationContainer = document.getElementById('userPagination');
        if (!paginationContainer) {
            paginationContainer = document.createElement('div');
            paginationContainer.id = 'userPagination';
            paginationContainer.className = 'pagination-container d-flex justify-content-center mt-3';
            document.getElementById('userDetails').appendChild(paginationContainer);
        }

        // Clear existing pagination
        paginationContainer.innerHTML = '';

        // Don't show pagination if only one page
        if (totalUserPages <= 1) {
            return;
        }

        // Create pagination UI
        const pagination = document.createElement('ul');
        pagination.className = 'pagination';

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentUserPage === 1 ? 'disabled' : ''}`;
        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.textContent = 'Previous';
        prevLink.addEventListener('click', (e) => {
            e.preventDefault();
            if (currentUserPage > 1) {
                showUserDetails(currentUserPage - 1);
            }
        });
        prevLi.appendChild(prevLink);
        pagination.appendChild(prevLi);

        // Page numbers
        const maxPages = 5; // Show max 5 page numbers
        const startPage = Math.max(1, currentUserPage - Math.floor(maxPages / 2));
        const endPage = Math.min(totalUserPages, startPage + maxPages - 1);

        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === currentUserPage ? 'active' : ''}`;
            const pageLink = document.createElement('a');
            pageLink.className = 'page-link';
            pageLink.href = '#';
            pageLink.textContent = i;
            pageLink.addEventListener('click', (e) => {
                e.preventDefault();
                showUserDetails(i);
            });
            pageLi.appendChild(pageLink);
            pagination.appendChild(pageLi);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentUserPage === totalUserPages ? 'disabled' : ''}`;
        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.textContent = 'Next';
        nextLink.addEventListener('click', (e) => {
            e.preventDefault();
            if (currentUserPage < totalUserPages) {
                showUserDetails(currentUserPage + 1);
            }
        });
        nextLi.appendChild(nextLink);
        pagination.appendChild(nextLi);

        paginationContainer.appendChild(pagination);
    }

function displayUsers(userList) {
    const userTableBody = document.getElementById('userTableBody');
    userTableBody.innerHTML = '';

    if (userList.length === 0) {
        userTableBody.innerHTML = '<tr><td colspan="4" class="text-center">No users found matching the criteria</td></tr>';
        return;
    }

    userList.forEach(user => {
        // Create subscription dropdown
        const subscriptionOptions = [
            'Free', 'Trial', 'Monthly', 'Monthly Basic', 'Annual', 'Annual Basic', 'Enterprise', 'Lifetime', 'Suspended'
        ];

        let subscriptionDropdown = `<select class="form-control subscription-select" data-user-id="${user._id}">`;
        subscriptionOptions.forEach(option => {
            const selected = (user.subscription_name === option) ? 'selected' : '';
            subscriptionDropdown += `<option value="${option}" ${selected}>${option}</option>`;
        });
        subscriptionDropdown += '</select>';

        const row = `
            <tr>
                <td>${user.username || 'N/A'}</td>
                <td>${user.email || 'N/A'}</td>
                <td>${subscriptionDropdown}</td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-primary" onclick="loginAsUser('${user._id || ''}')"><i class="fas fa-sign-in-alt me-1"></i>Login</button>
                        <button class="btn btn-sm btn-info" onclick="sendEmailToUser('${user.email}')"><i class="fas fa-envelope me-1"></i>Email</button>
                        <button class="btn btn-sm btn-danger delete-user" data-user-id="${user._id}"><i class="fas fa-trash me-1"></i>Delete</button>
                    </div>
                </td>
            </tr>`;
        userTableBody.innerHTML += row;
    });

    // Add event listeners for subscription changes
    document.querySelectorAll('.subscription-select').forEach(select => {
        select.addEventListener('change', function() {
            updateUserSubscription(this.dataset.userId, this.value);
        });
    });

    // Add event listeners for delete buttons
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', function() {
            deleteUser(this.dataset.userId);
        });
    });
}

    function filterUsers() {
        // Reset to first page when filtering
        currentUserPage = 1;

        // Show user details with the new filters
        showUserDetails(1);
    }

    function updateUserSubscription(userId, subscriptionType) {
        // Show loading indicator
        const select = document.querySelector(`.subscription-select[data-user-id="${userId}"]`);
        const originalHtml = select.parentElement.innerHTML;
        select.parentElement.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"></div>';

        // Make API call to update subscription
        fetch('/admin/api/update-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                subscription_type: subscriptionType
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the UI
                select.parentElement.innerHTML = originalHtml;
                // Highlight the row briefly to indicate success
                const row = select.closest('tr');
                row.classList.add('table-success');
                setTimeout(() => {
                    row.classList.remove('table-success');
                }, 2000);
            } else {
                // Show error
                select.parentElement.innerHTML = originalHtml;
                alert(`Error: ${data.message || 'Failed to update subscription'}`);
            }
        })
        .catch(error => {
            console.error('Error updating subscription:', error);
            select.parentElement.innerHTML = originalHtml;
            alert('An error occurred while updating the subscription');
        });
    }

    function deleteUser(userId) {
        const username = document.querySelector(`button.delete-user[data-user-id="${userId}"]`).closest('tr').cells[0].textContent;

        if (confirm(`Are you sure you want to delete user ${username}? This action cannot be undone.`)) {
            fetch('/admin/api/delete-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('User deleted successfully');
                    // Refresh the user list
                    showUserDetails();
                } else {
                    alert(`Error: ${data.message || 'Failed to delete user'}`);
                }
            })
            .catch(error => {
                console.error('Error deleting user:', error);
                alert('An error occurred while deleting the user');
            });
        }
    }

    function sendEmailToUser(email) {
        // Pre-fill the email composer with the user's email
        document.getElementById('emailComposerModal').querySelector('#recipientList').innerHTML = `
            <div class="form-check text-light">
                <input class="form-check-input" type="checkbox" value="${email}" id="recipient_${email}" checked>
                <label class="form-check-label" for="recipient_${email}">
                    ${email}
                </label>
            </div>
        `;

        // Show the email composer modal
        $('#emailComposerModal').modal('show');
    }

    // Export users to CSV
    document.getElementById('exportUsersBtn').addEventListener('click', function() {
        // Get the filtered users
        const userTableBody = document.getElementById('userTableBody');
        const rows = userTableBody.querySelectorAll('tr');

        // Create CSV content
        let csvContent = 'Username,Email,Subscription,Start Date,End Date,Last Login\n';

        rows.forEach(row => {
            const username = row.cells[0].textContent;
            const email = row.cells[1].textContent;
            const subscription = row.cells[2].querySelector('select').value;
            const startDate = row.cells[3].textContent;
            const endDate = row.cells[4].textContent;
            const lastLogin = row.cells[5].textContent;

            csvContent += `"${username}","${email}","${subscription}","${startDate}","${endDate}","${lastLogin}"\n`;
        });

        // Create and download the CSV file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `users_export_${timestamp}.csv`;

        if (navigator.msSaveBlob) {
            navigator.msSaveBlob(blob, filename);
        } else {
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', filename);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    });

    // Add event listeners for quick access buttons and filter dropdowns
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listeners for quick access buttons
        document.getElementById('usersQuickAccess').addEventListener('click', function(e) {
            e.preventDefault();
            showUserDetails(1); // Always pass a number, not the event object
        });
        document.getElementById('paymentsQuickAccess').addEventListener('click', showPaymentsDetails);

        // Add event listeners for filter dropdowns
        const subscriptionFilter = document.getElementById('subscriptionFilter');
        if (subscriptionFilter) {
            subscriptionFilter.addEventListener('change', filterUsers);
        }
    });

    // Show items for a specific comment
    function showCommentItems(comment) {
        currentComment = comment;
        currentPage = 1;
        document.getElementById('commentTitle').textContent = `Comment Items (${comment})`;
        document.getElementById('fbtDetails').style.display = 'none';
        document.getElementById('commentItems').style.display = 'block';
        document.getElementById('commentItemsTable').innerHTML = '';

        fetchItemsForComment();
    }

    // Fetch items for a comment with pagination and filters
    function fetchItemsForComment() {
        const url = new URL('/admin/api/fbt-products', window.location.origin);
        url.searchParams.append('comment', currentComment);
        url.searchParams.append('page', currentPage);
        url.searchParams.append('per_page', perPage);
        if (selectedGame) {
            url.searchParams.append('product_idGame', selectedGame);
        }
        if (selectedFoil) {
            url.searchParams.append('isFoil', selectedFoil);
        }
        if (selectedLetter) {
            url.searchParams.append('letter', selectedLetter);
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('commentItemsTable');
                const items = data.data;

                items.sort((a, b) => (a.name || '').localeCompare(b.name || '')).forEach(item => {
                    const row = `
                        <tr>
                            <td>${item.name}</td>
                            <td>${item.product_nr}</td>
                            <td>${item.product_abbreviation}</td>
                            <td>${item.count}</td>
                            <td>${item.isFoil ? 'Yes' : 'No'}</td>
                            <td>
                                <div class="input-group">
                                    <input type="number" class="form-control update-stock-input" id="desiredStock_${item.idArticle}" placeholder="Stock">
                                    <button class="btn btn-success" onclick="updateStock('${item.idArticle}', ${item.count}, ${item.isFoil})">Update</button>
                                </div>
                            </td>
                        </tr>`;
                    tableBody.innerHTML += row;
                });

                if (currentPage * perPage >= data.total_records) {
                    document.getElementById('loadMoreBtn').style.display = 'none';
                } else {
                    document.getElementById('loadMoreBtn').style.display = 'block';
                }
            })
            .catch(error => console.error('Error fetching items for comment:', error));
    }

    // Apply filters and fetch items
    function applyFilter() {
        selectedGame = document.getElementById('product_idGame').value;
        selectedFoil = document.getElementById('isFoil').value;
        const filter = document.getElementById('itemFilter').value.toLowerCase();
        selectedLetter = filter ? filter.charAt(0) : '';
        currentPage = 1;
        document.getElementById('commentItemsTable').innerHTML = '';
        fetchItemsForComment();
    }

    // Apply letter filter
    function applyLetterFilter(letter) {
        selectedLetter = letter;
        currentPage = 1;
        document.getElementById('commentItemsTable').innerHTML = '';
        fetchItemsForComment();
    }

    // Load more items for the current comment
    function loadMoreItems() {
        currentPage++;
        fetchItemsForComment();
    }

    // Show main dashboard with just the quick access links
    function showMainDashboard() {
        // Hide all detail sections
        document.getElementById('userDetails').style.display = 'none';
        document.getElementById('commentItems').style.display = 'none';
        document.getElementById('fbtDetails').style.display = 'none';
        document.getElementById('paymentsDetails').style.display = 'none';

        // Show only the quick access section
        document.querySelector('.quick-access-section').style.display = 'block';
    }

    // Print filtered results
    function printFilteredResults() {
        const url = new URL('/admin/api/print-fbt-products', window.location.origin);
        url.searchParams.append('comment', currentComment);
        if (selectedGame) {
            url.searchParams.append('product_idGame', selectedGame);
        }
        if (selectedFoil) {
            url.searchParams.append('isFoil', selectedFoil);
        }
        if (selectedLetter) {
            url.searchParams.append('letter', selectedLetter);
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                const items = data.data;
                let printContent = `
                    <h2>Comment Items (${currentComment})</h2>
                    <table class="table table-striped table-dark" id="printTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Number</th>
                                <th>Set Code</th>
                                <th>QTY</th>
                                <th>Foil</th>
                            </tr>
                        </thead>
                        <tbody>`;

                items.sort((a, b) => (a.name || '').localeCompare(b.name || '')).forEach(item => {
                    printContent += `
                        <tr>
                            <td>${item.name}</td>
                            <td>${item.product_nr}</td>
                            <td>${item.product_abbreviation}</td>
                            <td>${item.count}</td>
                            <td>${item.isFoil ? 'Yes' : 'No'}</td>
                        </tr>`;
                });

                printContent += `</tbody></table>`;

                const originalContent = document.body.innerHTML;
                document.body.innerHTML = printContent;
                window.print();
                document.body.innerHTML = originalContent;
            })
            .catch(error => console.error('Error fetching full results for print:', error));
    }

    // Launch Stock Check Mode
    function launchStockCheckMode() {
        const url = new URL('/admin/api/print-fbt-products', window.location.origin);
        url.searchParams.append('comment', currentComment);
        if (selectedGame) {
            url.searchParams.append('product_idGame', selectedGame);
        }
        if (selectedFoil) {
            url.searchParams.append('isFoil', selectedFoil);
        }
        if (selectedLetter) {
            url.searchParams.append('letter', selectedLetter);
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                stockCheckData = data.data.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
                currentStockIndex = 0;
                openStockCheckPopup();
            })
            .catch(error => console.error('Error fetching items for stock check:', error));
    }

    function openStockCheckPopup() {
        if (!stockCheckPopup || stockCheckPopup.closed) {
            stockCheckPopup = window.open('', 'StockCheckPopup', 'width=600,height=800');
            stockCheckPopup.document.head.innerHTML = `
                <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
                <style>
                    body {
                        padding: 20px;
                        font-family: Arial, sans-serif;
                    }
                    .popup-content {
                        max-width: 500px;
                        margin: 0 auto;
                    }
                    .popup-header {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 15px;
                        text-align: center;
                    }
                    .popup-details {
                        background-color: #f8f9fa;
                        padding: 15px;
                        border-radius: 5px;
                        margin-bottom: 15px;
                    }
                    .stock-check-image {
                        max-height: 300px;
                        object-fit: contain;
                    }
                    .popup-navigation {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-top: 20px;
                    }
                    .stock-counter {
                        font-size: 16px;
                        font-weight: bold;
                    }
                </style>
            `;
        }

        updateStockCheckPopupContent();
    }

    function updateStockCheckPopupContent() {
        if (!stockCheckPopup || stockCheckPopup.closed) {
            return;
        }

        const item = stockCheckData[currentStockIndex];

        let popupContent = `
            <div class="popup-content">
                <h2 class="popup-header">${item.name}</h2>
                <div class="popup-details">
                    <p><strong>Number:</strong> ${item.product_nr}</p>
                    <p><strong>Set Code:</strong> ${item.product_abbreviation}</p>
                    <p><strong>QTY:</strong> ${item.count}</p>
                    <p><strong>Foil:</strong> ${item.isFoil ? 'Yes' : 'No'}</p>
                </div>
                <div class="text-center mb-3">
                    <img src="${item.image}" alt="${item.name}" class="img-fluid stock-check-image" />
                </div>
                <div class="popup-navigation">
                    <button id="prevButton" class="btn btn-secondary" ${currentStockIndex === 0 ? 'disabled' : ''}>Previous</button>
                    <span class="stock-counter">${currentStockIndex + 1} / ${stockCheckData.length}</span>
                    <button id="nextButton" class="btn btn-primary" ${currentStockIndex === stockCheckData.length - 1 ? 'disabled' : ''}>Next</button>
                </div>
            </div>
        `;

        stockCheckPopup.document.body.innerHTML = popupContent;

        // Add event listeners to the buttons
        stockCheckPopup.document.getElementById('prevButton').addEventListener('click', prevStockItem);
        stockCheckPopup.document.getElementById('nextButton').addEventListener('click', nextStockItem);
    }

    function nextStockItem() {
        if (currentStockIndex < stockCheckData.length - 1) {
            currentStockIndex++;
            updateStockCheckPopupContent();
        }
    }

    function prevStockItem() {
        if (currentStockIndex > 0) {
            currentStockIndex--;
            updateStockCheckPopupContent();
        }
    }

    function prevRecord() {
        if (currentPage > 1) {
            currentPage--;
            fetchItemsForComment();
        }
    }

    // Update stock
    function updateStock(idArticle, currentStock, isFoil) {
        const desiredStock = parseInt(document.getElementById(`desiredStock_${idArticle}`).value);
        if (isNaN(desiredStock)) {
            alert('Please enter a valid number for desired stock.');
            return;
        }

        const difference = desiredStock - currentStock;
        if (difference === 0) {
            alert('The desired stock is equal to the current stock.');
            return;
        }

        const operation = difference > 0 ? 'increase' : 'decrease';
        const amount = Math.abs(difference);

        fetch('/admin/api/update-stock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                idArticle,
                currentStock,
                desiredStock,
                isFoil
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error(`Error updating stock: ${data.error}`);
                alert(`Error updating stock: ${data.error}`);
            } else {
                console.log(`Stock updated successfully: ${data.message}`);
                alert(`Stock updated successfully: ${data.message}`);
                fetchItemsForComment(); // Refresh the current page to reflect the updated stock
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert(`An error occurred while updating stock: ${error}`);
        });
    }

    // Show payments details
    function showPaymentsDetails() {
        document.querySelector('.quick-access-section').style.display = 'none';
        document.getElementById('fbtDetails').style.display = 'none';
        document.getElementById('commentItems').style.display = 'none';
        document.getElementById('userDetails').style.display = 'none';
        document.getElementById('paymentsDetails').style.display = 'block';
        loadFBTUserTotals();
    }

    // Load FBT user totals
    function loadFBTUserTotals() {
        fetch('/admin/api/fbt-user-totals')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('paymentsTableBody');
                tableBody.innerHTML = '';
                let totalFbtFeeCurrentMonth = 0;
                const currentDate = new Date();
                const currentMonth = currentDate.getMonth();
                const currentYear = currentDate.getFullYear();

                data.forEach(user => {
                    const row = `
                        <tr>
                            <td>${user.username}</td>
                            <td>${user.fbtusername}</td>
                            <td>£${user.total_value.toFixed(2)}</td>
                            <td>${user.total_count}</td>
                            <td>£${user.platform_fee.toFixed(2)}</td>
                            <td>£${user.fbt_fee.toFixed(2)}</td>
                            <td>£${user.take_home.toFixed(2)}</td>
                            <td>£${user.total_paid.toFixed(2)}</td>
                            <td>£${user.balance_due.toFixed(2)}</td>
                            <td>
                                <button class="btn btn-sm btn-info" onclick="showUserDetailsByFbtUsername('${user.fbtusername}')">Details</button>
                                <button class="btn btn-sm btn-success" onclick="showAddPaymentModal('${user.user_id}', '${user.fbtusername}')">Add Payment</button>
                            </td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;

                    // Calculate total FBT fee for the current month
                    if (user.last_transaction_date) {
                        const transactionDate = new Date(user.last_transaction_date);
                        if (transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear) {
                            totalFbtFeeCurrentMonth += user.fbt_fee;
                        }
                    }
                });

            })
            .catch(error => {
                console.error('Error loading FBT totals:', error);
                alert('An error occurred while loading the FBT totals. Please try again.');
            });
    }

    // Function to show user details for a specific FBT username
    function showUserDetailsByFbtUsername(fbtusername) {
        fetch(`/admin/api/user-details/${fbtusername}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Show user details in modal
                $('#userDetailsModal').modal('show');
                loadTransactions(fbtusername);
            })
            .catch(error => {
                console.error('Error loading user details:', error);
                alert('An error occurred while loading the user details. Please try again.');
            });
    }

    function loadTransactions(fbtusername) {
        fetch(`/admin/api/get-transactions/${fbtusername}`)
            .then(response => response.json())
            .then(data => {
                const transactionsBody = document.getElementById('transactionsBody');
                transactionsBody.innerHTML = '';
                data.transactions.forEach(transaction => {
                    const row = `
                        <tr>
                            <td>${transaction.date}</td>
                            <td>£${transaction.amount.toFixed(2)}</td>
                            <td>${transaction.type}</td>
                        </tr>
                    `;
                    transactionsBody.innerHTML += row;
                });
            })
            .catch(error => {
                console.error('Error loading transactions:', error);
                alert('An error occurred while loading the transactions. Please try again.');
            });
    }

    // Add transaction
    document.getElementById('addTransactionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const fbtusername = document.getElementById('transactionFbtUsername').value;
        const amount = document.getElementById('transactionAmount').value;
        const date = document.getElementById('transactionDate').value;
        const type = document.getElementById('transactionType').value;

        fetch('/admin/api/add-transaction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                fbtusername: fbtusername,
                amount: amount,
                date: date,
                type: type
            }),
        })
        .then(response => response.json())
        .then(data => {
            alert('Transaction added successfully');
            loadTransactions(fbtusername);
            document.getElementById('addTransactionForm').reset();
        })
        .catch((error) => {
            console.error('Error:', error);
            alert('An error occurred while adding the transaction');
        });
    });

    // Initialize event listeners
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listeners for quick access cards
        document.getElementById('usersQuickAccess').addEventListener('click', function(e) {
            e.preventDefault();
            showUserDetails(1); // Always pass a number, not the event object
        });
        document.getElementById('paymentsQuickAccess').addEventListener('click', showPaymentsDetails);

        // Other event listeners
        document.getElementById('product_idGame').addEventListener('change', applyFilter);
        document.getElementById('isFoil').addEventListener('change', applyFilter);
        document.getElementById('loadMoreBtn').addEventListener('click', loadMoreItems);
        document.getElementById('prevBtn').addEventListener('click', prevRecord);
        document.getElementById('printBtn').addEventListener('click', printFilteredResults);
        document.getElementById('stockCheckBtn').addEventListener('click', launchStockCheckMode);
        document.getElementById('composeEmailBtn').addEventListener('click', showEmailComposer);
    });

    function maskBankDetail(value) {
        if (typeof value !== 'string') return value;
        const visibleChars = 4;
        const maskedPart = '*'.repeat(Math.max(0, value.length - visibleChars));
        return maskedPart + value.slice(-visibleChars);
    }

    function loginAsUser(userId) {
        if (!userId || userId === 'undefined') {
            console.error('Invalid user ID:', userId);
            alert('Invalid user ID. Please try again or contact support.');
            return;
        }
        if (confirm('Are you sure you want to login as this user?')) {
            fetch(`/admin/login-as-user/${encodeURIComponent(userId)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    console.error('Error:', data.error);
                    alert('Error: ' + data.error);
                }
            })
            .catch((error) => {
                console.error('Error:', error);
                alert('An error occurred while logging in as the user. Please try again or contact support.');
            });
        }
    }

    // Add this to your existing DOMContentLoaded event listener
    document.addEventListener('DOMContentLoaded', function() {
        // ... existing code ...

        // Check if there's an admin session and show the "Return to Admin" button if needed
        if (sessionStorage.getItem('admin_user_id')) {
            const returnButton = document.createElement('button');
            returnButton.textContent = 'Return to Admin';
            returnButton.className = 'btn btn-warning';
            returnButton.onclick = returnToAdmin;
            document.body.insertBefore(returnButton, document.body.firstChild);
        }
    });

    function returnToAdmin() {
        fetch('/admin/return-to-admin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                sessionStorage.removeItem('admin_user_id');
                window.location.href = data.redirect;
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch((error) => {
            console.error('Error:', error);
            alert('An error occurred while returning to admin');
        });
    }
</script>

<!-- Email Composer Modal -->
<div class="modal fade" id="emailComposerModal" tabindex="-1" role="dialog" aria-labelledby="emailComposerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content bg-dark text-light">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="emailComposerModalLabel">Compose Email</h5>
                <button type="button" class="close text-light" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="emailForm">
                    <div class="form-group">
                        <label for="emailSubject">Subject</label>
                        <input type="text" class="form-control bg-secondary text-light" id="emailSubject" required>
                    </div>
                    <div class="form-group">
                        <label for="emailBody">Message</label>
                        <textarea class="form-control bg-secondary text-light" id="emailBody" rows="10" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="activationFilter">Filter by Activation Status</label>
                        <select id="activationFilter" class="form-control bg-secondary text-light">
                            <option value="all">All Users</option>
                            <option value="true">Activated Users</option>
                            <option value="false">Non-Activated Users</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-secondary" onclick="addEmailMarketingLists()">Add Email Marketing Lists</button>
                    </div>
                    <div class="form-group">
                        <label>Select Recipients</label>
                        <div id="recipientList" class="bg-secondary p-2 rounded"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-secondary" onclick="sendTestEmail()">Send Test Email</button>
                <button type="button" class="btn btn-primary" onclick="sendEmail()">Send Email to All</button>
            </div>
        </div>
    </div>
</div>

<script>
function showEmailComposer() {
    const recipientList = document.getElementById('recipientList');
    recipientList.innerHTML = '';
    const activationFilter = document.getElementById('activationFilter').value;

    users.forEach(user => {
        if (activationFilter === 'all' ||
            (activationFilter === 'true' && user.activated) ||
            (activationFilter === 'false' && !user.activated)) {
            const checkbox = document.createElement('div');
            checkbox.className = 'form-check text-light';
            checkbox.innerHTML = `
                <input class="form-check-input" type="checkbox" value="${user.email}" id="recipient_${user.username}">
                <label class="form-check-label" for="recipient_${user.username}">
                    ${user.username} (${user.email})
                </label>
            `;
            recipientList.appendChild(checkbox);
        }
    });
    $('#emailComposerModal').modal('show');
}

function addEmailMarketingLists() {
    fetch('/admin/api/email-marketing-lists')
        .then(response => response.json())
        .then(data => {
            const recipientList = document.getElementById('recipientList');
            data.lists.forEach(list => {
                fetch(`/admin/api/email-marketing-list/${list._id}`)
                    .then(response => response.json())
                    .then(listData => {
                        listData.emails.forEach(email => {
                            if (!document.getElementById(`recipient_${email}`)) {
                                const checkbox = document.createElement('div');
                                checkbox.className = 'form-check text-light';
                                checkbox.innerHTML = `
                                    <input class="form-check-input" type="checkbox" value="${email}" id="recipient_${email}">
                                    <label class="form-check-label" for="recipient_${email}">
                                        ${email} (Marketing List: ${list.name})
                                    </label>
                                `;
                                recipientList.appendChild(checkbox);
                            }
                        });
                    })
                    .catch(error => console.error('Error fetching email marketing list:', error));
            });
        })
        .catch(error => console.error('Error fetching email marketing lists:', error));
}

document.getElementById('activationFilter').addEventListener('change', showEmailComposer);

async function sendTestEmail() {
    const subject = document.getElementById('emailSubject').value;
    const body = document.getElementById('emailBody').value;
    const activationFilter = document.getElementById('activationFilter').value;

    if (!subject || !body) {
        alert('Please fill in both subject and body fields.');
        return;
    }

    try {
        const response = await fetch('/admin/send_test_email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ subject, body, activationFilter })
        });

        if (response.ok) {
            alert('Test email sent successfully!');
        } else {
            const errorData = await response.json();
            alert(`Failed to send test email: ${errorData.error}`);
        }
    } catch (error) {
        console.error('Error sending test email:', error);
        alert('An error occurred while sending the test email. Please try again.');
    }
}

async function sendEmail() {
    const subject = document.getElementById('emailSubject').value;
    const body = document.getElementById('emailBody').value;
    const recipients = Array.from(document.querySelectorAll('#recipientList input:checked')).map(input => input.value);
    const activationFilter = document.getElementById('activationFilter').value;

    if (!subject || !body || recipients.length === 0) {
        alert('Please fill in all fields and select at least one recipient.');
        return;
    }

    if (!confirm('Are you sure you want to send this email to all selected recipients?')) {
        return;
    }

    try {
        const response = await fetch('/admin/send_email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ subject, body, recipients, activationFilter })
        });

        if (response.ok) {
            alert('Email sent successfully to all recipients!');
            $('#emailComposerModal').modal('hide');
        } else {
            const errorData = await response.json();
            alert(`Failed to send email: ${errorData.error}`);
        }
    } catch (error) {
        console.error('Error sending email:', error);
        alert('An error occurred while sending the email. Please try again.');
    }
}

// This function is already defined above

document.addEventListener('DOMContentLoaded', function() {
});

document.getElementById('activationFilter').addEventListener('change', showEmailComposer);
</script>

<!-- Add Payment Modal -->
<div class="modal fade" id="addPaymentModal" tabindex="-1" role="dialog" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content bg-dark text-light">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="addPaymentModalLabel">Add Payment</h5>
                <button type="button" class="close text-light" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addPaymentForm">
                    <input type="hidden" id="paymentUserId">
                    <input type="hidden" id="paymentFbtUsername">
                    <div class="form-group">
                        <label for="paymentAmount">Amount</label>
                        <input type="number" step="0.01" class="form-control bg-secondary text-light" id="paymentAmount" required>
                    </div>
                    <div class="form-group">
                        <label for="paymentDate">Date</label>
                        <input type="date" class="form-control bg-secondary text-light" id="paymentDate" required>
                    </div>
                    <div class="form-group">
                        <label for="paymentType">Type</label>
                        <select class="form-control bg-secondary text-light" id="paymentType" required>
                            <option value="payment">Payment</option>
                            <option value="refund">Refund</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="addPayment()">Add Payment</button>
            </div>
        </div>
    </div>
</div>

<script>
function showAddPaymentModal(userId, fbtUsername) {
    document.getElementById('paymentUserId').value = userId;
    document.getElementById('paymentFbtUsername').value = fbtUsername;
    document.getElementById('paymentDate').valueAsDate = new Date();
    $('#addPaymentModal').modal('show');
}

function addPayment() {
    const fbtusername = document.getElementById('paymentFbtUsername').value;
    const amount = document.getElementById('paymentAmount').value;
    const date = document.getElementById('paymentDate').value;
    const type = document.getElementById('paymentType').value;

    fetch('/admin/api/add-transaction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            fbtusername: fbtusername,
            amount: amount,
            date: date,
            type: type
        }),
    })
    .then(response => response.json())
    .then(data => {
        alert('Payment added successfully');
        $('#addPaymentModal').modal('hide');
        loadFBTUserTotals(); // Refresh the table
    })
    .catch((error) => {
        console.error('Error:', error);
        alert('An error occurred while adding the payment');
    });
}

function upgradeUserAccounts() {
    if (confirm('Are you sure you want to upgrade all user accounts? This action cannot be undone.')) {
        fetch('/admin/api/upgrade-user-accounts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            alert('User accounts upgraded successfully');
            // Optionally, refresh the user list or perform any other necessary updates
        })
        .catch((error) => {
            console.error('Error:', error);
            alert('An error occurred while upgrading user accounts');
        });
    }
}
</script>



{% endblock %}
