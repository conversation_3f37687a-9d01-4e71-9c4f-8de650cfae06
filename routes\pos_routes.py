import re
from flask import Blueprint, render_template, request, jsonify, current_app, session
from flask_login import login_required, current_user
from pymongo import MongoClient, DESCENDING
import os
import requests
from models.pos_model import PosSetting, Transaction, Layaway, Employee, Currency, HeldSale, Quicklink, SavedCategorySearch, PaymentMethod, TillClosing
from models.pos_sale_model import POSSale, PaymentMethodDetail
from datetime import datetime, timedelta
from bson import ObjectId
from bson.errors import InvalidId
import logging
from routes.payment_processor import process_card_payment
from tasks import process_pos_transaction

# MongoDB client setup
mongo_client = MongoClient(os.environ.get('MONGO_URI', '*******************************************************************'))
db = mongo_client[os.environ.get('MONGO_DBNAME', 'test')]
reserved_items_collection = db['reservedItems']
user_collection = db['users']

# Define the blueprint
pos_bp = Blueprint('pos', __name__)

def adjust_shopify_inventory_for_reserved_items(items, adjustment, username):
    """
    Adjust Shopify inventory for items being held or in layaway

    Args:
        items (list): List of item dictionaries containing variant_id and quantity
        adjustment (int): Amount to adjust (negative for decrease, positive for increase)
        username (str): Username of the store owner
    """
    current_app.logger.info(f"Starting Shopify inventory adjustment for {len(items)} items, adjustment={adjustment}, username={username}")

    # Get user's Shopify credentials from the User model
    from models.user_model import User
    user = User.objects(username=username).first()
    if not user:
        current_app.logger.error(f"No user found with username: {username}")
        return False

    store_name = user.shopifyStoreName.strip().replace('.myshopify.com', '')
    access_token = user.shopifyAccessToken.strip()

    current_app.logger.info(f"Using Shopify store: {store_name}")

    if not store_name or not access_token:
        current_app.logger.error(f"Missing Shopify credentials for user {username}")
        return False

    # Set up Shopify headers
    shopify_headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": access_token
    }

    # Get all locations from Shopify API
    locations = []
    try:
        locations_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/locations.json"
        current_app.logger.info(f"Fetching Shopify locations from: {locations_url}")
        locations_response = requests.get(locations_url, headers=shopify_headers)
        locations_response.raise_for_status()
        locations = locations_response.json().get('locations', [])
        current_app.logger.info(f"Found {len(locations)} Shopify locations")

        if not locations:
            current_app.logger.error(f"No locations found for Shopify store")
            return False

        # Filter for active locations
        active_locations = [loc for loc in locations if loc.get('active', False)]
        if not active_locations:
            current_app.logger.error(f"No active locations found for Shopify store")
            return False
    except Exception as e:
        current_app.logger.error(f"Error getting Shopify locations: {str(e)}")
        if hasattr(e, 'response') and e.response:
            current_app.logger.error(f"Response status: {e.response.status_code}, body: {e.response.text}")
        return False

    # Get default location ID - First check if the user has a preferred location set in PosSetting
    default_location_id = None
    try:
        # Check if user has a preferred location in PosSetting
        from models.pos_model import PosSetting
        pos_setting = PosSetting.objects(username=username).first()
        if pos_setting and pos_setting.shopify_location_id:
            # Verify this location ID is in our active locations
            location_exists = any(str(loc['id']) == str(pos_setting.shopify_location_id) for loc in active_locations)
            if location_exists:
                default_location_id = pos_setting.shopify_location_id
                current_app.logger.info(f"Using preferred location ID from PosSetting: {default_location_id}")

        # If no preferred location, use the first active location
        if not default_location_id and active_locations:
            default_location_id = active_locations[0]['id']
            current_app.logger.info(f"Using first active location as default: {default_location_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting default location: {str(e)}")
        # Continue with active_locations even if we couldn't get a default

    if not default_location_id and active_locations:
        default_location_id = active_locations[0]['id']

    if not default_location_id:
        current_app.logger.error(f"Failed to determine default location ID for inventory adjustment")
        return False

    # Create a map of location IDs to location details for quick lookup
    location_map = {str(loc['id']): loc for loc in locations}

    # Process each item
    success = True
    for item in items:
        # Skip custom items (they don't affect inventory)
        is_custom = item.get("is_custom") or (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
        if is_custom:
            current_app.logger.info(f"Skipping custom item: {item}")
            continue

        variant_id = item["variant_id"]
        quantity = item["quantity"]

        current_app.logger.info(f"Processing variant_id: {variant_id}, quantity: {quantity}")

        # Get inventory_item_id for this variant
        try:
            # Normalize variant_id
            normalized_variant_id = str(variant_id)
            current_app.logger.info(f"Normalized variant_id: {normalized_variant_id}")

            # Try to find the variant with different ID formats
            # First try with string and numeric formats in a single query
            possible_id_formats = [normalized_variant_id]

            # Add numeric format if the ID is a digit string
            if normalized_variant_id.isdigit():
                possible_id_formats.append(int(normalized_variant_id))

            # Use a single query with $in operator for string and numeric formats
            current_app.logger.info(f"Querying for variant with string/numeric formats: {possible_id_formats}")
            variant_info = db.shProducts.find_one(
                {
                    "username": username,
                    "variants.id": {"$in": possible_id_formats}
                },
                {"variants.$": 1}
            )

            # If not found, try with $numberLong format in a separate query
            if not variant_info:
                current_app.logger.info(f"Trying with $numberLong format")
                variant_info = db.shProducts.find_one(
                    {
                        "username": username,
                        "variants.id": {"$numberLong": normalized_variant_id}
                    },
                    {"variants.$": 1}
                )

            if not variant_info or not variant_info.get('variants'):
                current_app.logger.error(f"Variant info not found for variant {variant_id}")
                success = False
                continue

            current_app.logger.info(f"Variant info: {variant_info}")

            inventory_item_id = variant_info['variants'][0].get('inventory_item_id')
            if not inventory_item_id:
                current_app.logger.error(f"No inventory_item_id found for variant {variant_id}")
                success = False
                continue

            current_app.logger.info(f"Raw inventory_item_id: {inventory_item_id}")

            # Normalize inventory_item_id
            if isinstance(inventory_item_id, dict) and '$numberLong' in inventory_item_id:
                inventory_item_id = str(inventory_item_id['$numberLong'])
            else:
                inventory_item_id = str(inventory_item_id)

            current_app.logger.info(f"Normalized inventory_item_id: {inventory_item_id}")

            # Get inventory levels for this inventory item to find the best location
            best_location_id = None
            try:
                inventory_levels_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/inventory_levels.json?inventory_item_ids={inventory_item_id}"
                inventory_levels_response = requests.get(inventory_levels_url, headers=shopify_headers)
                inventory_levels_response.raise_for_status()
                inventory_levels = inventory_levels_response.json().get('inventory_levels', [])

                if inventory_levels:
                    current_app.logger.info(f"Found {len(inventory_levels)} inventory levels for item {inventory_item_id}")

                    # For decreasing inventory (adjustment < 0), find location with highest available quantity
                    # For increasing inventory (adjustment > 0), use any active location
                    if adjustment < 0:
                        # Sort by available quantity (highest first)
                        inventory_levels.sort(key=lambda x: x.get('available', 0), reverse=True)

                        # Find the first level with sufficient quantity at an active location
                        for level in inventory_levels:
                            level_location_id = str(level.get('location_id'))
                            location_info = location_map.get(level_location_id)

                            if location_info and location_info.get('active', False):
                                available = level.get('available', 0)
                                if available >= quantity:
                                    best_location_id = level_location_id
                                    current_app.logger.info(f"Using location {best_location_id} with {available} available items")
                                    break

                        # If no location has sufficient quantity, use the one with the most available
                        if not best_location_id and inventory_levels:
                            for level in inventory_levels:
                                level_location_id = str(level.get('location_id'))
                                location_info = location_map.get(level_location_id)

                                if location_info and location_info.get('active', False):
                                    best_location_id = level_location_id
                                    current_app.logger.info(f"Using location {best_location_id} with highest available quantity")
                                    break
                    else:
                        # For increasing inventory, find any active location that already has this item
                        for level in inventory_levels:
                            level_location_id = str(level.get('location_id'))
                            location_info = location_map.get(level_location_id)

                            if location_info and location_info.get('active', False):
                                best_location_id = level_location_id
                                current_app.logger.info(f"Using location {best_location_id} for inventory increase")
                                break
            except Exception as e:
                current_app.logger.error(f"Error getting inventory levels: {str(e)}")
                # Continue with default location

            # If we couldn't find a suitable location from inventory levels, use the default
            if not best_location_id:
                best_location_id = default_location_id
                current_app.logger.info(f"Using default location ID: {best_location_id}")

            # Calculate adjustment value (negative for hold, positive for restore)
            adjustment_value = adjustment * quantity

            # Adjust inventory in Shopify
            inventory_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/inventory_levels/adjust.json"
            inventory_data = {
                "inventory_item_id": inventory_item_id,
                "location_id": best_location_id,
                "available_adjustment": adjustment_value
            }

            current_app.logger.info(f"Adjusting Shopify inventory with data: {inventory_data}")
            current_app.logger.info(f"Sending request to: {inventory_url}")

            # Add retry logic for inventory adjustment
            max_retries = 3
            retry_delay = 1  # seconds
            item_success = False

            for attempt in range(max_retries):
                try:
                    inventory_response = requests.post(inventory_url, json=inventory_data, headers=shopify_headers)

                    # Log the full response for debugging
                    current_app.logger.info(f"Shopify response status: {inventory_response.status_code}")
                    current_app.logger.info(f"Shopify response body: {inventory_response.text}")

                    # Check for specific error messages that might indicate location issues
                    if inventory_response.status_code == 422:
                        error_text = inventory_response.text.lower()
                        if "location" in error_text or "inventory" in error_text:
                            current_app.logger.error(f"Possible location or inventory issue: {inventory_response.text}")
                            # Try to get a different location if this is the first attempt
                            if attempt == 0:
                                current_app.logger.info("Attempting to find alternative location")
                                try:
                                    # Try a different active location
                                    for loc in active_locations:
                                        if str(loc['id']) != str(best_location_id):
                                            best_location_id = loc['id']
                                            inventory_data["location_id"] = best_location_id
                                            current_app.logger.info(f"Trying alternative location ID: {best_location_id}")
                                            break
                                except Exception as loc_err:
                                    current_app.logger.error(f"Error getting alternative locations: {str(loc_err)}")
                            continue  # Retry with new location or same location

                    inventory_response.raise_for_status()
                    item_success = True
                    break  # Success, exit retry loop

                except requests.RequestException as req_err:
                    current_app.logger.error(f"Request error on attempt {attempt+1}: {str(req_err)}")
                    if attempt < max_retries - 1:
                        current_app.logger.info(f"Retrying in {retry_delay} seconds...")
                        import time
                        time.sleep(retry_delay)
                    else:
                        current_app.logger.error(f"Failed after {max_retries} attempts")
                        raise

            if not item_success:
                raise Exception("Failed to adjust inventory after multiple attempts")

            current_app.logger.info(f"Successfully adjusted Shopify inventory for variant {variant_id}")

        except Exception as e:
            current_app.logger.error(f"Error adjusting Shopify inventory for variant {variant_id}: {str(e)}")
            if hasattr(e, 'response') and e.response:
                current_app.logger.error(f"Response status: {e.response.status_code}, body: {e.response.text}")
            success = False

    current_app.logger.info(f"Completed Shopify inventory adjustment with success={success}")
    return success

@pos_bp.route('/pos')
@login_required
def pos():
    tills = PosSetting.objects(username=current_user.username).all()
    current_date = datetime.utcnow().strftime('%Y-%m-%d')
    return render_template('pos.html', tills=tills, current_date=current_date)

@pos_bp.route('/pos/get_shopify_locations', methods=['GET'])
@login_required
def get_shopify_locations():
    """
    Fetch all locations from Shopify for the current user's store.
    This is used for selecting a location when creating or editing a till.
    """
    try:
        # Set up Shopify API request
        shopify_headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": current_user.shopifyAccessToken
        }

        # Get the locations from Shopify
        locations_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/locations.json"
        locations_response = requests.get(locations_url, headers=shopify_headers)
        locations_response.raise_for_status()
        locations = locations_response.json().get('locations', [])

        # Format the locations for the response
        formatted_locations = []
        for location in locations:
            formatted_locations.append({
                "id": str(location['id']),
                "name": location['name'],
                "address1": location.get('address1', ''),
                "address2": location.get('address2', ''),
                "city": location.get('city', ''),
                "zip": location.get('zip', ''),
                "country": location.get('country', ''),
                "active": location.get('active', False)
            })

        return jsonify({
            "success": True,
            "locations": formatted_locations
        }), 200
    except requests.RequestException as e:
        current_app.logger.error(f"Error fetching Shopify locations: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error fetching Shopify locations: {str(e)}"
        }), 500
    except Exception as e:
        current_app.logger.error(f"Unexpected error fetching Shopify locations: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Unexpected error: {str(e)}"
        }), 500

@pos_bp.route('/pos/create_till', methods=['POST'])
@login_required
def create_till():
    data = request.json
    location = data.get('location')
    starting_float = data.get('starting_float')
    tax_rate = data.get('tax_rate')
    tax_inclusive = data.get('tax_inclusive', False)  # Default to exclusive if not provided

    # Shopify location fields
    shopify_location_id = data.get('shopify_location_id')
    shopify_location_name = data.get('shopify_location_name')

    # Merchant Match Card Machine fields
    tpn = data.get('tpn')
    register_id = data.get('register_id')
    auth_key = data.get('auth_key')

    if not location or starting_float is None or tax_rate is None:
        return jsonify({"error": "Location, starting float, and tax rate are required"}), 400

    till = PosSetting(
        location=location,
        starting_float=starting_float,
        running_total=starting_float,
        tax_rate=tax_rate,
        tax_inclusive=tax_inclusive,
        username=current_user.username,
        shopify_location_id=shopify_location_id,
        shopify_location_name=shopify_location_name,
        tpn=tpn,
        register_id=register_id,
        auth_key=auth_key
    )
    till.save()
    return jsonify({
        "id": str(till.id),
        "location": till.location,
        "starting_float": till.starting_float,
        "running_total": till.running_total,
        "tax_rate": till.tax_rate,
        "tax_inclusive": till.tax_inclusive,
        "shopify_location_id": till.shopify_location_id,
        "shopify_location_name": till.shopify_location_name,
        "tpn": till.tpn,
        "register_id": till.register_id,
        "auth_key": till.auth_key
    }), 201

@pos_bp.route('/pos/start_pos/<till_id>')
@login_required
def start_pos(till_id):
    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return "Invalid till ID", 400
    except PosSetting.DoesNotExist:
        return "Till not found", 404

    # Store the till_id in the session for later use
    session['current_till_id'] = till_id
    current_app.logger.info(f"Stored till_id in session: {till_id}")

    # Fetch quicklinks for this till
    quicklinks = Quicklink.objects(till=till, username=current_user.username).order_by('quicklink_number')

    # Ensure we have exactly 4 quicklinks, filling with None if necessary
    quicklink_products = [ql.product_name if ql else None for ql in quicklinks] + [None] * (4 - len(quicklinks))

    # Update the last_used timestamp and staff information if not already set
    if not till.current_staff:
        till.current_staff = current_user.username
        till.staff_name = getattr(current_user, 'name', current_user.username)
        till.last_used = datetime.utcnow()
        till.save()

    # Pass staff information to the template
    staff_info = {
        'username': till.current_staff,
        'name': till.staff_name,
        'last_used': till.last_used
    }

    return render_template('pos_start.html', till=till, quicklinks=quicklink_products[:4], staff_info=staff_info)

@pos_bp.route('/pos/search_products')
@login_required
def search_products():
    query = request.args.get('query', '')
    search_type = request.args.get('search_type', 'title')
    in_stock_only = request.args.get('in_stock_only', 'false').lower() == 'true'
    username = current_user.username

    pipeline = []

    # Match stage for basic search conditions
    match_stage = {
        "username": username,
    }

    if search_type == 'barcode':
        try:
            barcode_as_int = int(query)
            match_stage["$or"] = [
                {"variants.barcode": {"$regex": query, "$options": "i"}},
                {"variants.barcode": query},
                {"variants.barcode": barcode_as_int}
            ]
        except ValueError:
            match_stage[search_type] = {"$regex": query, "$options": "i"}
    else:
        match_stage[search_type] = {"$regex": query, "$options": "i"}

    pipeline.append({"$match": match_stage})

    # If in_stock_only is true, add a match stage for inventory
    if in_stock_only:
        pipeline.append({"$match": {"variants": {"$elemMatch": {"inventory_quantity": {"$gt": 0}}}}})

    # No limit on search results - return all matching products
    products = db.shProducts.aggregate(pipeline)

    result = []
    for product in products:
        # Check if product is a dictionary before using .get() method
        if not isinstance(product, dict):
            current_app.logger.error(f"Unexpected product type: {type(product)}, value: {product}")
            continue

        # Handle image URL whether it's a dictionary with 'src' or directly a string
        image = product.get('image', '')
        if isinstance(image, dict):
            image_src = image.get('src', '')
        else:
            image_src = image if isinstance(image, str) else ''
        variants = product.get('variants', [])
        if in_stock_only:
            variants = [variant for variant in variants if variant.get('inventory_quantity', 0) > 0]
        result.append({
            "id": str(product['_id']),
            "title": product.get('title', 'No Title'),
            "expansionName": product.get('expansionName', 'Unknown Expansion'),
            "rarity": product.get('rarity', 'Unknown Rarity'),
            "image": image_src,
            "variants": [
                {
                    "id": str(variant['id']['$numberLong']) if isinstance(variant['id'], dict) else str(variant['id']),
                    "title": variant.get('title', 'No Title'),
                    "price": variant.get('price', '0.00'),
                    "barcode": variant.get('barcode', 'No Barcode'),
                    "inventory_quantity": variant.get('inventory_quantity', 0)
                } for variant in variants
            ]
        })
    return jsonify(result)

@pos_bp.route('/pos/get_distinct_vendors')
@login_required
def get_distinct_vendors():
    # Use aggregation pipeline for better performance with large collections
    pipeline = [
        {"$match": {"username": current_user.username}},
        {"$group": {"_id": "$vendor"}},
        {"$match": {"_id": {"$ne": None}}},  # Filter out null vendors
        {"$sort": {"_id": 1}}  # Sort alphabetically
    ]

    vendors_cursor = db.shProducts.aggregate(pipeline)
    vendors = [doc["_id"] for doc in vendors_cursor if doc["_id"]]

    return jsonify({"success": True, "vendors": vendors})

@pos_bp.route('/pos/get_product_types')
@login_required
def get_product_types():
    vendor = request.args.get('vendor', '')

    # Use aggregation pipeline for better performance
    pipeline = [
        {"$match": {"username": current_user.username}}
    ]

    # If vendor is provided, filter by vendor
    if vendor:
        pipeline[0]["$match"]["vendor"] = vendor

    # Group by product_type and sort
    pipeline.extend([
        {"$group": {"_id": "$product_type"}},
        {"$match": {"_id": {"$ne": None}}},  # Filter out null product types
        {"$sort": {"_id": 1}}  # Sort alphabetically
    ])

    product_types_cursor = db.shProducts.aggregate(pipeline)
    product_types = [doc["_id"] for doc in product_types_cursor if doc["_id"]]

    return jsonify({"success": True, "product_types": product_types})

@pos_bp.route('/pos/get_products_by_category')
@login_required
def get_products_by_category():
    vendor = request.args.get('vendor', '')
    product_type = request.args.get('product_type', '')
    in_stock_only = request.args.get('in_stock_only', 'false').lower() == 'true'

    # Add pagination parameters
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 50))  # Default to 50 items per page

    if not vendor or not product_type:
        return jsonify({"success": False, "message": "Vendor and product type are required"}), 400

    # Create an optimized aggregation pipeline
    pipeline = []

    # Match stage for basic search conditions
    match_stage = {
        "username": current_user.username,
        "vendor": vendor,
        "product_type": product_type
    }

    # If in_stock_only is true, add inventory condition to match stage
    if in_stock_only:
        match_stage["variants"] = {"$elemMatch": {"inventory_quantity": {"$gt": 0}}}

    pipeline.append({"$match": match_stage})

    # Project stage to include only the fields we need
    project_stage = {
        "$project": {
            "_id": 1,
            "title": 1,
            "vendor": 1,
            "product_type": 1,
            "image": 1,
            "variants": {
                "$map": {
                    "input": {
                        "$cond": [
                            in_stock_only,
                            {"$filter": {
                                "input": "$variants",
                                "as": "variant",
                                "cond": {"$gt": ["$$variant.inventory_quantity", 0]}
                            }},
                            "$variants"
                        ]
                    },
                    "as": "variant",
                    "in": {
                        "id": "$$variant.id",
                        "title": {"$ifNull": ["$$variant.title", "No Title"]},
                        "price": {"$ifNull": ["$$variant.price", "0.00"]},
                        "barcode": {"$ifNull": ["$$variant.barcode", "No Barcode"]},
                        "inventory_quantity": {"$ifNull": ["$$variant.inventory_quantity", 0]}
                    }
                }
            }
        }
    }

    pipeline.append(project_stage)

    # Sort stage to sort products alphabetically by title
    sort_stage = {"$sort": {"title": 1}}
    pipeline.append(sort_stage)

    # Count total products for pagination info
    count_pipeline = pipeline.copy()
    count_pipeline.append({"$count": "total"})
    count_result = list(db.shProducts.aggregate(count_pipeline))
    total_products = count_result[0]["total"] if count_result else 0

    # Add pagination stages
    skip = (page - 1) * limit
    pipeline.append({"$skip": skip})
    pipeline.append({"$limit": limit})

    # Execute the aggregation pipeline
    products = db.shProducts.aggregate(pipeline)

    result = []
    for product in products:
        # Check if product is a dictionary before using .get() method
        if not isinstance(product, dict):
            current_app.logger.error(f"Unexpected product type: {type(product)}, value: {product}")
            continue

        # Handle image URL whether it's a dictionary with 'src' or directly a string
        image = product.get('image', '')
        if isinstance(image, dict):
            image_src = image.get('src', '')
        else:
            image_src = image if isinstance(image, str) else ''

        # Process variants
        variants = product.get('variants', [])
        processed_variants = []

        for variant in variants:
            variant_id = variant.get('id')
            if isinstance(variant_id, dict) and '$numberLong' in variant_id:
                variant_id = str(variant_id['$numberLong'])
            else:
                variant_id = str(variant_id) if variant_id else ''

            processed_variants.append({
                "id": variant_id,
                "title": variant.get('title', 'No Title'),
                "price": variant.get('price', '0.00'),
                "barcode": variant.get('barcode', 'No Barcode'),
                "inventory_quantity": variant.get('inventory_quantity', 0)
            })

        result.append({
            "id": str(product['_id']),
            "title": product.get('title', 'No Title'),
            "vendor": product.get('vendor', 'Unknown Vendor'),
            "productType": product.get('product_type', 'Unknown Type'),
            "image": image_src,
            "variants": processed_variants
        })

    # Create pagination info
    pagination = {
        "page": page,
        "limit": limit,
        "total": total_products,
        "total_pages": (total_products + limit - 1) // limit  # Ceiling division
    }

    return jsonify({
        "success": True,
        "products": result,
        "pagination": pagination
    })

@pos_bp.route('/pos/filter_products')
@login_required
def filter_products():
    product_type = request.args.get('productType', '')
    in_stock_only = request.args.get('inStockOnly', 'false').lower() == 'true'
    username = current_user.username

    search_condition = {
        "username": username,
        "product_type": product_type
    }

    products = db.shProducts.find(search_condition)

    result = []
    for product in products:
        # Check if product is a dictionary before using .get() method
        if not isinstance(product, dict):
            current_app.logger.error(f"Unexpected product type: {type(product)}, value: {product}")
            continue

        # Handle image URL whether it's a dictionary with 'src' or directly a string
        image = product.get('image', '')
        if isinstance(image, dict):
            image_src = image.get('src', '')
        else:
            image_src = image if isinstance(image, str) else ''
        variants = product.get('variants', [])
        if in_stock_only:
            variants = [variant for variant in variants if variant.get('inventory_quantity', 0) > 0]
        result.append({
            "id": str(product['_id']),
            "title": product.get('title', 'No Title'),
            "expansionName": product.get('expansionName', 'Unknown Expansion'),
            "rarity": product.get('rarity', 'Unknown Rarity'),
            "image": image_src,
            "variants": [
                {
                    "id": str(variant['id']['$numberLong']) if isinstance(variant['id'], dict) else str(variant['id']),
                    "title": variant.get('title', 'No Title'),
                    "price": variant.get('price', '0.00'),
                    "barcode": variant.get('barcode', 'No Barcode'),
                    "inventory_quantity": variant.get('inventory_quantity', 0)
                } for variant in variants
            ]
        })
    return jsonify(result)

@pos_bp.route('/pos/search_customer')
@login_required
def search_customer():
    search_term = request.args.get('search', '').strip()
    if not search_term:
        return jsonify({"success": False, "message": "Search term is required"}), 400

    # Create a case-insensitive regex pattern
    pattern = re.compile(f".*{re.escape(search_term)}.*", re.IGNORECASE)

    # Search by email, first_name, or last_name
    customers = list(db.shCustomers.find({
        "username": current_user.username,
        "$or": [
            {"email": pattern},
            {"first_name": pattern},
            {"last_name": pattern}
        ]
    }))

    if customers:
        customer_list = []
        for customer in customers:
            gift_card_balance = 0
            store_credit_balance = 0
            gift_card_id = customer.get("gift_card_id")
            customer_id = customer.get("id")

            # Check for gift card balance in Shopify if gift_card_id exists
            if gift_card_id:
                shopify_headers = {
                    "Content-Type": "application/json",
                    "X-Shopify-Access-Token": current_user.shopifyAccessToken
                }
                gift_card_balance_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/gift_cards/{gift_card_id}.json"
                try:
                    gift_card_balance_response = requests.get(gift_card_balance_url, headers=shopify_headers)
                    gift_card_balance_response.raise_for_status()
                    gift_card_data = gift_card_balance_response.json()["gift_card"]
                    gift_card_balance = float(gift_card_data["balance"])
                except requests.RequestException as e:
                    current_app.logger.error(f"Error fetching gift card balance: {str(e)}")

            # Check for additional gift cards in the shGiftCards collection
            if customer_id:
                try:
                    # Convert customer_id to string for comparison, handling NumberLong format
                    if isinstance(customer_id, dict) and "$numberLong" in customer_id:
                        customer_id_str = str(customer_id["$numberLong"])
                    else:
                        customer_id_str = str(customer_id)

                    # Find all gift cards associated with this customer
                    gift_cards = list(db.shGiftCards.find({
                        "username": current_user.username,
                        "customer_id": customer_id_str,
                        "disabled": {"$ne": True}  # Exclude disabled gift cards
                    }))

                    current_app.logger.info(f"Found {len(gift_cards)} gift cards for customer {customer_id_str}")

                    for card in gift_cards:
                        # First check if the card has a local balance property we can use directly
                        if "balance" in card and card["balance"] is not None:
                            # Add the local balance
                            current_app.logger.info(f"Using local balance from gift card: {card['balance']}")
                            gift_card_balance += float(card["balance"])
                        # Otherwise, fetch from Shopify API
                        elif "id" in card and card["id"]:
                            # If this is not the same as the primary gift_card_id already checked
                            if not (gift_card_id and str(card.get("id")) == str(gift_card_id)):
                                card_id = card.get("id")
                                try:
                                    current_app.logger.info(f"Fetching balance for gift card ID: {card_id}")
                                    card_balance_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/gift_cards/{card_id}.json"
                                    card_balance_response = requests.get(card_balance_url, headers=shopify_headers)
                                    card_balance_response.raise_for_status()
                                    card_data = card_balance_response.json()["gift_card"]
                                    # Add this card's balance to the total
                                    gift_card_balance += float(card_data["balance"])
                                except requests.RequestException as e:
                                    current_app.logger.error(f"Error fetching additional gift card balance: {str(e)}")
                except Exception as e:
                    current_app.logger.error(f"Error querying shGiftCards: {str(e)}")
                    current_app.logger.error(f"Exception details: {str(e)}")

                # Check for Shopify store credit using GraphQL
                try:
                    # Format customer ID for GraphQL query
                    if isinstance(customer_id, dict) and "$numberLong" in customer_id:
                        formatted_customer_id = customer_id["$numberLong"]
                    else:
                        formatted_customer_id = str(customer_id)

                    current_app.logger.info(f"Checking store credit for customer ID: {formatted_customer_id}")

                    # Execute GraphQL query to get store credit balance
                    query = """
                    query {
                      customer(id: "gid://shopify/Customer/%s") {
                        storeCreditAccounts(first: 10) {
                          edges {
                            node {
                              id
                              balance {
                                amount
                                currencyCode
                              }
                            }
                          }
                        }
                      }
                    }
                    """ % (formatted_customer_id)

                    # Set up GraphQL request
                    graphql_endpoint = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2025-04/graphql.json"
                    headers = {
                        'X-Shopify-Access-Token': current_user.shopifyAccessToken,
                        'Content-Type': 'application/json'
                    }

                    # Make the request
                    response = requests.post(
                        graphql_endpoint,
                        headers=headers,
                        json={"query": query}
                    )

                    if response.status_code == 200:
                        result = response.json()

                        # Extract customer data
                        customer_data = result.get('data', {}).get('customer', {})
                        if customer_data:
                            # Extract store credit accounts
                            store_credit_accounts = customer_data.get('storeCreditAccounts', {}).get('edges', [])

                            # Add up all store credit balances
                            for account_edge in store_credit_accounts:
                                account = account_edge.get('node', {})
                                balance = account.get('balance', {})
                                amount = balance.get('amount')

                                if amount:
                                    store_credit_balance += float(amount)
                                    current_app.logger.info(f"Found store credit balance: {amount}")
                    else:
                        current_app.logger.error(f"Error fetching store credit: {response.status_code} - {response.text}")

                except Exception as e:
                    current_app.logger.error(f"Error checking store credit: {str(e)}")

            # Format the customer ID correctly
            formatted_customer_id = None
            if isinstance(customer_id, dict) and "$numberLong" in customer_id:
                formatted_customer_id = customer_id["$numberLong"]
            else:
                formatted_customer_id = str(customer_id) if customer_id else None

            # Add customer to results with all relevant information
            customer_list.append({
                "id": formatted_customer_id,
                "shopify_id": formatted_customer_id,
                "first_name": customer.get("first_name", "N/A"),
                "last_name": customer.get("last_name", "N/A"),
                "email": customer.get("email", "N/A"),
                "gift_card_balance": str(gift_card_balance),
                "store_credit_balance": str(store_credit_balance),
                "total_credit_balance": str(gift_card_balance + store_credit_balance),
                "gift_card_id": gift_card_id or "",
                "full_name": f"{customer.get('first_name', '')} {customer.get('last_name', '')}".strip()
            })

        return jsonify({
            "success": True,
            "customers": customer_list
        })
    else:
        return jsonify({"success": False, "message": "No customers found."}), 404

@pos_bp.route('/pos/create_customer', methods=['POST'])
@login_required
def create_customer():
    data = request.json
    email = data.get('email')
    name = data.get('name')

    if not email or not name:
        return jsonify({"success": False, "message": "Email and name are required"}), 400

    customer_id = db.shCustomers.insert_one({
        "email": email,
        "name": name,
        "username": current_user.username,
        "timestamp": datetime.utcnow()
    }).inserted_id

    return jsonify({"success": True, "customer_id": str(customer_id)}), 201


@pos_bp.route('/pos/complete_transaction', methods=['POST'])
@login_required
def complete_transaction():
    try:
        # Log the raw request data for debugging
        current_app.logger.info(f"Complete transaction request received: {request.data.decode('utf-8')}")

        data = request.json
        current_app.logger.info(f"Parsed JSON data: {data}")

        till_id = data.get('till_id')
        payment_method = data.get('payment_method')
        total = data.get('total')
        items = data.get('items')
        customer_id = data.get('customer_id')
        gift_card_id = data.get('gift_card_id')
        gift_card_amount = float(data.get('gift_card_amount', 0.0))
        use_gift_card = data.get('use_gift_card', False)  # New flag to indicate if gift card should be used
        employee_name = data.get('employee_name')
        held_sale_id = data.get('held_sale_id')

        # Added store credit variables
        store_credit_account_id = data.get('store_credit_account_id')
        store_credit_amount = float(data.get('store_credit_amount', 0.0))
        use_store_credit = data.get('use_store_credit', False)

        # Calculate line item discounts
        line_item_discounts = 0
        for item in items:
            if 'discount_amount' in item:
                line_item_discounts += float(item['discount_amount'])

        # Apply line item discounts to the total
        if line_item_discounts > 0:
            current_app.logger.info(f"Applying line item discounts: {line_item_discounts}")
            total = float(total) - line_item_discounts

        # Log the extracted values for debugging
        current_app.logger.info(f"Extracted values: till_id={till_id}, payment_method={payment_method}, total={total}, items_count={len(items) if items else 0}, customer_id={customer_id}, employee_name={employee_name}")

        # If till_id is missing, try to get it from the session
        if not till_id:
            # Try to get the till ID from the session
            session_till_id = session.get('current_till_id')
            if session_till_id:
                till_id = session_till_id
                current_app.logger.info(f"Using till_id from session: {till_id}")
            else:
                current_app.logger.error("No till_id found in request or session")

        if not till_id or not payment_method or total is None or not items or not employee_name:
            missing_fields = []
            if not till_id: missing_fields.append("till_id")
            if not payment_method: missing_fields.append("payment_method")
            if total is None: missing_fields.append("total")
            if not items: missing_fields.append("items")
            if not employee_name: missing_fields.append("employee_name")

            error_message = f"Invalid transaction data. Missing required fields: {', '.join(missing_fields)}"
            current_app.logger.error(error_message)
            return jsonify({"success": False, "message": error_message}), 400
    except Exception as e:
        current_app.logger.error(f"Error parsing transaction request: {str(e)}")
        return jsonify({"success": False, "message": f"Error parsing transaction request: {str(e)}"}), 400

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except Exception as e:
        return jsonify({"success": False, "message": "Invalid till ID"}), 400

    transaction = Transaction(
        items=[{
            "title": item['title'],
            "variant_id": None if (item.get('is_custom') or (isinstance(item.get('variant_id'), str) and item['variant_id'].startswith('custom_'))) else item['variant_id'],
            "quantity": item['quantity'],
            "price": item['price']
        } for item in items],
        total=total,
        payment_method=payment_method,
        customer_id=customer_id,
        employee_name=employee_name,
        gift_card_id=gift_card_id,
        gift_card_amount=gift_card_amount,
        store_credit_account_id=store_credit_account_id,
        store_credit_amount=store_credit_amount
    )
    till.update(push__transactions=transaction, inc__running_total=round(total if payment_method == 'cash' else 0, 2))

    shopify_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/orders.json"
    shopify_headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": current_user.shopifyAccessToken
    }

    if customer_id:
        try:
            # Try to find the customer with various ID formats
            customer = None
            # First try with the ID directly
            customer = db.shCustomers.find_one({"id": customer_id, "username": current_user.username})

            if not customer:
                # Try with integer conversion (legacy format)
                try:
                    customer = db.shCustomers.find_one({"id": int(customer_id), "username": current_user.username})
                except (ValueError, TypeError):
                    pass

            if not customer:
                # Try with NumberLong format
                customer = db.shCustomers.find_one({
                    "id": {"$numberLong": str(customer_id)},
                    "username": current_user.username
                })

            if not customer:
                current_app.logger.error(f"Customer not found with ID: {customer_id}")
                return jsonify({"success": False, "message": "Customer not found in database"}), 400
        except Exception as e:
            current_app.logger.error(f"Error finding customer: {str(e)}")
            return jsonify({"success": False, "message": f"Error finding customer: {str(e)}"}), 400
    else:
        customer = {
            "id": None,
            "first_name": "Guest",
            "last_name": "Customer",
            "email": None
        }

    # Check if any items are custom (either by flag or variant_id)
    has_custom_items = any(
        item.get("is_custom") or
        (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
        for item in items
    )

    # Get tax information from the request if provided, otherwise calculate it
    tax_amount = data.get('tax_amount')
    if tax_amount is not None:
        # Use the tax amount from the frontend
        tax_amount = float(tax_amount)
    else:
        # Calculate tax amount based on the total and tax rate
        tax_amount = round(total * till.tax_rate / 100, 2)

    # Check if we should omit the customer object entirely
    omit_customer_object = data.get('omit_customer_object', False)

    # Get notification preferences
    notify_customer = data.get('notify_customer', False)

    # If notify_customer is true, set only the order receipt notification to true
    # We don't want to send fulfillment notifications for POS orders since the customer already has their items
    send_receipt = notify_customer or data.get('send_receipt', False)
    send_fulfillment_receipt = False  # Always false for POS orders

    current_app.logger.info(f"Notification settings: notify_customer={notify_customer}, send_receipt={send_receipt}, send_fulfillment_receipt={send_fulfillment_receipt}")

    order_data = {
        "order": {
            "line_items": [],
            "transactions": [],
            "source_name": "TCGSyncPos",
            "tags": "pos, tcgsync",
            "financial_status": "paid",
            "fulfillment_status": "fulfilled",  # Mark the order as fulfilled immediately
            # Add a default POS customer if no customer_id is provided
            "customer": {
                "first_name": "POS",
                "last_name": "Customer",
                "email": "<EMAIL>"
            } if not customer_id else None,  # This will be overridden if customer_id exists
            # Add notification preferences
            "send_receipt": send_receipt,
            "send_fulfillment_receipt": send_fulfillment_receipt
        }
    }

    # Only include customer object if we have a valid customer_id and are not explicitly omitting it
    if customer_id and not omit_customer_object:
        try:
            # Try to convert customer_id to int, but handle potential errors
            customer_id_int = None
            try:
                customer_id_int = int(customer_id)
            except (ValueError, TypeError):
                current_app.logger.warning(f"Could not convert customer_id {customer_id} to int, using as is")
                # If we can't convert to int, we'll use the original value

            order_data["order"]["customer"] = {
                "id": customer_id_int if customer_id_int is not None else customer_id,
                "first_name": customer.get("first_name"),
                "last_name": customer.get("last_name"),
                "email": customer.get("email")
            }
        except Exception as e:
            current_app.logger.error(f"Error setting customer data: {str(e)}")
            # If there's an error with the customer data, use a default customer
            order_data["order"]["customer"] = {
                "first_name": "POS",
                "last_name": "Customer",
                "email": "<EMAIL>"
            }

    # Log tax rate for debugging
    current_app.logger.info(f"Using tax rate: {till.tax_rate}%")

    for item in items:
        # Check if it's a custom item either by is_custom flag or variant_id starting with "custom_"
        is_custom = item.get("is_custom") or (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))

        # Check if this item is tax exempt
        is_tax_exempt = item.get("tax_exempt", False)

        # Get the tax-inclusive price from the item
        tax_inclusive_price = float(item["price"])
        item_quantity = int(item["quantity"])

        # Check if the username is gametimewilliam (case-insensitive)
        is_gametimewilliam = current_user.username.lower() == 'gametimewilliam'

        # Determine if tax is inclusive or exclusive
        # For gametimewilliam, always use tax-exclusive pricing regardless of till setting
        is_tax_inclusive = till.tax_inclusive and not is_gametimewilliam
        current_app.logger.info(f"Username: {current_user.username}, is_gametimewilliam: {is_gametimewilliam}, is_tax_inclusive: {is_tax_inclusive}")

        # Calculate tax amount and tax-exclusive price
        if not is_tax_exempt and "tax_amount" in item:
            # Use the tax amount from the item
            item_tax_amount = float(item["tax_amount"]) / item_quantity  # Per unit tax amount
            if is_tax_inclusive:
                tax_exclusive_price = tax_inclusive_price - item_tax_amount
            else:
                tax_exclusive_price = tax_inclusive_price
                # For tax-exclusive pricing, we don't subtract the tax amount from the price
            current_app.logger.info(f"Using provided tax amount: {item_tax_amount} per unit")
        elif not is_tax_exempt and till.tax_rate > 0:
            # Calculate tax amount based on the tax rate
            tax_rate_decimal = till.tax_rate / 100
            if is_tax_inclusive:
                # For tax-inclusive pricing, extract the tax from the price
                tax_exclusive_price = tax_inclusive_price / (1 + tax_rate_decimal)
                item_tax_amount = tax_inclusive_price - tax_exclusive_price
            else:
                # For tax-exclusive pricing, the tax is additional
                tax_exclusive_price = tax_inclusive_price
                item_tax_amount = tax_exclusive_price * tax_rate_decimal
            current_app.logger.info(f"Calculated tax amount using rate {till.tax_rate}%: {item_tax_amount} per unit, is_tax_inclusive: {is_tax_inclusive}")
        else:
            # Item is tax exempt or tax rate is 0
            tax_exclusive_price = tax_inclusive_price
            item_tax_amount = 0
            current_app.logger.info(f"No tax applied: is_tax_exempt={is_tax_exempt}, tax_rate={till.tax_rate}%")

        # Round to 2 decimal places
        tax_exclusive_price = round(tax_exclusive_price, 2)

        # Create the base line item with tax-exclusive price
        line_item = {
            "quantity": item_quantity,
            "price": str(tax_exclusive_price),  # Send tax-exclusive price to Shopify
            "taxable": not is_tax_exempt  # Set taxable property based on tax_exempt flag
        }

        # Add item-specific properties
        # Clean up the title by removing extra newlines and spaces
        clean_title = item.get("title", "").strip()
        if "\n" in clean_title:
            # Extract the first line or use the whole string if it's empty after splitting
            first_line = clean_title.split("\n")[0].strip()
            clean_title = first_line if first_line else clean_title

        # Always include title for all line items
        line_item["title"] = clean_title
        line_item["name"] = clean_title  # Add name field as well to satisfy Shopify API requirements

        if is_custom:
            line_item["requires_shipping"] = False
        else:
            line_item["variant_id"] = item["variant_id"]

        # Always add tax_lines for taxable items, even if tax amount is 0
        if not is_tax_exempt and till.tax_rate > 0:
            # Ensure tax amount is at least 0.01 if tax rate is positive
            # This ensures Shopify recognizes the item as taxable
            effective_tax_amount = max(item_tax_amount, 0.01 / item_quantity) if till.tax_rate > 0 else item_tax_amount

            line_item["tax_lines"] = [
                {
                    "price": str(round(effective_tax_amount * item_quantity, 2)),  # Total tax amount
                    "rate": till.tax_rate / 100,
                    "title": "Standard Tax"
                }
            ]
            current_app.logger.info(f"Added tax line with rate {till.tax_rate}% and amount {effective_tax_amount * item_quantity}")

        # Log the price conversion for debugging
        current_app.logger.info(f"Item: {item.get('title', 'Unknown')}, Tax-inclusive: {tax_inclusive_price}, Tax-exclusive: {tax_exclusive_price}, Tax amount: {item_tax_amount * item_quantity}")

        # Add the line item to the order
        order_data["order"]["line_items"].append(line_item)

    remaining_total = float(total)
    total_gift_card_applied = 0.0
    total_store_credit_applied = 0.0

    # Handle store credit first if specified
    use_store_credit = data.get('use_store_credit', False)
    store_credit_account_id = data.get('store_credit_account_id')
    store_credit_amount = float(data.get('store_credit_amount', 0.0))

    if use_store_credit and customer_id and store_credit_account_id and store_credit_amount > 0:
        current_app.logger.info(f"Attempting to use store credit for customer {customer_id}")

        # Format customer ID for GraphQL query
        if isinstance(customer_id, dict) and "$numberLong" in customer_id:
            formatted_customer_id = customer_id["$numberLong"]
        else:
            formatted_customer_id = str(customer_id)

        # Set up GraphQL request
        graphql_endpoint = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2025-04/graphql.json"
        graphql_headers = {
            'X-Shopify-Access-Token': current_user.shopifyAccessToken,
            'Content-Type': 'application/json'
        }

        # Query to check current balance before deduction
        query = """
        query {
          customer(id: "gid://shopify/Customer/%s") {
            storeCreditAccounts(first: 10) {
              edges {
                node {
                  id
                  balance {
                    amount
                    currencyCode
                  }
                }
              }
            }
          }
        }
        """ % (formatted_customer_id)

        try:
            # Check current balance first to verify there's enough credit
            response = requests.post(
                graphql_endpoint,
                headers=graphql_headers,
                json={"query": query}
            )

            if response.status_code == 200:
                result = response.json()

                # Get total available store credit
                customer_data = result.get('data', {}).get('customer', {})
                store_credit_accounts = customer_data.get('storeCreditAccounts', {}).get('edges', [])
                account_balance = 0

                # Find the first account with balance
                for account_edge in store_credit_accounts:
                    account = account_edge.get('node', {})
                    balance = account.get('balance', {})
                    if float(balance.get('amount', 0)) > 0:
                        account_balance += float(balance.get('amount', 0))

                # Determine how much to charge (capped by balance and remaining total)
                amount_to_charge = min(account_balance, store_credit_amount, remaining_total)

                if amount_to_charge > 0:
                    current_app.logger.info(f"Charging {amount_to_charge} to store credit for customer {formatted_customer_id}")

                    # IMPORTANT: Get the currency from user settings
                    # Get user's currency from User model
                    from models.user_model import User
                    user_obj = User.objects(username=current_user.username).first()
                    currency_code = user_obj.currency if hasattr(user_obj, 'currency') and user_obj.currency else 'GBP'

                    current_app.logger.info(f"Using currency code: {currency_code}")

                    # Use the exact same GraphQL mutation as in shopify_customers_routes.py for consistency
                    mutation = """
                    mutation {
                      storeCreditAccountDebit(
                        id: "gid://shopify/Customer/%s",
                        debitInput: {
                          debitAmount: {
                            amount: "%s"
                            currencyCode: %s
                          }
                        }
                      ) {
                        storeCreditAccountTransaction {
                          id
                          amount {
                            amount
                            currencyCode
                          }
                        }
                        userErrors {
                          field
                          message
                        }
                      }
                    }
                    """ % (formatted_customer_id, str(amount_to_charge), currency_code)

                    current_app.logger.info(f"Executing store credit debit mutation with: customer_id={formatted_customer_id}, amount={amount_to_charge}, currency={currency_code}")

                    # Apply the store credit payment
                    debit_response = requests.post(
                        graphql_endpoint,
                        headers=graphql_headers,
                        json={"query": mutation}
                    )

                    if debit_response.status_code == 200:
                        debit_result = debit_response.json()

                        # Check for user errors
                        user_errors = debit_result.get('data', {}).get('storeCreditAccountDebit', {}).get('userErrors', [])
                        if user_errors:
                            error_messages = [error.get('message') for error in user_errors]
                            current_app.logger.error(f"Error applying store credit: {', '.join(error_messages)}")
                        else:
                            # Transaction was successful
                            transaction = debit_result.get('data', {}).get('storeCreditAccountDebit', {}).get('storeCreditAccountTransaction', {})
                            if transaction:
                                # Update remaining total and add transaction
                                remaining_total -= amount_to_charge
                                total_store_credit_applied += amount_to_charge

                                order_data["order"]["transactions"].append({
                                    "kind": "sale",  # Changed from "store_credit" to "sale" to avoid refund elements
                                    "status": "success",
                                    "amount": amount_to_charge
                                })

                                current_app.logger.info(f"Successfully applied store credit payment. Remaining balance: {remaining_total}")
                    else:
                        current_app.logger.error(f"Error applying store credit: {debit_response.status_code} - {debit_response.text}")
        except Exception as e:
            current_app.logger.error(f"Error processing store credit payment: {str(e)}")
            # Continue with regular payment processing even if store credit fails

    # If use_gift_card is specified and we have a customer, try to use their gift card balance
    if use_gift_card and customer_id:
        current_app.logger.info(f"Attempting to use gift card for customer {customer_id}")

        # Process gift cards in order:
        # 1. First use the specific gift card ID if provided
        # 2. Then use customer's primary gift card
        # 3. Then use any gift cards from shGiftCards collection

        # List to hold all the customer's gift cards
        customer_gift_cards = []

        # 1. If a specific gift card ID was specified in the request, use it first
        if gift_card_id:
            customer_gift_cards.append({"id": gift_card_id, "custom_amount": gift_card_amount if gift_card_amount > 0 else None})

        # 2. Use the primary gift card associated with the customer (if not already added)
        customer_primary_gift_card = customer.get("gift_card_id")
        if customer_primary_gift_card and (not gift_card_id or gift_card_id != customer_primary_gift_card):
            customer_gift_cards.append({"id": customer_primary_gift_card, "custom_amount": None})

        # 3. Get all gift cards from shGiftCards collection
        try:
            # Convert customer_id to string for comparison if needed
            if isinstance(customer_id, dict) and "$numberLong" in customer_id:
                customer_id_str = str(customer_id["$numberLong"])
            else:
                customer_id_str = str(customer_id)

            db_gift_cards = list(db.shGiftCards.find({
                "username": current_user.username,
                "customer_id": customer_id_str,
                "disabled": {"$ne": True}  # Exclude disabled gift cards
            }))

            for card in db_gift_cards:
                card_id = card.get("id")
                if card_id and (not gift_card_id or gift_card_id != card_id) and (not customer_primary_gift_card or customer_primary_gift_card != card_id):
                    # Add card to list with its known balance if available
                    balance = card.get("balance")
                    customer_gift_cards.append({
                        "id": card_id,
                        "balance": float(balance) if balance is not None else None,
                        "custom_amount": None
                    })
        except Exception as e:
            current_app.logger.error(f"Error querying shGiftCards: {str(e)}")

        # Process each gift card until transaction is fully paid or no more gift cards
        for gift_card in customer_gift_cards:
            if remaining_total <= 0:
                break  # No need to process more gift cards

            card_id = gift_card["id"]
            custom_amount = gift_card.get("custom_amount")
            current_app.logger.info(f"Processing gift card {card_id}")

            # If card has a known balance from the database, use it
            if "balance" in gift_card and gift_card["balance"] is not None:
                card_balance = gift_card["balance"]
                current_app.logger.info(f"Using known balance: {card_balance}")
            else:
                # Otherwise, query Shopify for the current balance
                gift_card_balance_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/gift_cards/{card_id}.json"
                try:
                    gift_card_balance_response = requests.get(gift_card_balance_url, headers=shopify_headers)
                    gift_card_balance_response.raise_for_status()
                    card_data = gift_card_balance_response.json()["gift_card"]
                    card_balance = float(card_data["balance"])
                    current_app.logger.info(f"Fetched balance from Shopify: {card_balance}")
                except requests.RequestException as e:
                    current_app.logger.error(f"Error fetching gift card balance: {str(e)}")
                    continue  # Skip this gift card and try the next one

            # Determine how much to charge to this gift card
            if card_balance <= 0:
                current_app.logger.info(f"Gift card {card_id} has zero balance, skipping")
                continue  # Skip empty gift cards

            # If a specific amount was requested for this card, use that amount (capped by balance and remaining total)
            if custom_amount is not None:
                amount_to_charge = min(card_balance, custom_amount, remaining_total)
            else:
                # Otherwise use the full balance (capped by remaining total)
                amount_to_charge = min(card_balance, remaining_total)

            if amount_to_charge <= 0:
                continue  # Skip if nothing to charge (shouldn't happen, but being safe)

            current_app.logger.info(f"Charging {amount_to_charge} to gift card {card_id}")

            # Apply the gift card payment
            gift_card_adjustment_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/gift_cards/{card_id}/adjustments.json"
            adjustment_data = {
                "adjustment": {
                    "amount": -amount_to_charge,
                    "note": "POS transaction deduction"
                }
            }

            try:
                gift_card_adjustment_response = requests.post(gift_card_adjustment_url, json=adjustment_data, headers=shopify_headers)
                gift_card_adjustment_response.raise_for_status()

                # Update remaining total and add transaction
                remaining_total -= amount_to_charge
                total_gift_card_applied += amount_to_charge

                order_data["order"]["transactions"].append({
                    "kind": "sale",  # Always use "sale" for Shopify Admin API
                    "status": "success",
                    "amount": amount_to_charge
                })

                # Add gift card info to note attributes
                if "note_attributes" not in order_data["order"]:
                    order_data["order"]["note_attributes"] = []

                order_data["order"]["note_attributes"].append({
                    "name": "gift_card_payment",
                    "value": str(amount_to_charge)
                })

                current_app.logger.info(f"Successfully applied gift card payment. Remaining balance: {remaining_total}")
            except requests.RequestException as e:
                current_app.logger.error(f"Error processing gift card payment: {str(e)}")
                # Continue with other gift cards even if this one failed

    if remaining_total > 0:
        order_data["order"]["transactions"].append({
            "kind": "sale",  # Always use "sale" for Shopify Admin API (not "cash" or "card")
            "status": "success",
            "amount": remaining_total
        })

        # Add payment method as a tag instead
        order_data["order"]["tags"] += f", {payment_method}"

    try:
        # Log the order data being sent to Shopify
        current_app.logger.info(f"Sending order to Shopify: {order_data}")

        # Add more detailed error handling
        try:
            shopify_response = requests.post(shopify_url, json=order_data, headers=shopify_headers)
            shopify_response.raise_for_status()

            # Get the order ID from the response
            order_response_data = shopify_response.json()
            order_id = order_response_data.get("order", {}).get("id")
        except requests.exceptions.RequestException as e:
            # Log the detailed error
            current_app.logger.error(f"Shopify API error: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                current_app.logger.error(f"Response status: {e.response.status_code}")
                current_app.logger.error(f"Response body: {e.response.text}")
            raise

        current_app.logger.info(f"Order {order_id} created successfully with fulfillment_status=fulfilled")

        # With the new inventory policy, we need to adjust inventory here for completed transactions
        # But only if it's not a held sale or layaway (which already adjusted inventory)
        if not held_sale_id and not data.get('is_layaway'):
            try:
                for item in items:
                    is_custom = item.get("is_custom") or (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
                    if not is_custom:
                        # Adjust inventory for completed transactions
                        current_app.logger.info(f"Adjusting inventory for variant {item['variant_id']} during transaction completion")

                        # Call adjust_shopify_inventory_for_reserved_items with negative adjustment to reduce inventory
                        adjust_shopify_inventory_for_reserved_items(
                            [{
                                "variant_id": item["variant_id"],
                                "quantity": int(item["quantity"])  # Ensure quantity is an integer
                            }],
                            -1,  # Negative adjustment to reduce inventory
                            current_user.username
                        )
            except Exception as e:
                # Log the error but continue with the transaction
                current_app.logger.error(f"Error adjusting inventory during transaction completion: {str(e)}")
                # Don't return an error response here, continue with the transaction
        else:
            # For held sales or layaways, skip inventory adjustment since it was already done
            current_app.logger.info(f"Skipping inventory adjustment for held sale or layaway completion - already adjusted during creation")

        if held_sale_id:
            # When completing a held sale as a transaction, we should just remove the reservation records
            # without restoring inventory since the items are being sold
            reserved_items_collection.delete_many({
                "reservation_type": "hold",
                "reservation_id": held_sale_id,
                "username": current_user.username
            })
            current_app.logger.info(f"Removed reservation records for completed held sale {held_sale_id}")

            # Delete the held sale record
            HeldSale.objects.get(id=ObjectId(held_sale_id), username=current_user.username).delete()
            current_app.logger.info(f"Deleted held sale record {held_sale_id} after completion")
    except requests.RequestException as e:
        current_app.logger.error(f"Error creating Shopify order: {str(e)}")
        return jsonify({"success": False, "message": "Error creating Shopify order"}), 400

    # Check if there's a terminal order to delete after successful payment
    terminal_order_id = data.get('terminal_order_id')
    if terminal_order_id:
        try:
            from models.terminal_order_model import TerminalOrder

            # Find and delete the terminal order
            terminal_order = TerminalOrder.objects(
                id=ObjectId(terminal_order_id),
                username=current_user.username
            ).first()

            if terminal_order:
                customer_name = terminal_order.customerName
                terminal_order.delete()
                current_app.logger.info(f"Deleted terminal order {terminal_order_id} for customer {customer_name} after successful payment")
            else:
                current_app.logger.warning(f"Terminal order {terminal_order_id} not found for deletion")

        except Exception as e:
            current_app.logger.error(f"Error deleting terminal order {terminal_order_id}: {str(e)}")
            # Don't fail the transaction if terminal order deletion fails

    return jsonify({"success": True, "message": "Transaction completed successfully"}), 201

@pos_bp.route('/pos/complete_split_transaction', methods=['POST'])
@login_required
def complete_split_transaction():
    data = request.json
    items = data.get('items', [])
    total = float(data.get('total', 0))
    payment_methods = data.get('payment_methods', [])
    customer_id = data.get('customer_id')
    employee_name = data.get('employee_name')
    till_id = data.get('till_id')
    held_sale_id = data.get('held_sale_id')

    # Added gift card variables
    gift_card_id = data.get('gift_card_id')
    gift_card_amount = float(data.get('gift_card_amount', 0.0))

    # Added store credit variables
    store_credit_account_id = data.get('store_credit_account_id')
    store_credit_amount = float(data.get('store_credit_amount', 0.0))
    use_store_credit = data.get('use_store_credit', False)

    if not till_id or not payment_methods or total is None or not items or not employee_name:
        return jsonify({"success": False, "message": "Invalid transaction data"}), 400

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except Exception as e:
        return jsonify({"success": False, "message": "Invalid till ID"}), 400

    # Create payment method objects
    payment_method_objects = []
    cash_total = 0

    for method in payment_methods:
        payment_type = method.get('type')
        amount = float(method.get('amount'))

        # Track cash total for till update
        if payment_type == 'cash':
            cash_total += amount

        payment_method_objects.append(PaymentMethod(
            type=payment_type,
            amount=amount
        ))

    # Create the transaction
    transaction = Transaction(
        items=[{
            "title": item['title'],
            "variant_id": None if (item.get('is_custom') or (isinstance(item.get('variant_id'), str) and item['variant_id'].startswith('custom_'))) else item['variant_id'],
            "quantity": item['quantity'],
            "price": item['price']
        } for item in items],
        total=total,
        payment_methods=payment_method_objects,
        is_split_payment=True,
        customer_id=customer_id,
        employee_name=employee_name,
        gift_card_id=gift_card_id,
        gift_card_amount=gift_card_amount,
        store_credit_account_id=store_credit_account_id,
        store_credit_amount=store_credit_amount
    )

    # Update the till with the transaction and increment running total for cash
    till.update(push__transactions=transaction, inc__running_total=round(cash_total, 2))

    # Create Shopify order
    try:
        shopify_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/orders.json"
        shopify_headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": current_user.shopifyAccessToken
        }
        current_app.logger.info(f"Shopify API URL: {shopify_url}")
        current_app.logger.info(f"Using Shopify store: {current_user.shopifyStoreName}")
    except Exception as e:
        current_app.logger.error(f"Error setting up Shopify API request: {str(e)}")
        return jsonify({"success": False, "message": f"Error setting up Shopify API request: {str(e)}"}), 500

    # Get customer information if customer_id is provided
    customer = None
    if customer_id:
        try:
            customer = db.shCustomers.find_one({"_id": ObjectId(customer_id)})

            if not customer:
                # Try to find in Shopify customers collection
                customer = db.shopifyCustomers.find_one({
                    "id": int(customer_id),
                    "username": current_user.username
                })

            if not customer:
                current_app.logger.error(f"Customer not found with ID: {customer_id}")
                return jsonify({"success": False, "message": "Customer not found in database"}), 400
        except Exception as e:
            current_app.logger.error(f"Error finding customer: {str(e)}")
            return jsonify({"success": False, "message": f"Error finding customer: {str(e)}"}), 400
    else:
        customer = {
            "id": None,
            "first_name": "Guest",
            "last_name": "Customer",
            "email": None
        }

    # Get tax information from the request if provided, otherwise calculate it
    tax_amount = data.get('tax_amount')
    if tax_amount is not None:
        # Use the tax amount from the frontend
        tax_amount = float(tax_amount)
    else:
        # Calculate tax amount based on the total and tax rate
        tax_amount = round(total * till.tax_rate / 100, 2)

    # Get notification preferences
    notify_customer = data.get('notify_customer', False)
    send_receipt = notify_customer or data.get('send_receipt', False)
    send_fulfillment_receipt = False  # Always false for POS orders

    # Create Shopify order data
    order_data = {
        "order": {
            "line_items": [],
            "transactions": [],
            "source_name": "TCGSyncPos",
            "tags": "pos, tcgsync, split_payment",
            "financial_status": "paid",
            "fulfillment_status": "fulfilled",  # Mark the order as fulfilled immediately
            # Add a default POS customer if no customer_id is provided
            "customer": {
                "first_name": "POS",
                "last_name": "Customer",
                "email": "<EMAIL>"
            } if not customer_id else None,  # This will be overridden if customer_id exists
            # Add notification preferences
            "send_receipt": send_receipt,
            "send_fulfillment_receipt": send_fulfillment_receipt
        }
    }

    # Log tax rate for debugging
    current_app.logger.info(f"Using tax rate: {till.tax_rate}%")

    # Add line items with tax-exclusive prices
    for item in items:
        # Get the tax-inclusive price from the item
        tax_inclusive_price = float(item["price"])
        item_quantity = int(item["quantity"])
        is_tax_exempt = item.get("tax_exempt", False)

        # Check if the username is gametimewilliam (case-insensitive)
        is_gametimewilliam = current_user.username.lower() == 'gametimewilliam'

        # Determine if tax is inclusive or exclusive
        # For gametimewilliam, always use tax-exclusive pricing regardless of till setting
        is_tax_inclusive = till.tax_inclusive and not is_gametimewilliam
        current_app.logger.info(f"Username: {current_user.username}, is_gametimewilliam: {is_gametimewilliam}, is_tax_inclusive: {is_tax_inclusive}")

        # Calculate tax amount and tax-exclusive price
        if not is_tax_exempt and "tax_amount" in item:
            # Use the tax amount from the item
            item_tax_amount = float(item["tax_amount"]) / item_quantity  # Per unit tax amount
            if is_tax_inclusive:
                tax_exclusive_price = tax_inclusive_price - item_tax_amount
            else:
                tax_exclusive_price = tax_inclusive_price
                # For tax-exclusive pricing, we don't subtract the tax amount from the price
            current_app.logger.info(f"Using provided tax amount: {item_tax_amount} per unit")
        elif not is_tax_exempt and till.tax_rate > 0:
            # Calculate tax amount based on the tax rate
            tax_rate_decimal = till.tax_rate / 100
            if is_tax_inclusive:
                # For tax-inclusive pricing, extract the tax from the price
                tax_exclusive_price = tax_inclusive_price / (1 + tax_rate_decimal)
                item_tax_amount = tax_inclusive_price - tax_exclusive_price
            else:
                # For tax-exclusive pricing, the tax is additional
                tax_exclusive_price = tax_inclusive_price
                item_tax_amount = tax_exclusive_price * tax_rate_decimal
            current_app.logger.info(f"Calculated tax amount using rate {till.tax_rate}%: {item_tax_amount} per unit, is_tax_inclusive: {is_tax_inclusive}")
        else:
            # Item is tax exempt or tax rate is 0
            tax_exclusive_price = tax_inclusive_price
            item_tax_amount = 0
            current_app.logger.info(f"No tax applied: is_tax_exempt={is_tax_exempt}, tax_rate={till.tax_rate}%")

        # Round to 2 decimal places
        tax_exclusive_price = round(tax_exclusive_price, 2)

        # Create the base line item with tax-exclusive price
        line_item = {
            "quantity": item_quantity,
            "price": str(tax_exclusive_price),  # Send tax-exclusive price to Shopify
            "taxable": not is_tax_exempt  # Set taxable property based on tax_exempt flag
        }

        # Add item-specific properties
        # Clean up the title by removing extra newlines and spaces
        clean_title = item.get("title", "").strip()
        if "\n" in clean_title:
            # Extract the first line or use the whole string if it's empty after splitting
            first_line = clean_title.split("\n")[0].strip()
            clean_title = first_line if first_line else clean_title

        # Always include title for all line items
        line_item["title"] = clean_title
        line_item["name"] = clean_title  # Add name field as well to satisfy Shopify API requirements

        if item.get("is_custom") or (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_")):
            line_item["requires_shipping"] = False
        else:
            line_item["variant_id"] = item["variant_id"]

        # Always add tax_lines for taxable items, even if tax amount is 0
        if not is_tax_exempt and till.tax_rate > 0:
            # Ensure tax amount is at least 0.01 if tax rate is positive
            # This ensures Shopify recognizes the item as taxable
            effective_tax_amount = max(item_tax_amount, 0.01 / item_quantity) if till.tax_rate > 0 else item_tax_amount

            line_item["tax_lines"] = [
                {
                    "price": str(round(effective_tax_amount * item_quantity, 2)),  # Total tax amount
                    "rate": till.tax_rate / 100,
                    "title": "Standard Tax"
                }
            ]
            current_app.logger.info(f"Added tax line with rate {till.tax_rate}% and amount {effective_tax_amount * item_quantity}")

        # Log the price conversion for debugging
        current_app.logger.info(f"Item: {item.get('title', 'Unknown')}, Tax-inclusive: {tax_inclusive_price}, Tax-exclusive: {tax_exclusive_price}, Tax amount: {item_tax_amount * item_quantity}")

        # Add the line item to the order
        order_data["order"]["line_items"].append(line_item)

    # Add customer information if customer_id is provided
    if customer_id and customer:
        if "id" in customer and customer["id"]:
            # This is a Shopify customer
            order_data["order"]["customer"] = {"id": int(customer["id"])}
        else:
            # This is a custom customer
            order_data["order"]["customer"] = {
                "first_name": customer.get("name", "").split(" ")[0] if " " in customer.get("name", "") else customer.get("name", ""),
                "last_name": " ".join(customer.get("name", "").split(" ")[1:]) if " " in customer.get("name", "") else "",
                "email": customer.get("email")
            }

    # Add payment transactions
    for method in payment_methods:
        payment_type = method.get('type')
        amount = float(method.get('amount'))

        transaction_data = {
            "kind": "sale",
            "status": "success",
            "amount": str(amount),
            "gateway": payment_type.capitalize(),
            "source_name": "TCGSyncPos"
        }

        order_data["order"]["transactions"].append(transaction_data)

    # Add gift card transaction if applicable
    if gift_card_id and gift_card_amount > 0:
        order_data["order"]["transactions"].append({
            "kind": "sale",
            "status": "success",
            "amount": str(gift_card_amount),
            "gateway": "Gift Card",
            "source_name": "TCGSyncPos"
        })

    # Add store credit transaction if applicable
    if store_credit_account_id and store_credit_amount > 0:
        order_data["order"]["transactions"].append({
            "kind": "sale",
            "status": "success",
            "amount": str(store_credit_amount),
            "gateway": "Store Credit",
            "source_name": "TCGSyncPos"
        })

    # Create the order in Shopify
    try:
        response = requests.post(shopify_url, headers=shopify_headers, json=order_data)
        response.raise_for_status()
        shopify_order = response.json()["order"]
        shopify_order_id = shopify_order["id"]

        # If this was a held sale, delete it now that it's been processed
        if held_sale_id:
            try:
                HeldSale.objects.get(id=ObjectId(held_sale_id), username=current_user.username).delete()
                current_app.logger.info(f"Deleted held sale {held_sale_id} after processing")
            except Exception as e:
                current_app.logger.error(f"Error deleting held sale: {str(e)}")

        # Create a POSSale record for reporting
        pos_sale = POSSale(
            id=str(ObjectId()),
            username=current_user.username,
            till_id=till_id,
            customer_id=customer_id,
            customer_name=customer.get("name") if customer else None,
            customer_email=customer.get("email") if customer else None,
            employee_name=employee_name,
            is_split_payment=True,
            payment_methods=payment_method_objects,
            total=total,
            subtotal=total - tax_amount,
            tax_amount=tax_amount,
            gift_card_id=gift_card_id,
            gift_card_amount=gift_card_amount,
            store_credit_account_id=store_credit_account_id,
            store_credit_amount=store_credit_amount,
            items=items,
            shopify_order_id=str(shopify_order_id)
        )
        pos_sale.save()

        # Update gift card balance if used
        if gift_card_id and gift_card_amount > 0:
            try:
                db.giftCards.update_one(
                    {"_id": ObjectId(gift_card_id)},
                    {"$inc": {"balance": -gift_card_amount}}
                )
                current_app.logger.info(f"Updated gift card {gift_card_id} balance, deducted {gift_card_amount}")
            except Exception as e:
                current_app.logger.error(f"Error updating gift card balance: {str(e)}")

        # Update store credit balance if used
        if store_credit_account_id and store_credit_amount > 0 and use_store_credit:
            try:
                db.storeCredit.update_one(
                    {"_id": ObjectId(store_credit_account_id)},
                    {"$inc": {"balance": -store_credit_amount}}
                )
                current_app.logger.info(f"Updated store credit {store_credit_account_id} balance, deducted {store_credit_amount}")
            except Exception as e:
                current_app.logger.error(f"Error updating store credit balance: {str(e)}")

        return jsonify({"success": True, "message": "Split payment transaction completed successfully", "shopify_order_id": shopify_order_id})

    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error creating Shopify order: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            current_app.logger.error(f"Shopify response: {e.response.text}")
        return jsonify({"success": False, "message": f"Error creating Shopify order: {str(e)}"}), 500

@pos_bp.route('/pos/initiate_layaway', methods=['POST'])
@login_required
def initiate_layaway():
    data = request.json
    till_id = data.get('till_id')
    customer_id = data.get('customer_id')
    items = data.get('items')
    total = data.get('total')

    if not all([till_id, customer_id, items, total]):
        return jsonify({"success": False, "message": "Missing required fields"}), 400

    default_deposit = round(float(total) * 0.2, 2)

    return jsonify({
        "success": True,
        "message": "Layaway initiated",
        "default_deposit": default_deposit
    }), 200

@pos_bp.route('/pos/create_layaway', methods=['POST'])
@login_required
def create_layaway():
    data = request.json
    till_id = data.get('till_id')
    customer_id = data.get('customer_id')
    items = data.get('items')
    total = data.get('total')
    deposit = data.get('deposit')

    if not all([till_id, customer_id, items, total, deposit]):
        return jsonify({"success": False, "message": "Missing required fields"}), 400

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except Exception as e:
        return jsonify({"success": False, "message": "Invalid till ID"}), 400

    layaway = Layaway(
        customer_id=customer_id,
        items=items,
        total=float(total),
        deposit=float(deposit),
        remaining_balance=float(total) - float(deposit),
        due_date=datetime.utcnow() + timedelta(days=14),
        till=till
    )
    layaway.save()
    layaway_id = str(layaway.id)

    # Filter out custom items that don't affect inventory
    non_custom_items = [item for item in items if not (
        item.get("is_custom") or
        (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
    )]

    # Adjust Shopify inventory if non-custom items exist
    if non_custom_items:
        # Negative adjustment to reduce inventory
        inventory_adjusted = adjust_shopify_inventory_for_reserved_items(
            non_custom_items, -1, current_user.username
        )

        if inventory_adjusted:
            # Track these items as reserved
            for item in non_custom_items:
                reserved_items_collection.insert_one({
                    "variant_id": item["variant_id"],
                    "quantity": item["quantity"],
                    "reservation_type": "layaway",
                    "reservation_id": layaway_id,
                    "username": current_user.username,
                    "timestamp": datetime.utcnow()
                })
            current_app.logger.info(f"Reserved {len(non_custom_items)} items in layaway {layaway_id}")
        else:
            current_app.logger.warning(f"Failed to adjust inventory for layaway {layaway_id}")

    till.update(inc__running_total=float(deposit))

    return jsonify({
        "success": True,
        "message": "Layaway created successfully",
        "layaway_id": layaway_id
    }), 201

@pos_bp.route('/pos/get_layaways', methods=['GET'])
@login_required
def get_layaways():
    customer_id = request.args.get('customer_id')
    status = request.args.get('status', 'active')
    username = current_user.username

    # Modified query to remove username field which doesn't exist in Layaway model
    query = {'status': status}
    if customer_id:
        query['customer_id'] = customer_id

    # Get all layaways matching the query
    layaways = Layaway.objects(**query)

    # For each layaway, try to retrieve customer details
    layaway_list = []
    for layaway in layaways:
        # Calculate total and item count
        items_count = len(layaway.items) if layaway.items else 0

        # Get reservation status from reserved_items_collection
        reserved_items = list(reserved_items_collection.find({
            "reservation_type": "layaway",
            "reservation_id": str(layaway.id),
            "username": current_user.username
        }))

        # Calculate total reserved quantity
        total_reserved_quantity = sum(item.get('quantity', 0) for item in reserved_items)

        # Get details about reserved items
        reserved_item_details = []
        for item in reserved_items:
            variant_id = item.get('variant_id')
            quantity = item.get('quantity', 0)

            # Try to get product details for this variant
            product_info = None
            try:
                # Normalize variant_id for query
                normalized_variant_id = str(variant_id)

                # Try different query formats to find the variant
                query_formats = [
                    {"variants.id": normalized_variant_id},
                    {"variants.id": int(normalized_variant_id)} if normalized_variant_id.isdigit() else None,
                    {"variants.id": {"$numberLong": normalized_variant_id}}
                ]

                for query in query_formats:
                    if query:
                        product = db.shProducts.find_one(
                            {**query, "username": current_user.username},
                            {"title": 1, "variants.$": 1}
                        )
                        if product:
                            product_info = {
                                "product_title": product.get('title', 'Unknown Product'),
                                "variant_title": product['variants'][0].get('title', 'Unknown Variant') if product.get('variants') else 'Unknown Variant'
                            }
                            break
            except Exception as e:
                current_app.logger.error(f"Error getting product info for variant {variant_id}: {str(e)}")

            reserved_item_details.append({
                "variant_id": variant_id,
                "quantity": quantity,
                "product_title": product_info.get('product_title', 'Unknown Product') if product_info else 'Unknown Product',
                "variant_title": product_info.get('variant_title', 'Unknown Variant') if product_info else 'Unknown Variant'
            })

        # Safely get till information, handling the case where the till might not exist
        till_id = "unknown"
        till_name = "Unknown Till"
        try:
            # Check if till reference exists and is valid
            if layaway.till and isinstance(layaway.till, PosSetting):
                till_id = str(layaway.till.id)
                till_name = layaway.till.location
            elif hasattr(layaway, 'till') and hasattr(layaway.till, 'id'):
                # Try to get the till ID even if we can't access the full object
                till_id = str(layaway.till.id)
                # We can't safely get the location name in this case
        except Exception as e:
            current_app.logger.error(f"Error accessing till for layaway {layaway.id}: {str(e)}")

        layaway_data = {
            "id": str(layaway.id),
            "customer_id": layaway.customer_id,
            "total": layaway.total,
            "deposit": layaway.deposit,
            "remaining_balance": layaway.remaining_balance,
            "due_date": layaway.due_date.isoformat(),
            "status": layaway.status,
            "customer_name": None,
            "items_count": items_count,
            "created_at": layaway.id.generation_time.isoformat() if hasattr(layaway.id, 'generation_time') else None,
            "reserved_items_count": len(reserved_items),
            "total_reserved_quantity": total_reserved_quantity,
            "reserved_items": reserved_item_details,
            "till_id": till_id,
            "till_name": till_name
        }

        # Try to get customer details if customer_id exists
        if layaway.customer_id:
            try:
                # Try different formats for customer ID
                customer = None
                customer_id = layaway.customer_id

                # 1. Direct match
                customer = db.shCustomers.find_one({"id": customer_id, "username": username})

                # 2. Integer match (if numeric)
                if not customer and isinstance(customer_id, str) and customer_id.isdigit():
                    customer = db.shCustomers.find_one({"id": int(customer_id), "username": username})

                # 3. MongoDB $numberLong format
                if not customer:
                    customer = db.shCustomers.find_one({
                        "id": {"$numberLong": str(customer_id)},
                        "username": username
                    })

                # 4. Direct ObjectId match (if it's a valid ObjectId)
                if not customer:
                    try:
                        customer = db.shCustomers.find_one({"_id": ObjectId(customer_id), "username": username})
                    except:
                        pass

                if customer:
                    layaway_data["customer_name"] = f"{customer.get('first_name', '')} {customer.get('last_name', '')}".strip()
            except Exception as e:
                current_app.logger.error(f"Error fetching customer for layaway {str(layaway.id)}: {str(e)}")

        layaway_list.append(layaway_data)

    return jsonify({
        "success": True,
        "layaways": layaway_list
    }), 200

@pos_bp.route('/pos/complete_layaway/<layaway_id>', methods=['POST'])
@login_required
def complete_layaway(layaway_id):
    data = request.json
    payment_method = data.get('payment_method')
    amount_paid = data.get('amount_paid')
    employee_name = data.get('employee_name')

    if not employee_name:
        return jsonify({"success": False, "message": "Employee name is required"}), 400

    try:
        layaway = Layaway.objects.get(id=ObjectId(layaway_id))
    except Exception as e:
        return jsonify({"success": False, "message": "Invalid layaway ID"}), 400

    if layaway.status != 'active':
        return jsonify({"success": False, "message": "Layaway is not active"}), 400

    if amount_paid < layaway.remaining_balance:
        return jsonify({"success": False, "message": "Amount paid is less than remaining balance"}), 400

    # When completing a layaway, we should remove the reservation records
    # but not restore inventory since the items are being sold
    reserved_items_collection.delete_many({
        "reservation_type": "layaway",
        "reservation_id": layaway_id,
        "username": current_user.username
    })
    current_app.logger.info(f"Removed reservation records for completed layaway {layaway_id}")

    layaway.status = 'completed'
    layaway.save()

    transaction_data = {
        'till_id': str(layaway.till.id),
        'payment_method': payment_method,
        'total': amount_paid,
        'items': layaway.items,
        'customer_id': layaway.customer_id,
        'is_layaway': True,
        'layaway_id': str(layaway.id),
        'employee_name': employee_name
    }

    return complete_transaction(transaction_data)

@pos_bp.route('/pos/cancel_layaway/<layaway_id>', methods=['POST'])
@login_required
def cancel_layaway(layaway_id):
    try:
        layaway = Layaway.objects.get(id=ObjectId(layaway_id))
    except Exception as e:
        return jsonify({"success": False, "message": "Invalid layaway ID"}), 400

    if layaway.status != 'active':
        return jsonify({"success": False, "message": "Layaway is not active"}), 400

    # Get reserved items for this layaway
    reserved_items = list(reserved_items_collection.find({
        "reservation_type": "layaway",
        "reservation_id": layaway_id,
        "username": current_user.username
    }))

    # If we have reserved items, restore inventory
    if reserved_items:
        items_to_restore = []
        for item in reserved_items:
            items_to_restore.append({
                "variant_id": item["variant_id"],
                "quantity": item["quantity"]
            })

        # Positive adjustment to restore inventory
        if items_to_restore:
            inventory_restored = adjust_shopify_inventory_for_reserved_items(
                items_to_restore, 1, current_user.username
            )

            if inventory_restored:
                # Remove reservation records
                reserved_items_collection.delete_many({
                    "reservation_type": "layaway",
                    "reservation_id": layaway_id,
                    "username": current_user.username
                })
                current_app.logger.info(f"Restored {len(items_to_restore)} items from cancelled layaway {layaway_id}")
            else:
                current_app.logger.warning(f"Failed to restore inventory for cancelled layaway {layaway_id}")

    # Update layaway status
    layaway.status = 'cancelled'
    layaway.save()

    # Update till running total
    till = layaway.till
    till.update(inc__running_total=-layaway.deposit)

    return jsonify({
        "success": True,
        "message": "Layaway cancelled successfully",
        "refunded_amount": layaway.deposit
    }), 200

@pos_bp.route('/pos/create_employee', methods=['POST'])
@login_required
def create_employee():
    data = request.json
    name = data.get('name')
    pin = data.get('pin')

    if not name or not pin:
        return jsonify({"error": "Name and PIN are required"}), 400

    employee = Employee(
        name=name,
        pin=pin,
        username=current_user.username
    )
    employee.save()
    return jsonify({
        "id": str(employee.id),
        "name": employee.name,
        "pin": employee.pin
    }), 201

@pos_bp.route('/pos/get_employees', methods=['GET'])
@login_required
def get_employees():
    employees = Employee.objects(username=current_user.username).all()
    return jsonify({
        "employees": [{
            "id": str(employee.id),
            "name": employee.name,
            "pin": employee.pin
        } for employee in employees]
    }), 200

@pos_bp.route('/pos/validate_employee_pin', methods=['POST'])
@login_required
def validate_employee_pin():
    data = request.json
    pin = data.get('pin')

    if not pin:
        return jsonify({"success": False, "message": "PIN is required"}), 400

    employee = Employee.objects(username=current_user.username, pin=pin).first()
    if employee:
        return jsonify({"success": True, "employee": {"id": str(employee.id), "name": employee.name}}), 200
    else:
        return jsonify({"success": False, "message": "Invalid PIN"}), 401

@pos_bp.route('/pos/hold_sale', methods=['POST'])
@login_required
def hold_sale():
    data = request.json
    till_id = data.get('till_id')
    items = data.get('items')
    customer_id = data.get('customer_id')
    customer_name = data.get('customer_name')  # Added customer name field
    employee_name = data.get('employee_name')

    current_app.logger.info(f"Hold sale request received: till_id={till_id}, items_count={len(items) if items else 0}, customer_id={customer_id}, customer_name={customer_name}")

    if not till_id or not items or not employee_name:
        current_app.logger.error(f"Missing required fields for hold sale: till_id={till_id}, items={bool(items)}, employee_name={bool(employee_name)}")
        return jsonify({"success": False, "message": "Missing required fields"}), 400

    try:
        # Create the held sale first (to get the ID)
        held_sale = HeldSale(
            till=ObjectId(till_id),
            items=items,
            customer_id=customer_id,
            customer_name=customer_name,  # Added customer name to held sale
            employee_name=employee_name,
            username=current_user.username
        )
        held_sale.save()
        held_sale_id = str(held_sale.id)
        current_app.logger.info(f"Created held sale with ID: {held_sale_id}")

        # Filter out custom items that don't affect inventory
        non_custom_items = [item for item in items if not (
            item.get("is_custom") or
            (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
        )]

        current_app.logger.info(f"Found {len(non_custom_items)} non-custom items that need inventory adjustment")

        # Adjust Shopify inventory if non-custom items exist
        if non_custom_items:
            try:
                # Negative adjustment to reduce inventory
                inventory_adjusted = adjust_shopify_inventory_for_reserved_items(
                    non_custom_items, -1, current_user.username
                )

                if inventory_adjusted:
                    # Track these items as reserved
                    reservation_records = []
                    for item in non_custom_items:
                        reservation_record = {
                            "variant_id": item["variant_id"],
                            "quantity": item["quantity"],
                            "reservation_type": "hold",
                            "reservation_id": held_sale_id,
                            "username": current_user.username,
                            "timestamp": datetime.utcnow()
                        }
                        reserved_items_collection.insert_one(reservation_record)
                        reservation_records.append(reservation_record)

                    current_app.logger.info(f"Reserved {len(non_custom_items)} items in hold sale {held_sale_id}")
                    current_app.logger.debug(f"Reservation records created: {reservation_records}")
                else:
                    current_app.logger.warning(f"Failed to adjust inventory for hold sale {held_sale_id}")
                    # Continue anyway - the sale is held but inventory might not be properly adjusted
            except Exception as e:
                current_app.logger.error(f"Error adjusting inventory for hold sale {held_sale_id}: {str(e)}")
                # Continue anyway - the sale is held but inventory might not be properly adjusted

        return jsonify({"success": True, "message": "Sale held successfully", "held_sale_id": held_sale_id}), 201
    except Exception as e:
        current_app.logger.error(f"Error creating held sale: {str(e)}")
        return jsonify({"success": False, "message": f"Error creating held sale: {str(e)}"}), 500

@pos_bp.route('/pos/get_held_sales', methods=['GET'])
@login_required
def get_held_sales():
    held_sales = HeldSale.objects(username=current_user.username).order_by('-timestamp').all()

    # Format the response with detailed information about each held sale
    formatted_sales = []
    for sale in held_sales:
        try:
            # Calculate total and item count
            total = sum(float(item.get('price', 0)) * int(item.get('quantity', 1)) for item in sale.items) if sale.items else 0
            items_count = len(sale.items) if sale.items else 0

            # Get reservation status from reserved_items_collection
            reserved_items = list(reserved_items_collection.find({
                "reservation_type": "hold",
                "reservation_id": str(sale.id),
                "username": current_user.username
            }))

            # Calculate total reserved quantity
            total_reserved_quantity = sum(item.get('quantity', 0) for item in reserved_items)

            # Get details about reserved items
            reserved_item_details = []
            for item in reserved_items:
                variant_id = item.get('variant_id')
                quantity = item.get('quantity', 0)

                # Try to get product details for this variant
                product_info = None
                try:
                    # Normalize variant_id for query
                    normalized_variant_id = str(variant_id)

                    # Try different query formats to find the variant
                    query_formats = [
                        {"variants.id": normalized_variant_id},
                        {"variants.id": int(normalized_variant_id)} if normalized_variant_id.isdigit() else None,
                        {"variants.id": {"$numberLong": normalized_variant_id}}
                    ]

                    for query in query_formats:
                        if query:
                            product = db.shProducts.find_one(
                                {**query, "username": current_user.username},
                                {"title": 1, "variants.$": 1}
                            )
                            if product:
                                product_info = {
                                    "product_title": product.get('title', 'Unknown Product'),
                                    "variant_title": product['variants'][0].get('title', 'Unknown Variant') if product.get('variants') else 'Unknown Variant'
                                }
                                break
                except Exception as e:
                    current_app.logger.error(f"Error getting product info for variant {variant_id}: {str(e)}")

                reserved_item_details.append({
                    "variant_id": variant_id,
                    "quantity": quantity,
                    "product_title": product_info.get('product_title', 'Unknown Product') if product_info else 'Unknown Product',
                    "variant_title": product_info.get('variant_title', 'Unknown Variant') if product_info else 'Unknown Variant'
                })

            # Safely get till information, handling the case where the till might not exist
            # We need to be able to access all held sales even if the till is deleted
            till_id = "unknown"
            till_name = "Unknown Till"
            try:
                # Check if till reference exists and is valid
                if sale.till and isinstance(sale.till, PosSetting):
                    till_id = str(sale.till.id)
                    till_name = sale.till.location
                elif hasattr(sale, 'till') and hasattr(sale.till, 'id'):
                    # Try to get the till ID even if we can't access the full object
                    till_id = str(sale.till.id)
            except Exception as e:
                current_app.logger.error(f"Error accessing till for held sale {sale.id}: {str(e)}")

            # Format the sale data
            formatted_sale = {
                "id": str(sale.id),
                "till": till_id,
                "till_name": till_name,
                "items": sale.items,
                "items_count": items_count,
                "total": total,
                "customer_id": sale.customer_id,
                "customer_name": sale.customer_name or "No Customer",
                "employee_name": sale.employee_name,
                "timestamp": sale.timestamp.isoformat(),
                "age_hours": round((datetime.utcnow() - sale.timestamp).total_seconds() / 3600, 1),
                "reserved_items_count": len(reserved_items),
                "total_reserved_quantity": total_reserved_quantity,
                "reserved_items": reserved_item_details
            }

            formatted_sales.append(formatted_sale)
        except Exception as e:
            current_app.logger.error(f"Error processing held sale {sale.id}: {str(e)}")
            # Continue processing other held sales even if one fails

    return jsonify({
        "success": True,
        "held_sales": formatted_sales
    }), 200

@pos_bp.route('/pos/retrieve_held_sale/<held_sale_id>', methods=['POST'])
@login_required
def retrieve_held_sale(held_sale_id):
    try:
        held_sale = HeldSale.objects.get(id=ObjectId(held_sale_id), username=current_user.username)
    except Exception as e:
        current_app.logger.error(f"Error retrieving held sale: {str(e)}")
        return jsonify({"success": False, "message": "Invalid held sale ID"}), 400

    # Get reserved items for this hold
    reserved_items = list(reserved_items_collection.find({
        "reservation_type": "hold",
        "reservation_id": held_sale_id,
        "username": current_user.username
    }))

    # Mark items as from_held_sale to prevent double inventory adjustment
    marked_items = []
    for item in held_sale.items:
        item_copy = dict(item)
        item_copy['from_held_sale'] = True
        marked_items.append(item_copy)

    # Safely get till information, handling the case where the till might not exist
    till_id = "unknown"
    try:
        # Check if till reference exists and is valid
        if held_sale.till and isinstance(held_sale.till, PosSetting):
            till_id = str(held_sale.till.id)
        elif hasattr(held_sale, 'till') and hasattr(held_sale.till, 'id'):
            # Try to get the till ID even if we can't access the full object
            till_id = str(held_sale.till.id)
    except Exception as e:
        current_app.logger.error(f"Error accessing till for held sale {held_sale.id}: {str(e)}")

    current_app.logger.info(f"Retrieved held sale {held_sale_id} with {len(marked_items)} items and {len(reserved_items)} reserved items")

    return jsonify({
        "success": True,
        "held_sale": {
            "id": str(held_sale.id),
            "till": till_id,
            "items": marked_items,
            "customer_id": held_sale.customer_id,
            "customer_name": held_sale.customer_name,
            "employee_name": held_sale.employee_name,
            "timestamp": held_sale.timestamp.isoformat(),
            "reserved_items_count": len(reserved_items)
        }
    }), 200

@pos_bp.route('/pos/remove_held_sale/<held_sale_id>', methods=['POST'])
@login_required
def remove_held_sale(held_sale_id):
    try:
        held_sale = HeldSale.objects.get(id=ObjectId(held_sale_id), username=current_user.username)
    except Exception as e:
        return jsonify({"success": False, "message": "Invalid held sale ID"}), 400

    # Get reserved items for this hold
    reserved_items = list(reserved_items_collection.find({
        "reservation_type": "hold",
        "reservation_id": held_sale_id,
        "username": current_user.username
    }))

    # If we have reserved items, restore inventory
    if reserved_items:
        items_to_restore = []
        for item in reserved_items:
            items_to_restore.append({
                "variant_id": item["variant_id"],
                "quantity": item["quantity"]
            })

        # Positive adjustment to restore inventory
        if items_to_restore:
            inventory_restored = adjust_shopify_inventory_for_reserved_items(
                items_to_restore, 1, current_user.username
            )

            if inventory_restored:
                # Remove reservation records
                reserved_items_collection.delete_many({
                    "reservation_type": "hold",
                    "reservation_id": held_sale_id,
                    "username": current_user.username
                })
                current_app.logger.info(f"Restored {len(items_to_restore)} items from removed hold sale {held_sale_id}")
            else:
                current_app.logger.warning(f"Failed to restore inventory for removed hold sale {held_sale_id}")

    # Delete the held sale
    held_sale.delete()

    return jsonify({
        "success": True,
        "message": "Held sale removed successfully"
    }), 200

@pos_bp.route('/pos/get_product/<product_id>', methods=['GET'])
@login_required
def get_product(product_id):
    current_app.logger.info(f"Attempting to fetch product with ID: {product_id}")

    if not product_id:
        current_app.logger.error("No product ID provided")
        return jsonify({"success": False, "message": "No product ID provided"}), 400

    # Clean and validate the product ID
    cleaned_product_id = str(product_id).strip()

    # Check for common invalid formats
    if cleaned_product_id in ['null', 'undefined', 'None', '']:
        current_app.logger.error(f"Invalid product ID value: {product_id}")
        return jsonify({"success": False, "message": "Invalid product ID format"}), 400

    try:
        object_id = ObjectId(cleaned_product_id)
        current_app.logger.info(f"Successfully converted product ID to ObjectId: {object_id}")
    except InvalidId as e:
        current_app.logger.error(f"Invalid product ID format: {product_id} - Error: {str(e)}")
        return jsonify({"success": False, "message": f"Invalid product ID format: {product_id}"}), 400
    except Exception as e:
        current_app.logger.error(f"Unexpected error converting product ID: {product_id} - Error: {str(e)}")
        return jsonify({"success": False, "message": "Invalid product ID format"}), 400

    try:
        product = db.shProducts.find_one({"_id": object_id, "username": current_user.username})
        current_app.logger.info(f"Database query completed for product ID: {object_id}")
    except Exception as e:
        current_app.logger.error(f"Error querying database for product {product_id}: {str(e)}")
        return jsonify({"success": False, "message": "Error querying database"}), 500

    if not product:
        current_app.logger.warning(f"Product not found for ID: {product_id} (ObjectId: {object_id})")
        return jsonify({"success": False, "message": f"Product not found for ID: {product_id}"}), 404

    if not product.get('variants'):
        current_app.logger.warning(f"Product {product_id} has no variants")
        return jsonify({"success": False, "message": "Product has no variants"}), 404

    variant = product['variants'][0]  # Assuming we're using the first variant

    result = {
        "id": str(product['_id']),
        "title": product.get('title', 'No Title'),
        "variant": {
            "id": str(variant['id']),
            "title": variant.get('title', 'No Title'),
            "price": variant.get('price', '0.00'),
        }
    }

    current_app.logger.info(f"Successfully fetched product: {result['title']} (ID: {result['id']})")
    return jsonify({"success": True, "product": result}), 200

@pos_bp.route('/pos/close_till', methods=['POST', 'PUT'])
@login_required
def close_till():
    data = request.json
    till_id = data.get('till_id')

    if not till_id:
        return jsonify({"success": False, "message": "Till ID is required"}), 400

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404
    except Exception as e:
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500

    # Get the actual cash count and cash count details from the request
    actual_cash = data.get('actual_cash')
    cash_count_data = data.get('cash_count')

    if not actual_cash:
        return jsonify({"success": False, "message": "Actual cash count is required"}), 400

    try:
        actual_cash = float(actual_cash)
    except ValueError:
        return jsonify({"success": False, "message": "Actual cash count must be a number"}), 400

    # Calculate financial summaries
    transactions = till.transactions

    # Calculate sales by payment method
    total_sales = 0
    cash_sales = 0
    card_sales = 0
    gift_card_sales = 0
    store_credit_sales = 0
    total_tax_collected = 0
    total_payouts = 0
    payout_transactions = []

    for transaction in transactions:
        # Handle payouts separately (they have negative totals and 'payout' payment method)
        if transaction.payment_method == 'payout':
            # Payouts are stored as negative amounts, so we take the absolute value
            payout_amount = abs(transaction.total)
            total_payouts += payout_amount
            payout_transactions.append(transaction)
            continue

        # Skip other transactions with negative totals (refunds, etc.)
        if transaction.total < 0:
            continue

        total_sales += transaction.total

        # Track sales by payment method
        if transaction.payment_method == 'cash':
            cash_sales += transaction.total
        elif transaction.payment_method == 'card':
            card_sales += transaction.total

        # Track gift card and store credit sales
        if hasattr(transaction, 'gift_card_amount') and transaction.gift_card_amount:
            gift_card_sales += transaction.gift_card_amount

        if hasattr(transaction, 'store_credit_amount') and transaction.store_credit_amount:
            store_credit_sales += transaction.store_credit_amount

        # Calculate tax collected (if available in transaction data)
        for item in transaction.items:
            if 'tax_amount' in item:
                total_tax_collected += float(item['tax_amount'])

    # Calculate expected cash (starting float + cash sales - payouts)
    expected_cash = float(till.starting_float) + cash_sales - total_payouts

    # Calculate discrepancy
    cash_discrepancy = actual_cash - expected_cash

    # Get the next Z-report number
    from models.pos_model import TillClosing
    last_closing = TillClosing.objects(username=current_user.username).order_by('-z_report_number').first()
    z_report_number = 1
    if last_closing and last_closing.z_report_number:
        z_report_number = last_closing.z_report_number + 1

    # Create a new TillClosing document
    till_closing = TillClosing(
        till_id=str(till.id),
        location=till.location,
        username=current_user.username,
        employee_name=till.staff_name if hasattr(till, 'staff_name') else None,

        business_date=datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0),
        closing_time=datetime.utcnow(),

        starting_float=till.starting_float,
        ending_float=actual_cash,
        expected_cash=expected_cash,
        cash_discrepancy=cash_discrepancy,

        total_sales=total_sales,
        cash_sales=cash_sales,
        card_sales=card_sales,
        gift_card_sales=gift_card_sales,
        store_credit_sales=store_credit_sales,
        total_payouts=total_payouts,

        transaction_count=len(transactions),
        transactions=transactions,

        total_tax_collected=total_tax_collected,

        discrepancy_notes=data.get('discrepancy', ''),
        closing_notes=data.get('notes', ''),

        # Store the cash count data
        cash_count=cash_count_data,

        z_report_number=z_report_number
    )

    # Save the TillClosing document
    till_closing.save()

    # Update the till
    # Set the running total to the actual cash count
    till.running_total = actual_cash

    # Clear the transactions list
    till.transactions = []

    # Set the status or is_closed field
    if hasattr(till, 'status'):
        till.status = 'closed'
    else:
        till.is_closed = True

    # Save the changes
    till.save()

    # Generate a simple HTML report for the closing
    html_report = f"""
    <div class="till-closing-report">
        <h2>Till Closing Report</h2>
        <h3>Z-Report #{z_report_number}</h3>
        <p><strong>Date:</strong> {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}</p>
        <p><strong>Location:</strong> {till.location}</p>
        <p><strong>Employee:</strong> {till.staff_name if hasattr(till, 'staff_name') else 'Unknown'}</p>

        <div class="financial-summary">
            <h4>Financial Summary</h4>
            <p><strong>Starting Float:</strong> ${till.starting_float:.2f}</p>
            <p><strong>Total Sales:</strong> ${total_sales:.2f}</p>
            <p><strong>Cash Sales:</strong> ${cash_sales:.2f}</p>
            <p><strong>Card Sales:</strong> ${card_sales:.2f}</p>
            <p><strong>Gift Card Sales:</strong> ${gift_card_sales:.2f}</p>
            <p><strong>Store Credit Sales:</strong> ${store_credit_sales:.2f}</p>
            <p><strong>Total Payouts:</strong> ${total_payouts:.2f}</p>
            <p><strong>Expected Cash:</strong> ${expected_cash:.2f}</p>
            <p><strong>Actual Cash:</strong> ${actual_cash:.2f}</p>
            <p><strong>Discrepancy:</strong> ${cash_discrepancy:.2f}</p>
            <p><strong>Total Tax Collected:</strong> ${total_tax_collected:.2f}</p>
        </div>

        <div class="transaction-summary">
            <h4>Transaction Summary</h4>
            <p><strong>Total Transactions:</strong> {len(transactions)}</p>
            <p><strong>Total Payouts:</strong> {len(payout_transactions)}</p>
        </div>

        <div class="notes">
            <h4>Notes</h4>
            <p>{data.get('notes', 'No notes provided.')}</p>
        </div>
    </div>
    """

    # Save the HTML report
    from models.pos_model import TillClosingReport
    report = TillClosingReport(
        till_closing=till_closing,
        html_report=html_report
    )
    report.save()

    return jsonify({
        "success": True,
        "message": "Till closed successfully",
        "z_report_number": z_report_number,
        "closing_id": str(till_closing.id),
        "report_id": str(report.id)
    }), 200

@pos_bp.route('/pos/create_currency', methods=['POST'])
@login_required
def create_currency():
    data = request.json
    name = data.get('name')
    rate = data.get('rate')

    if not name or rate is None:
        return jsonify({"error": "Currency name and rate are required"}), 400

    currency = Currency(
        name=name,
        rate=rate,
        username=current_user.username
    )
    currency.save()
    return jsonify({
        "id": str(currency.id),
        "name": currency.name,
        "rate": currency.rate
    }), 201

@pos_bp.route('/pos/get_currencies', methods=['GET'])
@login_required
def get_currencies():
    currencies = Currency.objects(username=current_user.username).all()

    # Get user's preferred currency from settings
    user_settings = db.user_settings.find_one({"username": current_user.username})
    preferred_currency = None
    currency_symbol = "$"  # Default symbol

    if user_settings and "preferred_currency" in user_settings:
        preferred_currency = user_settings["preferred_currency"]
        # Find the currency object with the preferred currency ID
        for currency in currencies:
            if str(currency.id) == preferred_currency:
                currency_symbol = currency.symbol if hasattr(currency, 'symbol') else currency_symbol
                break

    return jsonify({
        "currencies": [{
            "id": str(currency.id),
            "name": currency.name,
            "rate": currency.rate,
            "symbol": currency.symbol if hasattr(currency, 'symbol') else "$",
            "is_default": str(currency.id) == preferred_currency if preferred_currency else False
        } for currency in currencies],
        "preferred_currency": preferred_currency,
        "currency_symbol": currency_symbol
    }), 200

@pos_bp.route('/pos/get_user_currency', methods=['GET'])
@login_required
def get_user_currency():
    """Get the user's preferred currency symbol"""
    # Get user's preferred currency from settings
    user_settings = db.user_settings.find_one({"username": current_user.username})
    preferred_currency = None
    currency_symbol = "$"  # Default symbol

    if user_settings and "preferred_currency" in user_settings:
        preferred_currency = user_settings["preferred_currency"]
        # Find the currency object with the preferred currency ID
        currency = Currency.objects(id=preferred_currency, username=current_user.username).first()
        if currency and hasattr(currency, 'symbol'):
            currency_symbol = currency.symbol

    # If no preferred currency is set, check if there's a default currency for the user's country
    if not preferred_currency:
        # Try to get user's country from settings
        country_code = None
        if user_settings and "country" in user_settings:
            country_code = user_settings["country"]

        # Map common country codes to currency symbols
        country_to_symbol = {
            "US": "$",
            "CA": "CA$",
            "GB": "£",
            "EU": "€",
            "JP": "¥",
            "CN": "¥",
            "AU": "A$",
            "NZ": "NZ$",
            "IN": "₹",
            "RU": "₽",
            "BR": "R$",
            "KR": "₩",
            "ZA": "R",
            "CH": "CHF",
            "SE": "kr",
            "NO": "kr",
            "DK": "kr",
            "MX": "Mex$"
        }

        if country_code and country_code in country_to_symbol:
            currency_symbol = country_to_symbol[country_code]

    return jsonify({
        "currency_symbol": currency_symbol,
        "preferred_currency": preferred_currency
    }), 200

@pos_bp.route('/pos/get_system_currency', methods=['GET'])
@login_required
def get_system_currency():
    """Get the user's system currency code (USD, GBP, EUR, etc.)"""
    # Get user's preferred currency from settings
    user_settings = db.user_settings.find_one({"username": current_user.username})
    preferred_currency = None
    currency_code = "USD"  # Default currency code

    if user_settings and "preferred_currency" in user_settings:
        preferred_currency = user_settings["preferred_currency"]
        # Find the currency object with the preferred currency ID
        currency = Currency.objects(id=preferred_currency, username=current_user.username).first()
        if currency:
            currency_code = currency.name  # Use the currency name as the code

    # If no preferred currency is set, check if there's a default currency for the user's country
    if not preferred_currency:
        # Try to get user's country from settings
        country_code = None
        if user_settings and "country" in user_settings:
            country_code = user_settings["country"]

        # Map common country codes to currency codes
        country_to_currency = {
            "US": "USD",
            "CA": "CAD",
            "GB": "GBP",
            "EU": "EUR",
            "JP": "JPY",
            "CN": "CNY",
            "AU": "AUD",
            "NZ": "NZD",
            "IN": "INR",
            "RU": "RUB",
            "BR": "BRL",
            "KR": "KRW",
            "ZA": "ZAR",
            "CH": "CHF",
            "SE": "SEK",
            "NO": "NOK",
            "DK": "DKK",
            "MX": "MXN"
        }

        if country_code and country_code in country_to_currency:
            currency_code = country_to_currency[country_code]

    return jsonify({
        "success": True,
        "currency": currency_code
    }), 200

@pos_bp.route('/shopify/get_location/<till_id>', methods=['GET'])
@login_required
def get_shopify_location(till_id):
    """
    Get Shopify location details for a specific till.
    This endpoint returns the location details for a specific till, including name, address, etc.
    """
    try:
        # Get the till
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)

        # Check if the till has a Shopify location ID
        if not till.shopify_location_id:
            return jsonify({
                "success": False,
                "message": "This till does not have a Shopify location ID configured"
            }), 404

        # Set up Shopify API request
        shopify_headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": current_user.shopifyAccessToken
        }

        # Get the location details from Shopify
        location_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/locations/{till.shopify_location_id}.json"
        location_response = requests.get(location_url, headers=shopify_headers)
        location_response.raise_for_status()
        location = location_response.json().get('location', {})

        # Format the location for the response
        formatted_location = {
            "id": str(location.get('id')),
            "name": location.get('name', ''),
            "address1": location.get('address1', ''),
            "address2": location.get('address2', ''),
            "city": location.get('city', ''),
            "province": location.get('province', ''),
            "zip": location.get('zip', ''),
            "country": location.get('country', ''),
            "active": location.get('active', False)
        }

        return jsonify({
            "success": True,
            "location": formatted_location
        }), 200
    except InvalidId:
        return jsonify({
            "success": False,
            "message": "Invalid till ID format"
        }), 400
    except PosSetting.DoesNotExist:
        return jsonify({
            "success": False,
            "message": "Till not found"
        }), 404
    except requests.RequestException as e:
        current_app.logger.error(f"Error fetching Shopify location: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error fetching Shopify location: {str(e)}"
        }), 500
    except Exception as e:
        current_app.logger.error(f"Unexpected error fetching Shopify location: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Unexpected error: {str(e)}"
        }), 500

@pos_bp.route('/shopify/get_variant_locations/<variant_id>', methods=['GET'])
@login_required
def get_variant_locations(variant_id):
    """
    Get all Shopify locations where a specific variant is available.
    This endpoint returns all locations where the variant has inventory, along with inventory levels.
    """
    try:
        # Normalize variant_id
        normalized_variant_id = str(variant_id)

        # Try to find the variant with different ID formats
        possible_id_formats = [normalized_variant_id]

        # Add numeric format if the ID is a digit string
        if normalized_variant_id.isdigit():
            possible_id_formats.append(int(normalized_variant_id))

        # Use a single query with $in operator for string and numeric formats
        variant_info = db.shProducts.find_one(
            {
                "username": current_user.username,
                "variants.id": {"$in": possible_id_formats}
            },
            {"variants.$": 1}
        )

        # If not found, try with $numberLong format in a separate query
        if not variant_info:
            variant_info = db.shProducts.find_one(
                {
                    "username": current_user.username,
                    "variants.id": {"$numberLong": normalized_variant_id}
                },
                {"variants.$": 1}
            )

        if not variant_info or not variant_info.get('variants'):
            return jsonify({
                "success": False,
                "message": f"Variant info not found for variant {variant_id}"
            }), 404

        inventory_item_id = variant_info['variants'][0].get('inventory_item_id')
        if not inventory_item_id:
            return jsonify({
                "success": False,
                "message": f"No inventory_item_id found for variant {variant_id}"
            }), 404

        # Normalize inventory_item_id
        if isinstance(inventory_item_id, dict) and '$numberLong' in inventory_item_id:
            inventory_item_id = str(inventory_item_id['$numberLong'])
        else:
            inventory_item_id = str(inventory_item_id)

        # Set up Shopify API request
        shopify_headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": current_user.shopifyAccessToken
        }

        # Get all inventory levels for this inventory item
        inventory_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/inventory_levels.json?inventory_item_ids={inventory_item_id}"
        inventory_response = requests.get(inventory_url, headers=shopify_headers)
        inventory_response.raise_for_status()
        inventory_levels = inventory_response.json().get('inventory_levels', [])

        # Get all locations to match with inventory levels
        locations_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/locations.json"
        locations_response = requests.get(locations_url, headers=shopify_headers)
        locations_response.raise_for_status()
        locations = locations_response.json().get('locations', [])

        # Create a map of location IDs to location details
        location_map = {str(loc['id']): loc for loc in locations}

        # Format the inventory levels with location details
        formatted_levels = []
        for level in inventory_levels:
            location_id = str(level.get('location_id'))
            location = location_map.get(location_id, {})

            formatted_levels.append({
                "inventory_item_id": level.get('inventory_item_id'),
                "location_id": location_id,
                "available": level.get('available', 0),
                "updated_at": level.get('updated_at'),
                "location_name": location.get('name', 'Unknown Location'),
                "location_active": location.get('active', False),
                "address": {
                    "address1": location.get('address1', ''),
                    "address2": location.get('address2', ''),
                    "city": location.get('city', ''),
                    "province": location.get('province', ''),
                    "zip": location.get('zip', ''),
                    "country": location.get('country', '')
                }
            })

        # Sort by available quantity (descending) and then by location name
        formatted_levels.sort(key=lambda x: (-x['available'], x['location_name']))

        return jsonify({
            "success": True,
            "variant_id": variant_id,
            "inventory_item_id": inventory_item_id,
            "inventory_levels": formatted_levels
        }), 200
    except requests.RequestException as e:
        current_app.logger.error(f"Error fetching Shopify inventory levels: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error fetching Shopify inventory levels: {str(e)}"
        }), 500
    except Exception as e:
        current_app.logger.error(f"Unexpected error fetching inventory levels: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Unexpected error: {str(e)}"
        }), 500

@pos_bp.route('/pos/update_till_location/<till_id>', methods=['PUT'])
@login_required
def update_till_location(till_id):
    """
    Update a till's Shopify location and Merchant Match card machine settings.
    This endpoint allows updating the Shopify location and card machine settings of an existing till.
    """
    data = request.json
    shopify_location_id = data.get('shopify_location_id')
    shopify_location_name = data.get('shopify_location_name')

    # Merchant Match card machine settings
    tpn = data.get('tpn')
    register_id = data.get('register_id')
    auth_key = data.get('auth_key')

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404

    # Update the till's Shopify location
    till.shopify_location_id = shopify_location_id
    till.shopify_location_name = shopify_location_name

    # Update the till's Merchant Match card machine settings
    till.tpn = tpn
    till.register_id = register_id
    till.auth_key = auth_key

    till.save()

    return jsonify({
        "success": True,
        "message": "Till settings updated successfully",
        "till": {
            "id": str(till.id),
            "location": till.location,
            "shopify_location_id": till.shopify_location_id,
            "shopify_location_name": till.shopify_location_name,
            "tpn": till.tpn,
            "register_id": till.register_id,
            "auth_key": till.auth_key
        }
    }), 200

@pos_bp.route('/pos/get_till/<till_id>', methods=['GET'])
@login_required
def get_till(till_id):
    """
    Get a specific till's data.
    This endpoint returns all the till information including Shopify location and card machine settings.
    """
    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404

    return jsonify({
        "success": True,
        "till": {
            "id": str(till.id),
            "location": till.location,
            "starting_float": till.starting_float,
            "running_total": till.running_total,
            "tax_rate": till.tax_rate,
            "tax_inclusive": till.tax_inclusive,
            "shopify_location_id": till.shopify_location_id,
            "shopify_location_name": till.shopify_location_name,
            "tpn": till.tpn,
            "register_id": till.register_id,
            "auth_key": till.auth_key,
            "current_staff": till.current_staff,
            "staff_name": till.staff_name,
            "last_used": till.last_used.isoformat() if till.last_used else None
        }
    }), 200

@pos_bp.route('/pos/update_card_machine/<till_id>', methods=['PUT'])
@login_required
def update_card_machine(till_id):
    """
    Update a till's card machine settings.
    This endpoint allows updating the TPN, Register ID, and Auth Key of an existing till.
    """
    data = request.json
    tpn = data.get('tpn')
    register_id = data.get('register_id')
    auth_key = data.get('auth_key')

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404

    # Update the till's card machine settings
    till.tpn = tpn
    till.register_id = register_id
    till.auth_key = auth_key
    till.save()

    return jsonify({
        "success": True,
        "message": "Card machine settings updated successfully",
        "till": {
            "id": str(till.id),
            "location": till.location,
            "tpn": till.tpn,
            "register_id": till.register_id,
            "auth_key": till.auth_key
        }
    }), 200

@pos_bp.route('/pos/delete_till/<till_id>', methods=['DELETE'])
@login_required
def delete_till(till_id):
    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except Exception as e:
        return jsonify({"error": "Invalid till ID"}), 400

    till.delete()
    return jsonify({"success": True, "message": "Till deleted successfully"}), 200

@pos_bp.route('/pos/save_quicklink', methods=['POST'])
@login_required
def save_quicklink():
    data = request.json
    current_app.logger.info(f"Received quicklink data: {data}")

    till_id = data.get('till_id')
    quicklink_number = data.get('quicklink_number')
    product_id = data.get('product_id')

    current_app.logger.info(f"Parsed data - till_id: {till_id}, quicklink_number: {quicklink_number}, product_id: {product_id}")

    if not all([till_id, quicklink_number is not None, product_id]):
        return jsonify({"success": False, "message": "Missing required fields"}), 400
    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        current_app.logger.error(f"Invalid till ID format: {till_id}")
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        current_app.logger.error(f"Tiller not found for ID: {till_id}")
        return jsonify({"success": False, "message": "Till not found"}), 404

    # Clean and validate the product ID
    cleaned_product_id = str(product_id).strip()

    if cleaned_product_id in (None, '', 'null', 'undefined', 'None'):
        # Clear the quicklink
        current_app.logger.info(f"Clearing quicklink {quicklink_number} for till {till_id}")
        Quicklink.objects(till=till, quicklink_number=quicklink_number).delete()
        return jsonify({"success": True, "message": "Quicklink cleared successfully"}), 200

    try:
        object_id = ObjectId(cleaned_product_id)
        current_app.logger.info(f"Successfully converted product ID to ObjectId: {object_id}")
    except InvalidId as e:
        current_app.logger.error(f"Invalid product ID format: {product_id} - Error: {str(e)}")
        return jsonify({"success": False, "message": f"Invalid product ID format: {product_id}"}), 400
    except Exception as e:
        current_app.logger.error(f"Unexpected error converting product ID: {product_id} - Error: {str(e)}")
        return jsonify({"success": False, "message": "Invalid product ID format"}), 400

    # Verify the product exists and get its details
    try:
        product = db.shProducts.find_one({"_id": object_id, "username": current_user.username})
        if not product:
            current_app.logger.error(f"Product not found for ID: {product_id} (ObjectId: {object_id})")
            return jsonify({"success": False, "message": f"Product not found for ID: {product_id}"}), 404

        # Validate product has required fields
        product_title = product.get('title', '').strip()
        if not product_title:
            current_app.logger.error(f"Product {product_id} has no title")
            return jsonify({"success": False, "message": "Product has no title"}), 400

        current_app.logger.info(f"Found product: {product_title} (ID: {object_id})")

    except Exception as e:
        current_app.logger.error(f"Error querying product {product_id}: {str(e)}")
        return jsonify({"success": False, "message": f"Error querying product: {str(e)}"}), 500

    # Save the quicklink with validated data
    try:
        result = Quicklink.objects(till=till, quicklink_number=quicklink_number).update_one(
            set__product_id=object_id,
            set__product_name=product_title,
            set__username=current_user.username,
            upsert=True
        )
        current_app.logger.info(f"Quicklink update result: {result}")

    except Exception as e:
        current_app.logger.error(f"Error saving quicklink: {str(e)}")
        return jsonify({"success": False, "message": f"Error saving quicklink: {str(e)}"}), 500

    current_app.logger.info(f"Quicklink saved successfully for till {till_id}, quicklink number {quicklink_number}, product {product_id} ({product_title})")
    return jsonify({"success": True, "message": "Quicklink saved successfully", "product_name": product_title}), 200

@pos_bp.route('/pos/get_quicklinks/<till_id>', methods=['GET'])
@login_required
def get_quicklinks(till_id):
    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404

    quicklinks = Quicklink.objects(till=till, username=current_user.username).order_by('quicklink_number')

    return jsonify({
        "success": True,
        "quicklinks": [{
            "quicklink_number": ql.quicklink_number,
            "product_id": str(ql.product_id),
            "product_name": ql.product_name
        } for ql in quicklinks]
    }), 200

@pos_bp.route('/pos/save_category_search', methods=['POST'])
@login_required
def save_category_search():
    data = request.json
    current_app.logger.info(f"Received save category search data: {data}")

    vendor = data.get('vendor')
    product_type = data.get('product_type')
    display_name = data.get('display_name')
    in_stock_only = data.get('in_stock_only', True)

    if not all([vendor, product_type, display_name]):
        return jsonify({"success": False, "message": "Missing required fields"}), 400

    try:
        # Check if a search with the same vendor and product type already exists
        existing_search = SavedCategorySearch.objects(
            vendor=vendor,
            product_type=product_type,
            username=current_user.username
        ).first()

        if existing_search:
            # Update the existing search
            existing_search.display_name = display_name
            existing_search.in_stock_only = in_stock_only
            existing_search.timestamp = datetime.utcnow()
            existing_search.save()
            search_id = str(existing_search.id)
            message = "Search updated successfully"
        else:
            # Create a new saved search
            saved_search = SavedCategorySearch(
                vendor=vendor,
                product_type=product_type,
                display_name=display_name,
                in_stock_only=in_stock_only,
                username=current_user.username
            )
            saved_search.save()
            search_id = str(saved_search.id)
            message = "Search saved successfully"

        return jsonify({
            "success": True,
            "message": message,
            "search_id": search_id
        }), 200
    except Exception as e:
        current_app.logger.error(f"Error saving category search: {str(e)}")
        return jsonify({"success": False, "message": f"Error saving category search: {str(e)}"}), 500

@pos_bp.route('/pos/get_saved_category_searches', methods=['GET'])
@login_required
def get_saved_category_searches():
    try:
        saved_searches = SavedCategorySearch.objects(username=current_user.username).order_by('-timestamp')

        return jsonify({
            "success": True,
            "saved_searches": [{
                "id": str(search.id),
                "vendor": search.vendor,
                "product_type": search.product_type,
                "display_name": search.display_name,
                "in_stock_only": search.in_stock_only,
                "timestamp": search.timestamp.isoformat()
            } for search in saved_searches]
        }), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving saved category searches: {str(e)}")
        return jsonify({"success": False, "message": f"Error retrieving saved category searches: {str(e)}"}), 500

@pos_bp.route('/pos/remove_saved_category_search/<search_id>', methods=['DELETE'])
@login_required
def remove_saved_category_search(search_id):
    try:
        search = SavedCategorySearch.objects.get(id=ObjectId(search_id), username=current_user.username)
        search.delete()
        return jsonify({
            "success": True,
            "message": "Saved search removed successfully"
        }), 200
    except InvalidId:
        current_app.logger.error(f"Invalid search ID format: {search_id}")
        return jsonify({"success": False, "message": "Invalid search ID format"}), 400
    except SavedCategorySearch.DoesNotExist:
        current_app.logger.error(f"Saved search not found for ID: {search_id}")
        return jsonify({"success": False, "message": "Saved search not found"}), 404
    except Exception as e:
        current_app.logger.error(f"Error removing saved search: {str(e)}")
        return jsonify({"success": False, "message": f"Error removing saved search: {str(e)}"}), 500

@pos_bp.route('/pos/adjust_cart_inventory', methods=['POST'])
@login_required
def adjust_cart_inventory():
    """
    Adjust Shopify inventory when items are added to or removed from the cart.
    This endpoint is called from the frontend when the user adds or removes items from the cart.

    With the new inventory policy:
    - Do NOT adjust inventory when adding to cart (unless force_adjust=True)
    - Only adjust inventory for held sales, layaways, or completed transactions
    - Prevent double inventory deductions when completing held sales or layaways

    Parameters:
        - item: The item being added or removed
        - action: 'add' or 'remove'
        - quantity: The quantity being added or removed
        - till_id: (optional) The ID of the till to use for location information
        - from_held_sale: (optional) Whether this item is from a held sale
        - force_adjust: (optional) Force inventory adjustment for held sales/layaways/completed transactions
    """
    data = request.json
    item = data.get('item')
    action = data.get('action')  # 'add' or 'remove'
    quantity = data.get('quantity', 1)
    till_id = data.get('till_id')
    from_held_sale = data.get('from_held_sale', False)
    force_adjust = data.get('force_adjust', False)

    if not item or not action or not quantity:
        return jsonify({"success": False, "message": "Missing required fields"}), 400

    # Skip custom items (they don't affect inventory)
    is_custom = item.get("is_custom") or (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
    if is_custom:
        return jsonify({"success": True, "message": "Custom item, no inventory adjustment needed"}), 200

    # Skip inventory adjustment if the item is from a held sale and we're adding it to the cart
    # This is because the inventory was already adjusted when the sale was initially held
    if (from_held_sale or item.get('from_held_sale')) and action == 'add':
        current_app.logger.info(f"Skipping inventory adjustment for item from held sale: {item['variant_id']}")
        return jsonify({
            "success": True,
            "message": "Skipped inventory adjustment for item from held sale"
        }), 200

    # Skip inventory adjustment when adding to cart (unless forced)
    # Only adjust inventory for held sales, layaways, or when removing items from cart
    if action == 'add' and not force_adjust:
        current_app.logger.info(f"Skipping inventory adjustment when adding to cart: {item['variant_id']}")
        return jsonify({
            "success": True,
            "message": "Skipped inventory adjustment when adding to cart (new inventory policy)"
        }), 200

    # Determine the adjustment direction
    adjustment = -1 if action == 'add' else 1  # -1 to decrease inventory, 1 to increase

    # Get user's Shopify credentials
    from models.user_model import User
    user = User.objects(username=current_user.username).first()
    if not user:
        return jsonify({"success": False, "message": "User not found"}), 500

    store_name = user.shopifyStoreName.strip().replace('.myshopify.com', '')
    access_token = user.shopifyAccessToken.strip()

    if not store_name or not access_token:
        return jsonify({"success": False, "message": "Missing Shopify credentials"}), 500

    # Set up Shopify headers
    shopify_headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": access_token
    }

    # Get all locations from Shopify API
    try:
        locations_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/locations.json"
        locations_response = requests.get(locations_url, headers=shopify_headers)
        locations_response.raise_for_status()
        locations = locations_response.json().get('locations', [])

        if not locations:
            return jsonify({"success": False, "message": "No locations found for Shopify store"}), 500

        # Filter for active locations
        active_locations = [loc for loc in locations if loc.get('active', False)]
        if not active_locations:
            return jsonify({"success": False, "message": "No active locations found for Shopify store"}), 500

        # Create a map of location IDs to location details for quick lookup
        location_map = {str(loc['id']): loc for loc in locations}
    except Exception as e:
        current_app.logger.error(f"Error getting Shopify locations: {str(e)}")
        return jsonify({"success": False, "message": f"Error getting Shopify locations: {str(e)}"}), 500

    # Determine the best location to use
    best_location_id = None

    # If till_id is provided, try to use that till's location first
    if till_id:
        try:
            till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
            if till.shopify_location_id:
                # Verify this location ID is in our active locations
                location_exists = any(str(loc['id']) == str(till.shopify_location_id) for loc in active_locations)
                if location_exists:
                    best_location_id = till.shopify_location_id
                    current_app.logger.info(f"Using location ID {best_location_id} from till {till_id} for inventory adjustment")
        except Exception as e:
            current_app.logger.error(f"Error getting till for location ID: {str(e)}")

    # Process the item
    variant_id = item["variant_id"]

    try:
        # Normalize variant_id
        normalized_variant_id = str(variant_id)

        # Try to find the variant with different ID formats
        possible_id_formats = [normalized_variant_id]

        # Add numeric format if the ID is a digit string
        if normalized_variant_id.isdigit():
            possible_id_formats.append(int(normalized_variant_id))

        # Use a single query with $in operator for string and numeric formats
        variant_info = db.shProducts.find_one(
            {
                "username": current_user.username,
                "variants.id": {"$in": possible_id_formats}
            },
            {"variants.$": 1}
        )

        # If not found, try with $numberLong format in a separate query
        if not variant_info:
            variant_info = db.shProducts.find_one(
                {
                    "username": current_user.username,
                    "variants.id": {"$numberLong": normalized_variant_id}
                },
                {"variants.$": 1}
            )

        if not variant_info or not variant_info.get('variants'):
            return jsonify({"success": False, "message": f"Variant info not found for variant {variant_id}"}), 404

        inventory_item_id = variant_info['variants'][0].get('inventory_item_id')
        if not inventory_item_id:
            return jsonify({"success": False, "message": f"No inventory_item_id found for variant {variant_id}"}), 404

        # Normalize inventory_item_id
        if isinstance(inventory_item_id, dict) and '$numberLong' in inventory_item_id:
            inventory_item_id = str(inventory_item_id['$numberLong'])
        else:
            inventory_item_id = str(inventory_item_id)

        # If we don't have a location from the till, get inventory levels to find the best location
        if not best_location_id:
            try:
                inventory_levels_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/inventory_levels.json?inventory_item_ids={inventory_item_id}"
                inventory_levels_response = requests.get(inventory_levels_url, headers=shopify_headers)
                inventory_levels_response.raise_for_status()
                inventory_levels = inventory_levels_response.json().get('inventory_levels', [])

                if inventory_levels:
                    current_app.logger.info(f"Found {len(inventory_levels)} inventory levels for item {inventory_item_id}")

                    # For decreasing inventory (adjustment < 0), find location with highest available quantity
                    # For increasing inventory (adjustment > 0), use any active location
                    if adjustment < 0:
                        # Sort by available quantity (highest first)
                        inventory_levels.sort(key=lambda x: x.get('available', 0), reverse=True)

                        # Find the first level with sufficient quantity at an active location
                        for level in inventory_levels:
                            level_location_id = str(level.get('location_id'))
                            location_info = location_map.get(level_location_id)

                            if location_info and location_info.get('active', False):
                                available = level.get('available', 0)
                                if available >= quantity:
                                    best_location_id = level_location_id
                                    current_app.logger.info(f"Using location {best_location_id} with {available} available items")
                                    break

                        # If no location has sufficient quantity, use the one with the most available
                        if not best_location_id and inventory_levels:
                            for level in inventory_levels:
                                level_location_id = str(level.get('location_id'))
                                location_info = location_map.get(level_location_id)

                                if location_info and location_info.get('active', False):
                                    best_location_id = level_location_id
                                    current_app.logger.info(f"Using location {best_location_id} with highest available quantity")
                                    break
                    else:
                        # For increasing inventory, find any active location that already has this item
                        for level in inventory_levels:
                            level_location_id = str(level.get('location_id'))
                            location_info = location_map.get(level_location_id)

                            if location_info and location_info.get('active', False):
                                best_location_id = level_location_id
                                current_app.logger.info(f"Using location {best_location_id} for inventory increase")
                                break
            except Exception as e:
                current_app.logger.error(f"Error getting inventory levels: {str(e)}")
                # Continue with default location

        # If we still don't have a location, use the first active location
        if not best_location_id and active_locations:
            best_location_id = active_locations[0]['id']
            current_app.logger.info(f"Using first active location as default: {best_location_id}")

        if not best_location_id:
            return jsonify({"success": False, "message": "Failed to determine location for inventory adjustment"}), 500

        # Calculate adjustment value
        adjustment_value = adjustment * quantity

        # Adjust inventory in Shopify with the best location ID
        inventory_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/inventory_levels/adjust.json"
        inventory_data = {
            "inventory_item_id": inventory_item_id,
            "location_id": best_location_id,
            "available_adjustment": adjustment_value
        }

        current_app.logger.info(f"Adjusting Shopify inventory with data: {inventory_data}")

        # Add retry logic for inventory adjustment
        max_retries = 3
        retry_delay = 1  # seconds
        success = False

        for attempt in range(max_retries):
            try:
                inventory_response = requests.post(inventory_url, json=inventory_data, headers=shopify_headers)

                # Log the full response for debugging
                current_app.logger.info(f"Shopify response status: {inventory_response.status_code}")
                current_app.logger.info(f"Shopify response body: {inventory_response.text}")

                # Check for specific error messages that might indicate location issues
                if inventory_response.status_code == 422:
                    error_text = inventory_response.text.lower()
                    if "location" in error_text or "inventory" in error_text:
                        current_app.logger.error(f"Possible location or inventory issue: {inventory_response.text}")
                        # Try to get a different location if this is the first attempt
                        if attempt == 0:
                            current_app.logger.info("Attempting to find alternative location")
                            try:
                                # Try a different active location
                                for loc in active_locations:
                                    if str(loc['id']) != str(best_location_id):
                                        best_location_id = loc['id']
                                        inventory_data["location_id"] = best_location_id
                                        current_app.logger.info(f"Trying alternative location ID: {best_location_id}")
                                        break
                            except Exception as loc_err:
                                current_app.logger.error(f"Error getting alternative locations: {str(loc_err)}")
                        continue  # Retry with new location or same location

                inventory_response.raise_for_status()
                success = True
                break  # Success, exit retry loop

            except requests.RequestException as req_err:
                current_app.logger.error(f"Request error on attempt {attempt+1}: {str(req_err)}")
                if attempt < max_retries - 1:
                    current_app.logger.info(f"Retrying in {retry_delay} seconds...")
                    import time
                    time.sleep(retry_delay)
                else:
                    current_app.logger.error(f"Failed after {max_retries} attempts")
                    return jsonify({
                        "success": False,
                        "message": f"Failed to adjust inventory after multiple attempts: {str(req_err)}"
                    }), 500

        if success:
            return jsonify({
                "success": True,
                "message": f"Inventory {'deducted' if action == 'add' else 'restored'} successfully using location ID {best_location_id}"
            }), 200
        else:
            return jsonify({
                "success": False,
                "message": "Failed to adjust inventory after multiple attempts"
            }), 500

    except Exception as e:
        current_app.logger.error(f"Error adjusting inventory: {str(e)}")
        return jsonify({"success": False, "message": f"Error adjusting inventory: {str(e)}"}), 500

@pos_bp.route('/pos/remove_quicklink', methods=['POST'])
@login_required
def remove_quicklink():
    data = request.json
    current_app.logger.info(f"Received remove quicklink data: {data}")

    till_id = data.get('till_id')
    quicklink_number = data.get('quicklink_number')

    if not all([till_id, quicklink_number is not None]):
        return jsonify({"success": False, "message": "Missing required fields"}), 400

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        current_app.logger.error(f"Invalid till ID format: {till_id}")
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        current_app.logger.error(f"Till not found for ID: {till_id}")
        return jsonify({"success": False, "message": "Till not found"}), 404

    try:
        # Delete the quicklink
        result = Quicklink.objects(till=till, quicklink_number=quicklink_number, username=current_user.username).delete()
        if result == 0:
            current_app.logger.warning(f"No quicklink found to remove for till {till_id}, quicklink number {quicklink_number}")
            return jsonify({"success": True, "message": "No quicklink found to remove"}), 200
    except Exception as e:
        current_app.logger.error(f"Error removing quicklink: {str(e)}")
        return jsonify({"success": False, "message": f"Error removing quicklink: {str(e)}"}), 500

    current_app.logger.info(f"Quicklink removed successfully for till {till_id}, quicklink number {quicklink_number}")
    return jsonify({"success": True, "message": "Quicklink removed successfully"}), 200

@pos_bp.route('/pos/validate_quicklinks/<till_id>', methods=['GET'])
@login_required
def validate_quicklinks(till_id):
    """
    Validate all quicklinks for a till and remove any with invalid product IDs.
    This endpoint helps clean up corrupted quicklinks that cause loading issues.
    """
    current_app.logger.info(f"Validating quicklinks for till ID: {till_id}, user: {current_user.username}")

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
        current_app.logger.info(f"Found till: {till.location}")
    except InvalidId:
        current_app.logger.error(f"Invalid till ID format: {till_id}")
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        current_app.logger.error(f"Till not found for ID: {till_id}, user: {current_user.username}")
        return jsonify({"success": False, "message": "Till not found"}), 404
    except Exception as e:
        current_app.logger.error(f"Error finding till: {str(e)}")
        return jsonify({"success": False, "message": f"Error finding till: {str(e)}"}), 500

    try:
        quicklinks = Quicklink.objects(till=till, username=current_user.username).order_by('quicklink_number')
        current_app.logger.info(f"Found {len(quicklinks)} quicklinks to validate")

        valid_quicklinks = []
        invalid_quicklinks = []
        fixed_count = 0

        for ql in quicklinks:
            try:
                # Validate the product ID format
                if not ql.product_id:
                    current_app.logger.warning(f"Quicklink {ql.quicklink_number} has no product_id")
                    invalid_quicklinks.append({
                        "quicklink_number": ql.quicklink_number,
                        "product_id": str(ql.product_id) if ql.product_id else "None",
                        "product_name": ql.product_name,
                        "error": "No product ID"
                    })
                    ql.delete()
                    fixed_count += 1
                    continue

                # Try to convert to ObjectId
                try:
                    object_id = ObjectId(str(ql.product_id))
                except InvalidId:
                    current_app.logger.warning(f"Quicklink {ql.quicklink_number} has invalid product_id format: {ql.product_id}")
                    invalid_quicklinks.append({
                        "quicklink_number": ql.quicklink_number,
                        "product_id": str(ql.product_id),
                        "product_name": ql.product_name,
                        "error": "Invalid ObjectId format"
                    })
                    ql.delete()
                    fixed_count += 1
                    continue

                # Check if the product exists
                product = db.shProducts.find_one({"_id": object_id, "username": current_user.username})
                if not product:
                    current_app.logger.warning(f"Quicklink {ql.quicklink_number} references non-existent product: {ql.product_id}")
                    invalid_quicklinks.append({
                        "quicklink_number": ql.quicklink_number,
                        "product_id": str(ql.product_id),
                        "product_name": ql.product_name,
                        "error": "Product not found in database"
                    })
                    ql.delete()
                    fixed_count += 1
                    continue

                # Quicklink is valid
                valid_quicklinks.append({
                    "quicklink_number": ql.quicklink_number,
                    "product_id": str(ql.product_id),
                    "product_name": ql.product_name
                })
                current_app.logger.debug(f"Quicklink {ql.quicklink_number} is valid: {ql.product_name}")

            except Exception as e:
                current_app.logger.error(f"Error validating quicklink {ql.id}: {str(e)}")
                invalid_quicklinks.append({
                    "quicklink_number": ql.quicklink_number,
                    "product_id": str(ql.product_id) if ql.product_id else "None",
                    "product_name": ql.product_name,
                    "error": f"Validation error: {str(e)}"
                })
                try:
                    ql.delete()
                    fixed_count += 1
                except:
                    pass

        current_app.logger.info(f"Validation complete: {len(valid_quicklinks)} valid, {len(invalid_quicklinks)} invalid, {fixed_count} fixed")

        return jsonify({
            "success": True,
            "message": f"Quicklinks validated. {len(valid_quicklinks)} valid, {len(invalid_quicklinks)} invalid quicklinks removed.",
            "valid_quicklinks": valid_quicklinks,
            "invalid_quicklinks": invalid_quicklinks,
            "fixed_count": fixed_count
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error validating quicklinks: {str(e)}")
        return jsonify({"success": False, "message": f"Error validating quicklinks: {str(e)}"}), 500

@pos_bp.route('/pos/get_till_transactions/<till_id>', methods=['GET'])
@login_required
def get_till_transactions(till_id):
    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404

    transactions = till.transactions

    # Separate transactions by payment method for the close till modal
    cash_transactions = []
    card_transactions = []

    # Calculate totals
    cash_total = 0
    card_total = 0

    for t in transactions:
        transaction_data = {
            "items": t.items,
            "total": t.total,
            "amount": t.total,  # Add amount field specifically for the close till modal
            "payment_method": t.payment_method,
            "customer_id": t.customer_id,
            "time": t.timestamp.isoformat(),  # Renamed to 'time' to match what the frontend expects
            "employee_name": t.employee_name
        }

        if t.payment_method == 'cash':
            cash_transactions.append(transaction_data)
            cash_total += float(t.total)
        elif t.payment_method == 'card':
            card_transactions.append(transaction_data)
            card_total += float(t.total)

    # Calculate expected cash
    expected_cash = float(till.starting_float) + cash_total

    return jsonify({
        "success": True,
        "transactions": [{
            "items": t.items,
            "total": t.total,
            "payment_method": t.payment_method,
            "customer_id": t.customer_id,
            "timestamp": t.timestamp.isoformat(),
            "employee_name": t.employee_name
        } for t in transactions],
        "cash_transactions": cash_transactions,
        "card_transactions": card_transactions,
        "cash_total": cash_total,
        "card_total": card_total,
        "expected_cash": expected_cash,
        "starting_float": till.starting_float
    }), 200

@pos_bp.route('/pos/get_till_summary/<till_id>', methods=['GET'])
@login_required
def get_till_summary(till_id):
    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404

    total_sales = sum(t.total for t in till.transactions)
    cash_sales = sum(t.total for t in till.transactions if t.payment_method == 'cash')
    card_sales = sum(t.total for t in till.transactions if t.payment_method == 'card')

    return jsonify({
        "success": True,
        "summary": {
            "location": till.location,
            "starting_float": till.starting_float,
            "running_total": till.running_total,
            "total_sales": total_sales,
            "cash_sales": cash_sales,
            "card_sales": card_sales,
            "transaction_count": len(till.transactions)
        }
    }), 200

@pos_bp.route('/pos/get_till_closing/<till_id>', methods=['GET'])
@login_required
def get_till_closing(till_id):
    """
    Get the last closing details for a till.
    This is used when reopening a till to pre-fill the starting float with the last closing amount.
    """
    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404

    # Check if the till is closed
    is_closed = False
    if hasattr(till, 'status'):
        is_closed = till.status == 'closed'
    else:
        is_closed = getattr(till, 'is_closed', False)

    # Check if the till is closed
    if not is_closed:
        return jsonify({
            "success": False,
            "message": "Till is not closed"
        }), 404

    # Return the closing details
    closing_details = {
        "actual_cash": till.running_total,  # The running_total is set to actual_cash when closing
        "closing_notes": getattr(till, 'closing_notes', "")
    }

    return jsonify({
        "success": True,
        "closing_details": closing_details
    }), 200

@pos_bp.route('/pos/reopen_till/<till_id>', methods=['PUT'])
@login_required
def reopen_till(till_id):
    """
    Reopen a closed till with a new starting float.
    """
    data = request.json
    starting_float = data.get('starting_float')
    notes = data.get('notes', '')

    if not starting_float:
        return jsonify({"success": False, "message": "Starting float is required"}), 400

    try:
        starting_float = float(starting_float)
    except ValueError:
        return jsonify({"success": False, "message": "Starting float must be a number"}), 400

    try:
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid till ID format"}), 400
    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404

    # Check if the till is closed
    is_closed = False
    if hasattr(till, 'status'):
        is_closed = till.status == 'closed'
    else:
        is_closed = getattr(till, 'is_closed', False)

    if not is_closed:
        return jsonify({"success": False, "message": "Till is not closed"}), 400

    # Instead of using update() with potentially non-existent fields,
    # use direct attribute assignment and save()
    till.starting_float = starting_float
    till.running_total = starting_float

    # Set reopen notes if the field exists
    if hasattr(till, 'reopen_notes'):
        till.reopen_notes = notes

    # Set reopened_at if the field exists
    if hasattr(till, 'reopened_at'):
        till.reopened_at = datetime.utcnow()

    # Set the status or is_closed field
    if hasattr(till, 'status'):
        till.status = 'active'
    else:
        till.is_closed = False

    # Save the changes
    till.save()

    return jsonify({
        "success": True,
        "message": "Till reopened successfully",
        "till": {
            "id": str(till.id),
            "location": till.location,
            "starting_float": starting_float,
            "running_total": starting_float,
            "status": "active" if hasattr(till, 'status') else "reopened"
        }
    }), 200

@pos_bp.route('/pos/update_employee/<employee_id>', methods=['PUT'])
@login_required
def update_employee(employee_id):
    data = request.json
    name = data.get('name')
    pin = data.get('pin')

    if not name or not pin:
        return jsonify({"success": False, "message": "Name and PIN are required"}), 400

    try:
        employee = Employee.objects.get(id=ObjectId(employee_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid employee ID format"}), 400
    except Employee.DoesNotExist:
        return jsonify({"success": False, "message": "Employee not found"}), 404

    employee.name = name
    employee.pin = pin
    employee.save()

    return jsonify({
        "success": True,
        "message": "Employee updated successfully",
        "employee": {
            "id": str(employee.id),
            "name": employee.name,
            "pin": employee.pin
        }
    }), 200

@pos_bp.route('/pos/delete_employee/<employee_id>', methods=['DELETE'])
@login_required
def delete_employee(employee_id):
    try:
        employee = Employee.objects.get(id=ObjectId(employee_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid employee ID format"}), 400
    except Employee.DoesNotExist:
        return jsonify({"success": False, "message": "Employee not found"}), 404

    employee.delete()

    return jsonify({
        "success": True,
        "message": "Employee deleted successfully"
    }), 200

@pos_bp.route('/pos/apply_gift_card', methods=['POST'])
@login_required
def apply_gift_card():
    """
    Apply a gift card payment to a transaction.
    This endpoint is called from the frontend when the user wants to apply a gift card payment.
    """
    data = request.json
    customer_id = data.get('customer_id')
    amount = float(data.get('amount', 0.0))

    if not customer_id or amount <= 0:
        return jsonify({"success": False, "message": "Customer ID and amount greater than 0 are required"}), 400

    # Get total available gift card balance for this customer
    gift_card_balance = 0
    gift_card_id = None

    try:
        # Try to find the customer with various ID formats
        customer = None
        # First try with the ID directly
        customer = db.shCustomers.find_one({"id": customer_id, "username": current_user.username})

        if not customer:
            # Try with integer conversion (legacy format)
            try:
                customer = db.shCustomers.find_one({"id": int(customer_id), "username": current_user.username})
            except (ValueError, TypeError):
                pass

        if not customer:
            # Try with NumberLong format
            customer = db.shCustomers.find_one({
                "id": {"$numberLong": str(customer_id)},
                "username": current_user.username
            })

        if not customer:
            current_app.logger.error(f"Customer not found with ID: {customer_id}")
            return jsonify({"success": False, "message": "Customer not found in database"}), 400

        # Get customer's primary gift card if exists
        primary_gift_card_id = customer.get("gift_card_id")
        if primary_gift_card_id:
            gift_card_id = primary_gift_card_id

            # Check gift card balance in Shopify
            shopify_headers = {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": current_user.shopifyAccessToken
            }
            gift_card_balance_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/gift_cards/{primary_gift_card_id}.json"
            try:
                gift_card_balance_response = requests.get(gift_card_balance_url, headers=shopify_headers)
                gift_card_balance_response.raise_for_status()
                gift_card_data = gift_card_balance_response.json()["gift_card"]
                gift_card_balance = float(gift_card_data["balance"])
            except requests.RequestException as e:
                current_app.logger.error(f"Error fetching gift card balance: {str(e)}")

        # Check for additional gift cards in shGiftCards collection
        try:
            # Convert customer_id to string for comparison if needed
            if isinstance(customer_id, dict) and "$numberLong" in customer_id:
                customer_id_str = str(customer_id["$numberLong"])
            else:
                customer_id_str = str(customer_id)

            # Find all gift cards associated with this customer
            gift_cards = list(db.shGiftCards.find({
                "username": current_user.username,
                "customer_id": customer_id_str,
                "disabled": {"$ne": True}  # Exclude disabled gift cards
            }))

            # If no primary gift card was found but we found gift cards in shGiftCards, use the first one
            if not gift_card_id and gift_cards:
                gift_card_id = gift_cards[0].get("id")

            # Add up all gift card balances
            for card in gift_cards:
                if "balance" in card and card["balance"] is not None:
                    # Use local balance
                    gift_card_balance += float(card["balance"])
                elif "id" in card and card["id"] and card["id"] != primary_gift_card_id:
                    # Query Shopify for balance
                    card_id = card.get("id")
                    try:
                        card_balance_url = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2023-04/gift_cards/{card_id}.json"
                        card_balance_response = requests.get(card_balance_url, headers=shopify_headers)
                        card_balance_response.raise_for_status()
                        card_data = card_balance_response.json()["gift_card"]
                        gift_card_balance += float(card_data["balance"])
                    except requests.RequestException as e:
                        current_app.logger.error(f"Error fetching additional gift card balance: {str(e)}")
        except Exception as e:
            current_app.logger.error(f"Error querying shGiftCards: {str(e)}")

        # Verify the requested amount is available
        if amount > gift_card_balance:
            return jsonify({
                "success": False,
                "message": f"Requested amount ${amount} exceeds available gift card balance ${gift_card_balance}"
            }), 400

        # Return success with the gift card ID to use and the valid amount
        return jsonify({
            "success": True,
            "gift_card_id": gift_card_id,
            "amount": amount,
            "total_balance": gift_card_balance
        })

    except Exception as e:
        current_app.logger.error(f"Error in apply_gift_card: {str(e)}")
        return jsonify({"success": False, "message": f"Error processing gift card: {str(e)}"}), 500

@pos_bp.route('/pos/process_card_payment', methods=['POST'])
@login_required
def process_card_payment_route():
    """
    Process a card payment using the SPIn API v2.
    This endpoint is called from the frontend when the user wants to process a card payment.
    """
    data = request.json
    till_id = data.get('till_id')
    amount = float(data.get('amount', 0.0))
    reference_id = data.get('reference_id')  # Optional reference ID
    is_split_payment = data.get('is_split_payment', False)  # Flag to indicate if this is part of a split payment
    payment_index = data.get('payment_index')  # Index of the payment in the split payment list

    if not till_id or amount <= 0:
        return jsonify({"success": False, "message": "Till ID and amount greater than 0 are required"}), 400

    try:
        # Get the till settings
        till = PosSetting.objects.get(id=ObjectId(till_id), username=current_user.username)

        # Check if the till has the required card machine settings
        if not all([till.tpn, till.register_id, till.auth_key]):
            return jsonify({
                "success": False,
                "message": "This till is not configured for card payments. Please set up the TPN, Register ID, and Auth Key in the till settings."
            }), 400

        # Process the card payment
        result = process_card_payment(
            amount=amount,
            tpn=till.tpn,
            auth_key=till.auth_key,
            register_id=till.register_id,
            reference_id=reference_id
        )

        # Log the result
        current_app.logger.info(f"Card payment result: {result}")

        # Add split payment info to the result if applicable
        if is_split_payment and payment_index is not None:
            result['is_split_payment'] = True
            result['payment_index'] = payment_index

        # Return the result to the frontend
        return jsonify(result)

    except PosSetting.DoesNotExist:
        return jsonify({"success": False, "message": "Till not found"}), 404
    except Exception as e:
        current_app.logger.error(f"Error processing card payment: {str(e)}")
        return jsonify({"success": False, "message": f"Error processing card payment: {str(e)}"}), 500

@pos_bp.route('/pos/get_buylist_orders', methods=['GET'])
@login_required
def get_buylist_orders():
    """
    Get buylist orders for the logged-in user.
    Returns orders from the buylistOrders collection where the username matches the logged-in user.
    Orders are sorted by newest first.
    """
    try:
        # Get buylist orders for the current user
        orders = list(db.buylistOrders.find(
            {"username": current_user.username}
        ).sort("date", DESCENDING).limit(50))  # Limit to 50 most recent orders

        # Format the orders for the response
        formatted_orders = []
        for order in orders:
            # Convert ObjectId to string for JSON serialization
            order['_id'] = str(order['_id'])

            # Format dates if present
            if 'date' in order:
                order['date'] = order['date'].isoformat() if isinstance(order['date'], datetime) else order['date']
            elif 'created_at' in order:
                order['date'] = order['created_at'].isoformat() if isinstance(order['created_at'], datetime) else order['created_at']
            else:
                order['date'] = None

            # Ensure required fields exist
            if 'orderStatus' not in order:
                order['orderStatus'] = 'Unknown'

            # Count line items
            item_count = len(order.get('line_items', []))
            order['item_count'] = item_count

            formatted_orders.append(order)

        return jsonify({
            "success": True,
            "orders": formatted_orders
        }), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching buylist orders: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error fetching buylist orders: {str(e)}"
        }), 500

@pos_bp.route('/pos/get_buylist_order/<order_id>', methods=['GET'])
@login_required
def get_buylist_order(order_id):
    """
    Get a specific buylist order by ID.
    Returns the order from the buylistOrders collection where the ID matches the provided order_id.
    """
    try:
        # Convert order_id string to ObjectId
        from bson import ObjectId
        order_object_id = ObjectId(order_id)

        # Get the order from the database
        order = db.buylistOrders.find_one({"_id": order_object_id, "username": current_user.username})

        if not order:
            return jsonify({
                "success": False,
                "message": "Order not found"
            }), 404

        # Convert ObjectId to string for JSON serialization
        order['_id'] = str(order['_id'])

        # Format dates if present
        if 'date' in order:
            order['date'] = order['date'].isoformat() if isinstance(order['date'], datetime) else order['date']
        elif 'created_at' in order:
            order['date'] = order['created_at'].isoformat() if isinstance(order['created_at'], datetime) else order['created_at']

        # Ensure required fields exist
        if 'orderStatus' not in order:
            order['orderStatus'] = 'Unknown'

        # Count line items
        item_count = len(order.get('line_items', []))
        order['item_count'] = item_count

        return jsonify({
            "success": True,
            "order": order
        }), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching buylist order: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error fetching buylist order: {str(e)}"
        }), 500

@pos_bp.route('/pos/apply_store_credit', methods=['POST'])
@login_required
def apply_store_credit():
    """
    Apply store credit to a transaction.
    This endpoint is called from the frontend when the user wants to apply store credit.

    Note: This function only deducts store credit from the customer's account.
    It does NOT adjust inventory - inventory adjustment happens during the final transaction completion.
    """
    data = request.json
    customer_id = data.get('customer_id')
    amount = float(data.get('amount', 0.0))
    apply_immediately = data.get('apply_immediately', False)  # Flag to indicate if we should deduct immediately
    skip_inventory_adjustment = data.get('skip_inventory_adjustment', True)  # Skip inventory adjustment by default
    provided_store_credit_account_id = data.get('store_credit_account_id')  # Get store credit account ID if provided
    available_credit = data.get('available_credit')  # Get client-side available credit if provided

    current_app.logger.info(f"Apply store credit request: customer_id={customer_id}, amount={amount}, "
                           f"store_credit_account_id={provided_store_credit_account_id}, "
                           f"available_credit={available_credit}")

    if not customer_id or amount <= 0:
        return jsonify({"success": False, "message": "Customer ID and amount greater than 0 are required"}), 400

    # Log the incoming request data for debugging
    current_app.logger.info(f"Store credit request data: customer_id={customer_id}, amount={amount}, " +
                           f"store_credit_account_id={provided_store_credit_account_id}, " +
                           f"available_credit={available_credit}")

    # Get total available store credit balance for this customer
    store_credit_balance = 0
    store_credit_account_id = provided_store_credit_account_id  # Use provided ID if available

    # If client provided available credit, use it as a fallback
    client_provided_balance = None
    if available_credit is not None:
        try:
            client_provided_balance = float(available_credit)
            current_app.logger.info(f"Client provided store credit balance: {client_provided_balance}")
        except (ValueError, TypeError):
            current_app.logger.warning(f"Invalid client-provided store credit balance: {available_credit}")

    try:
        # Try to find the customer with various ID formats
        customer = None

        # Handle the case where customer_id is a dict with $numberLong
        if isinstance(customer_id, dict) and "$numberLong" in customer_id:
            try:
                # Extract the value from the $numberLong field
                long_id = str(customer_id["$numberLong"])
                current_app.logger.info(f"Extracted $numberLong value: {long_id}")
                customer_id = long_id  # Replace the dict with the extracted value
            except Exception as e:
                current_app.logger.warning(f"Error extracting $numberLong value: {str(e)}")

        # First try with the ID directly as a string
        customer = db.shCustomers.find_one({"id": str(customer_id), "username": current_user.username})
        current_app.logger.info(f"Searching for customer with string ID: {str(customer_id)}")

        if not customer:
            # Try with integer conversion (legacy format)
            try:
                int_id = int(customer_id)
                current_app.logger.info(f"Trying integer ID: {int_id}")
                customer = db.shCustomers.find_one({"id": int_id, "username": current_user.username})
            except (ValueError, TypeError):
                current_app.logger.info(f"Could not convert {customer_id} to integer")
                pass

        if not customer:
            # Try with case-insensitive regex pattern
            try:
                import re
                pattern = re.compile(f"^{re.escape(str(customer_id))}$", re.IGNORECASE)
                current_app.logger.info(f"Trying case-insensitive regex pattern: {pattern}")
                customer = db.shCustomers.find_one({"id": pattern, "username": current_user.username})
            except Exception as e:
                current_app.logger.warning(f"Error using regex pattern: {str(e)}")

        # If we still don't have a customer but have a store credit account ID, proceed anyway
        if not customer and provided_store_credit_account_id:
            current_app.logger.info(f"Customer not found but store credit account ID provided: {provided_store_credit_account_id}")
            # We'll use the provided store credit account ID directly
            store_credit_account_id = provided_store_credit_account_id
            # Skip the customer lookup error
        elif not customer:
            current_app.logger.error(f"Customer not found with ID: {customer_id}")
            return jsonify({"success": False, "message": "Customer not found in database"}), 400

        # Get the customer ID from the customer object if found
        if customer:
            # Extract the customer ID from the found customer document
            customer_id_from_db = customer.get('id')
            if customer_id_from_db:
                # Use the ID from the database
                formatted_customer_id = str(customer_id_from_db)
                current_app.logger.info(f"Using customer ID from database: {formatted_customer_id}")
            else:
                # Fallback to the provided ID
                formatted_customer_id = str(customer_id)
                current_app.logger.info(f"Using provided customer ID: {formatted_customer_id}")
        else:
            # If no customer found, use the provided ID
            formatted_customer_id = str(customer_id)
            current_app.logger.info(f"No customer found, using provided ID: {formatted_customer_id}")

        # Clean the formatted ID to ensure it's just the numeric part
        # Remove any non-numeric characters if it's not already in GraphQL format
        if not formatted_customer_id.startswith('gid://'):
            # Try to extract just the numeric part
            import re
            numeric_match = re.search(r'(\d+)', formatted_customer_id)
            if numeric_match:
                formatted_customer_id = numeric_match.group(1)
                current_app.logger.info(f"Extracted numeric customer ID: {formatted_customer_id}")

        # Ensure customer_id is properly formatted for Shopify GraphQL API
        if not formatted_customer_id.startswith('gid://'):
            # This is just the numeric ID, so we need to format it for GraphQL
            graphql_customer_id = f"gid://shopify/Customer/{formatted_customer_id}"
        else:
            # It's already in the correct format
            graphql_customer_id = formatted_customer_id

        current_app.logger.info(f"Checking store credit for customer ID: {formatted_customer_id}")
        current_app.logger.info(f"GraphQL formatted customer ID: {graphql_customer_id}")

        # If we don't have a store credit account ID but we have a customer ID,
        # create a store credit account ID from the customer ID
        if not store_credit_account_id and formatted_customer_id:
            store_credit_account_id = graphql_customer_id
            current_app.logger.info(f"Created store credit account ID from customer ID: {store_credit_account_id}")

        # If we already have a store credit account ID, we can skip querying for accounts
        if store_credit_account_id:
            current_app.logger.info(f"Using provided store credit account ID: {store_credit_account_id}")

            # We still need to get the balance for this account
            query = """
            query {
              node(id: "%s") {
                ... on StoreCreditAccount {
                  id
                  balance {
                    amount
                    currencyCode
                  }
                }
              }
            }
            """ % (store_credit_account_id)

            # Set up GraphQL request
            graphql_endpoint = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2025-04/graphql.json"
            headers = {
                'X-Shopify-Access-Token': current_user.shopifyAccessToken,
                'Content-Type': 'application/json'
            }

            # Make the request
            response = requests.post(
                graphql_endpoint,
                headers=headers,
                json={"query": query}
            )

            if response.status_code == 200:
                result = response.json()

                # Extract account data
                account = result.get('data', {}).get('node', {})
                if account:
                    balance = account.get('balance', {})
                    account_amount = balance.get('amount')
                    account_currency = balance.get('currencyCode')

                    if account_amount and float(account_amount) > 0:
                        store_credit_balance = float(account_amount)
                        # Store the currency code from the account
                        if account_currency:
                            session['store_credit_currency'] = account_currency

                    current_app.logger.info(f"Found store credit balance: {store_credit_balance}")
            else:
                current_app.logger.error(f"Error fetching store credit: {response.status_code} - {response.text}")
                return jsonify({"success": False, "message": "Error fetching store credit from Shopify"}), 500
        else:
            # Execute GraphQL query to get store credit balance
            query = """
            query {
              customer(id: "%s") {
                storeCreditAccounts(first: 10) {
                  edges {
                    node {
                      id
                      balance {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
            """ % (graphql_customer_id)

            # Set up GraphQL request
            graphql_endpoint = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2025-04/graphql.json"
            headers = {
                'X-Shopify-Access-Token': current_user.shopifyAccessToken,
                'Content-Type': 'application/json'
            }

            # Make the request
            response = requests.post(
                graphql_endpoint,
                headers=headers,
                json={"query": query}
            )

            if response.status_code == 200:
                result = response.json()

                # Extract customer data
                customer_data = result.get('data', {}).get('customer', {})
                if customer_data:
                    # Extract store credit accounts
                    store_credit_accounts = customer_data.get('storeCreditAccounts', {}).get('edges', [])

                    # Find the first non-empty account to use
                    for account_edge in store_credit_accounts:
                        account = account_edge.get('node', {})
                        balance = account.get('balance', {})
                        account_amount = balance.get('amount')
                        account_id = account.get('id')
                        account_currency = balance.get('currencyCode')

                        if account_amount and float(account_amount) > 0:
                            store_credit_balance += float(account_amount)
                            # Save the first valid account ID and currency we find
                            if not store_credit_account_id and account_id:
                                store_credit_account_id = account_id
                                # Store the currency code from the account
                                if account_currency:
                                    session['store_credit_currency'] = account_currency

                    current_app.logger.info(f"Found store credit balance: {store_credit_balance}")
            else:
                current_app.logger.error(f"Error fetching store credit: {response.status_code} - {response.text}")
                return jsonify({"success": False, "message": "Error fetching store credit from Shopify"}), 500

        # Verify the requested amount is available
        # If we couldn't get a balance from the API but have a client-provided balance, use that
        if store_credit_balance <= 0 and client_provided_balance is not None and client_provided_balance > 0:
            current_app.logger.info(f"Using client-provided store credit balance: {client_provided_balance}")
            store_credit_balance = client_provided_balance

        if amount > store_credit_balance:
            return jsonify({
                "success": False,
                "message": f"Requested amount ${amount} exceeds available store credit balance ${store_credit_balance}"
            }), 400

        # If apply_immediately is True, deduct the store credit now
        if apply_immediately:
            try:
                # Format customer ID for GraphQL query
                if isinstance(customer_id, dict) and "$numberLong" in customer_id:
                    formatted_customer_id = customer_id["$numberLong"]
                else:
                    formatted_customer_id = str(customer_id)

                # Get currency code from the store credit account
                currency_code = session.get('store_credit_currency')
                if not currency_code:
                    # Fallback to user's currency if we couldn't get it from the account
                    try:
                        # Try to get the user's currency
                        currency = Currency.objects(username=current_user.username).first()
                        if currency and currency.code:
                            currency_code = currency.code.upper()
                        else:
                            # Default to USD if no currency is found
                            currency_code = "USD"
                    except Exception as e:
                        current_app.logger.error(f"Error getting currency: {str(e)}")
                        currency_code = "USD"  # Default to USD

                current_app.logger.info("Not specifying currency code for store credit debit - using account's default currency")

                # Use the GraphQL mutation to debit the store credit with proper variables
                # Include account and balance information in the response
                mutation = """
                mutation storeCreditAccountDebit($id: ID!, $debitInput: StoreCreditAccountDebitInput!) {
                  storeCreditAccountDebit(
                    id: $id,
                    debitInput: $debitInput
                  ) {
                    storeCreditAccountTransaction {
                      id
                      amount {
                        amount
                        currencyCode
                      }
                      account {
                        id
                        balance {
                          amount
                          currencyCode
                        }
                      }
                    }
                    userErrors {
                      field
                      message
                    }
                  }
                }
                """

                # We need to get the actual store credit account ID and currency from the customer ID
                # If we have a customer ID (which starts with gid://shopify/Customer/), we need to query for the store credit account ID
                account_currency_code = None
                if store_credit_account_id.startswith('gid://shopify/Customer/'):
                    current_app.logger.info(f"Need to get store credit account ID for customer: {store_credit_account_id}")

                    # Query for the customer's store credit accounts
                    query = """
                    query {
                      customer(id: "%s") {
                        storeCreditAccounts(first: 10) {
                          edges {
                            node {
                              id
                              balance {
                                amount
                                currencyCode
                              }
                            }
                          }
                        }
                      }
                    }
                    """ % (store_credit_account_id)

                    # Set up GraphQL request
                    graphql_endpoint = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2025-04/graphql.json"
                    headers = {
                        'X-Shopify-Access-Token': current_user.shopifyAccessToken,
                        'Content-Type': 'application/json'
                    }

                    # Make the request
                    response = requests.post(
                        graphql_endpoint,
                        headers=headers,
                        json={"query": query}
                    )

                    if response.status_code == 200:
                        result = response.json()
                        current_app.logger.info(f"Store credit accounts query response: {result}")

                        # Extract store credit accounts
                        customer_data = result.get('data', {}).get('customer', {})
                        store_credit_accounts = customer_data.get('storeCreditAccounts', {}).get('edges', [])

                        # Find the first account with a positive balance
                        actual_store_credit_account_id = None
                        for account_edge in store_credit_accounts:
                            account = account_edge.get('node', {})
                            balance = account.get('balance', {})
                            account_amount = balance.get('amount')
                            account_id = account.get('id')
                            currency = balance.get('currencyCode')

                            if account_id and account_amount and float(account_amount) > 0:
                                actual_store_credit_account_id = account_id
                                account_currency_code = currency
                                current_app.logger.info(f"Found store credit account ID: {actual_store_credit_account_id} with balance: {account_amount} {currency}")
                                break

                        if actual_store_credit_account_id:
                            store_credit_account_id = actual_store_credit_account_id
                        else:
                            current_app.logger.error(f"No store credit account found for customer: {store_credit_account_id}")
                            return jsonify({
                                "success": False,
                                "message": "No store credit account found for this customer"
                            }), 400
                    else:
                        current_app.logger.error(f"Error fetching store credit accounts: {response.status_code} - {response.text}")
                        return jsonify({
                            "success": False,
                            "message": "Error fetching store credit accounts from Shopify"
                        }), 500
                elif not store_credit_account_id.startswith('gid://'):
                    # Format it as a store credit account ID if it's not already in the correct format
                    store_credit_account_id = f"gid://shopify/StoreCredit/{store_credit_account_id}"
                    current_app.logger.info(f"Reformatted store credit account ID: {store_credit_account_id}")

                # If we don't have the account's currency yet, we need to query for it
                if not account_currency_code and store_credit_account_id.startswith('gid://shopify/StoreCredit/'):
                    current_app.logger.info(f"Need to get currency for store credit account: {store_credit_account_id}")

                    # Query for the store credit account's currency
                    query = """
                    query {
                      storeCreditAccount(id: "%s") {
                        balance {
                          amount
                          currencyCode
                        }
                      }
                    }
                    """ % (store_credit_account_id)

                    # Set up GraphQL request
                    graphql_endpoint = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2025-04/graphql.json"
                    headers = {
                        'X-Shopify-Access-Token': current_user.shopifyAccessToken,
                        'Content-Type': 'application/json'
                    }

                    # Make the request
                    response = requests.post(
                        graphql_endpoint,
                        headers=headers,
                        json={"query": query}
                    )

                    if response.status_code == 200:
                        result = response.json()
                        current_app.logger.info(f"Store credit account query response: {result}")

                        # Extract currency code
                        account_data = result.get('data', {}).get('storeCreditAccount', {})
                        balance = account_data.get('balance', {})
                        account_currency_code = balance.get('currencyCode')

                        if account_currency_code:
                            current_app.logger.info(f"Found currency code for store credit account: {account_currency_code}")
                        else:
                            current_app.logger.warning("Could not determine currency code for store credit account, using USD as fallback")
                            account_currency_code = "USD"
                    else:
                        current_app.logger.error(f"Error fetching store credit account: {response.status_code} - {response.text}")
                        current_app.logger.warning("Could not determine currency code for store credit account, using USD as fallback")
                        account_currency_code = "USD"

                # If we still don't have a currency code, use the user's currency or USD as fallback
                if not account_currency_code:
                    account_currency_code = currency_code or "USD"
                    current_app.logger.info(f"Using fallback currency code: {account_currency_code}")

                current_app.logger.info(f"Using store credit account ID for debit: {store_credit_account_id} with currency: {account_currency_code}")

                # Set up variables for the GraphQL mutation
                # Use the account's currency that we determined
                variables = {
                    "id": store_credit_account_id,  # Use the actual store credit account ID, not the customer ID
                    "debitInput": {
                        "debitAmount": {
                            "amount": str(amount),
                            "currencyCode": account_currency_code
                        }
                    }
                }

                current_app.logger.info(f"GraphQL variables: {variables}")

                current_app.logger.info(f"Executing store credit debit mutation with: customer_id={formatted_customer_id}, amount={amount}, currency={account_currency_code}")

                # Set up GraphQL request
                graphql_endpoint = f"https://{current_user.shopifyStoreName}.myshopify.com/admin/api/2025-04/graphql.json"
                graphql_headers = {
                    'X-Shopify-Access-Token': current_user.shopifyAccessToken,
                    'Content-Type': 'application/json'
                }

                # Apply the store credit payment with variables
                debit_response = requests.post(
                    graphql_endpoint,
                    headers=graphql_headers,
                    json={
                        "query": mutation,
                        "variables": variables
                    }
                )

                if debit_response.status_code == 200:
                    debit_result = debit_response.json()

                    # Log the full response for debugging
                    current_app.logger.info(f"Store credit debit response: {debit_result}")

                    # Check for GraphQL errors
                    if 'errors' in debit_result:
                        error_messages = [error.get('message', 'Unknown error') for error in debit_result.get('errors', [])]
                        current_app.logger.error(f"GraphQL error applying store credit: {', '.join(error_messages)}")
                        return jsonify({
                            "success": False,
                            "message": f"Error applying store credit: {', '.join(error_messages)}"
                        }), 400

                    # Check for user errors
                    user_errors = debit_result.get('data', {}).get('storeCreditAccountDebit', {}).get('userErrors', [])
                    if user_errors:
                        error_messages = [error.get('message') for error in user_errors]
                        current_app.logger.error(f"Error applying store credit: {', '.join(error_messages)}")
                        return jsonify({
                            "success": False,
                            "message": f"Error applying store credit: {', '.join(error_messages)}"
                        }), 400
                    else:
                        # Transaction was successful
                        transaction = debit_result.get('data', {}).get('storeCreditAccountDebit', {}).get('storeCreditAccountTransaction', {})
                        if transaction:
                            current_app.logger.info(f"Successfully applied store credit payment: {amount}")

                            # Get the updated balance from the response
                            account = debit_result.get('data', {}).get('storeCreditAccountDebit', {}).get('storeCreditAccountTransaction', {}).get('account', {})
                            updated_balance = account.get('balance', {}).get('amount')

                            # If we couldn't get the updated balance from the response, calculate it
                            if updated_balance is None:
                                updated_balance = store_credit_balance - amount
                            else:
                                updated_balance = float(updated_balance)

                            # Return success with updated balance
                            return jsonify({
                                "success": True,
                                "store_credit_account_id": store_credit_account_id,
                                "amount": amount,
                                "total_balance": updated_balance,
                                "transaction_id": transaction.get('id')
                            })
                        else:
                            current_app.logger.error(f"No transaction data returned from Shopify")
                            return jsonify({
                                "success": False,
                                "message": "No transaction data returned from Shopify"
                            }), 500
                else:
                    current_app.logger.error(f"Error applying store credit: {debit_response.status_code} - {debit_response.text}")
                    return jsonify({
                        "success": False,
                        "message": f"Error applying store credit: {debit_response.status_code} - {debit_response.text}"
                    }), 500
            except Exception as e:
                current_app.logger.error(f"Error processing store credit payment: {str(e)}")
                return jsonify({
                    "success": False,
                    "message": f"Error processing store credit payment: {str(e)}"
                }), 500

        # If not applying immediately, just return the account info
        return jsonify({
            "success": True,
            "store_credit_account_id": store_credit_account_id,
            "amount": amount,
            "total_balance": store_credit_balance
        })

    except Exception as e:
        current_app.logger.error(f"Error in apply_store_credit: {str(e)}")
        return jsonify({"success": False, "message": f"Error processing store credit: {str(e)}"}), 500

@pos_bp.route('/pos/edit_held_sale/<held_sale_id>', methods=['PUT'])
@login_required
def edit_held_sale(held_sale_id):
    """
    Edit a held sale's details.
    This endpoint allows updating customer information and items in a held sale.
    """
    data = request.json
    customer_id = data.get('customer_id')
    customer_name = data.get('customer_name')
    items = data.get('items')

    try:
        held_sale = HeldSale.objects.get(id=ObjectId(held_sale_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid held sale ID format"}), 400
    except HeldSale.DoesNotExist:
        return jsonify({"success": False, "message": "Held sale not found"}), 404

    # Track which items need inventory adjustment
    original_items = held_sale.items
    original_non_custom_items = [item for item in original_items if not (
        item.get("is_custom") or
        (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
    )]

    new_non_custom_items = []
    if items:
        new_non_custom_items = [item for item in items if not (
            item.get("is_custom") or
            (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
        )]

    # Handle inventory adjustments
    try:
        # First restore inventory for removed items
        items_to_restore = []
        for orig_item in original_non_custom_items:
            # Check if this item is still in the new items list (matching by variant_id)
            orig_variant_id = str(orig_item.get('variant_id', ''))
            found = False
            for new_item in new_non_custom_items:
                new_variant_id = str(new_item.get('variant_id', ''))
                if orig_variant_id == new_variant_id:
                    found = True
                    # If quantity decreased, we need to restore some inventory
                    quantity_diff = int(orig_item.get('quantity', 0)) - int(new_item.get('quantity', 0))
                    if quantity_diff > 0:
                        items_to_restore.append({
                            "variant_id": orig_variant_id,
                            "quantity": quantity_diff
                        })
                    break

            # If item was completely removed, restore all inventory
            if not found:
                items_to_restore.append({
                    "variant_id": orig_variant_id,
                    "quantity": int(orig_item.get('quantity', 0))
                })

        # Restore inventory for removed or reduced quantity items
        if items_to_restore:
            adjust_shopify_inventory_for_reserved_items(
                items_to_restore, 1, current_user.username
            )
            # Update reservation records
            for item in items_to_restore:
                # Either delete reservation if item was removed, or update quantity if reduced
                variant_id = item['variant_id']
                found_in_new = False
                for new_item in new_non_custom_items:
                    if str(new_item.get('variant_id', '')) == str(variant_id):
                        found_in_new = True
                        break

                if not found_in_new:
                    # Delete reservation records for removed items
                    reserved_items_collection.delete_many({
                        "reservation_type": "hold",
                        "reservation_id": held_sale_id,
                        "variant_id": variant_id,
                        "username": current_user.username
                    })
                else:
                    # Update quantity for existing items that were reduced
                    for new_item in new_non_custom_items:
                        if str(new_item.get('variant_id', '')) == str(variant_id):
                            new_quantity = int(new_item.get('quantity', 0))
                            reserved_items_collection.update_many(
                                {
                                    "reservation_type": "hold",
                                    "reservation_id": held_sale_id,
                                    "variant_id": variant_id,
                                    "username": current_user.username
                                },
                                {"$set": {"quantity": new_quantity}}
                            )
                            break

        # Then reserve inventory for new or increased quantity items
        items_to_reserve = []
        for new_item in new_non_custom_items:
            new_variant_id = str(new_item.get('variant_id', ''))
            # Check if this item was in the original items list
            found = False
            for orig_item in original_non_custom_items:
                orig_variant_id = str(orig_item.get('variant_id', ''))
                if new_variant_id == orig_variant_id:
                    found = True
                    # If quantity increased, we need to reserve more inventory
                    quantity_diff = int(new_item.get('quantity', 0)) - int(orig_item.get('quantity', 0))
                    if quantity_diff > 0:
                        items_to_reserve.append({
                            "variant_id": new_variant_id,
                            "quantity": quantity_diff
                        })
                    break

            # If item is completely new, reserve all inventory
            if not found:
                items_to_reserve.append({
                    "variant_id": new_variant_id,
                    "quantity": int(new_item.get('quantity', 0))
                })

        # Reserve inventory for new or increased quantity items
        if items_to_reserve:
            adjust_shopify_inventory_for_reserved_items(
                items_to_reserve, -1, current_user.username
            )
            # Create new reservation records
            for item in items_to_reserve:
                variant_id = item['variant_id']
                # Check if this variant already has a reservation record
                existing_reservation = reserved_items_collection.find_one({
                    "reservation_type": "hold",
                    "reservation_id": held_sale_id,
                    "variant_id": variant_id,
                    "username": current_user.username
                })

                if existing_reservation:
                    # Update quantity for existing reservation
                    new_quantity = int(existing_reservation.get('quantity', 0)) + int(item['quantity'])
                    reserved_items_collection.update_one(
                        {"_id": existing_reservation['_id']},
                        {"$set": {"quantity": new_quantity}}
                    )
                else:
                    # Create new reservation record
                    reserved_items_collection.insert_one({
                        "variant_id": variant_id,
                        "quantity": item['quantity'],
                        "reservation_type": "hold",
                        "reservation_id": held_sale_id,
                        "username": current_user.username,
                        "timestamp": datetime.utcnow()
                    })
    except Exception as e:
        current_app.logger.error(f"Error adjusting inventory for held sale edit: {str(e)}")
        return jsonify({"success": False, "message": f"Error adjusting inventory: {str(e)}"}), 500

    # Update the held sale
    updates = {}
    if customer_id is not None:
        updates['customer_id'] = customer_id
    if customer_name is not None:
        updates['customer_name'] = customer_name
    if items is not None:
        updates['items'] = items

    held_sale.update(**updates)

    return jsonify({
        "success": True,
        "message": "Held sale updated successfully",
        "held_sale": {
            "id": str(held_sale.id),
            "customer_id": held_sale.customer_id,
            "customer_name": held_sale.customer_name,
            "items": held_sale.items,
            "timestamp": held_sale.timestamp.isoformat(),
            "employee_name": held_sale.employee_name
        }
    }), 200

@pos_bp.route('/pos/edit_layaway/<layaway_id>', methods=['PUT'])
@login_required
def edit_layaway(layaway_id):
    """
    Edit a layaway's details.
    This endpoint allows updating customer information, items, and due date of a layaway.
    """
    data = request.json
    customer_id = data.get('customer_id')
    items = data.get('items')
    due_date = data.get('due_date')

    try:
        layaway = Layaway.objects.get(id=ObjectId(layaway_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid layaway ID format"}), 400
    except Layaway.DoesNotExist:
        return jsonify({"success": False, "message": "Layaway not found"}), 404

    if layaway.status != 'active':
        return jsonify({"success": False, "message": "Only active layaways can be edited"}), 400

    # Track which items need inventory adjustment
    original_items = layaway.items
    original_non_custom_items = [item for item in original_items if not (
        item.get("is_custom") or
        (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
    )]

    new_non_custom_items = []
    if items:
        new_non_custom_items = [item for item in items if not (
            item.get("is_custom") or
            (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))
        )]

    # Handle inventory adjustments
    try:
        # First restore inventory for removed items
        items_to_restore = []
        for orig_item in original_non_custom_items:
            # Check if this item is still in the new items list (matching by variant_id)
            orig_variant_id = str(orig_item.get('variant_id', ''))
            found = False
            for new_item in new_non_custom_items:
                new_variant_id = str(new_item.get('variant_id', ''))
                if orig_variant_id == new_variant_id:
                    found = True
                    # If quantity decreased, we need to restore some inventory
                    quantity_diff = int(orig_item.get('quantity', 0)) - int(new_item.get('quantity', 0))
                    if quantity_diff > 0:
                        items_to_restore.append({
                            "variant_id": orig_variant_id,
                            "quantity": quantity_diff
                        })
                    break

            # If item was completely removed, restore all inventory
            if not found:
                items_to_restore.append({
                    "variant_id": orig_variant_id,
                    "quantity": int(orig_item.get('quantity', 0))
                })

        # Restore inventory for removed or reduced quantity items
        if items_to_restore:
            adjust_shopify_inventory_for_reserved_items(
                items_to_restore, 1, current_user.username
            )
            # Update reservation records
            for item in items_to_restore:
                # Either delete reservation if item was removed, or update quantity if reduced
                variant_id = item['variant_id']
                found_in_new = False
                for new_item in new_non_custom_items:
                    if str(new_item.get('variant_id', '')) == str(variant_id):
                        found_in_new = True
                        break

                if not found_in_new:
                    # Delete reservation records for removed items
                    reserved_items_collection.delete_many({
                        "reservation_type": "layaway",
                        "reservation_id": layaway_id,
                        "variant_id": variant_id,
                        "username": current_user.username
                    })
                else:
                    # Update quantity for existing items that were reduced
                    for new_item in new_non_custom_items:
                        if str(new_item.get('variant_id', '')) == str(variant_id):
                            new_quantity = int(new_item.get('quantity', 0))
                            reserved_items_collection.update_many(
                                {
                                    "reservation_type": "layaway",
                                    "reservation_id": layaway_id,
                                    "variant_id": variant_id,
                                    "username": current_user.username
                                },
                                {"$set": {"quantity": new_quantity}}
                            )
                            break

        # Then reserve inventory for new or increased quantity items
        items_to_reserve = []
        for new_item in new_non_custom_items:
            new_variant_id = str(new_item.get('variant_id', ''))
            # Check if this item was in the original items list
            found = False
            for orig_item in original_non_custom_items:
                orig_variant_id = str(orig_item.get('variant_id', ''))
                if new_variant_id == orig_variant_id:
                    found = True
                    # If quantity increased, we need to reserve more inventory
                    quantity_diff = int(new_item.get('quantity', 0)) - int(orig_item.get('quantity', 0))
                    if quantity_diff > 0:
                        items_to_reserve.append({
                            "variant_id": new_variant_id,
                            "quantity": quantity_diff
                        })
                    break

            # If item is completely new, reserve all inventory
            if not found:
                items_to_reserve.append({
                    "variant_id": new_variant_id,
                    "quantity": int(new_item.get('quantity', 0))
                })

        # Reserve inventory for new or increased quantity items
        if items_to_reserve:
            adjust_shopify_inventory_for_reserved_items(
                items_to_reserve, -1, current_user.username
            )
            # Create new reservation records
            for item in items_to_reserve:
                variant_id = item['variant_id']
                # Check if this variant already has a reservation record
                existing_reservation = reserved_items_collection.find_one({
                    "reservation_type": "layaway",
                    "reservation_id": layaway_id,
                    "variant_id": variant_id,
                    "username": current_user.username
                })

                if existing_reservation:
                    # Update quantity for existing reservation
                    new_quantity = int(existing_reservation.get('quantity', 0)) + int(item['quantity'])
                    reserved_items_collection.update_one(
                        {"_id": existing_reservation['_id']},
                        {"$set": {"quantity": new_quantity}}
                    )
                else:
                    # Create new reservation record
                    reserved_items_collection.insert_one({
                        "variant_id": variant_id,
                        "quantity": item['quantity'],
                        "reservation_type": "layaway",
                        "reservation_id": layaway_id,
                        "username": current_user.username,
                        "timestamp": datetime.utcnow()
                    })
    except Exception as e:
        current_app.logger.error(f"Error adjusting inventory for layaway edit: {str(e)}")
        return jsonify({"success": False, "message": f"Error adjusting inventory: {str(e)}"}), 500

    # Update the layaway
    updates = {}
    if customer_id is not None:
        updates['customer_id'] = customer_id
    if items is not None:
        updates['items'] = items
        # Recalculate total if items changed
        total = sum(float(item.get('price', 0)) * int(item.get('quantity', 1)) for item in items)
        updates['total'] = total
        # Recalculate remaining balance (total - deposit)
        updates['remaining_balance'] = total - layaway.deposit
    if due_date is not None:
        # Parse due_date string to datetime
        try:
            parsed_due_date = datetime.fromisoformat(due_date.replace('Z', '+00:00'))
            updates['due_date'] = parsed_due_date
        except (ValueError, TypeError) as e:
            current_app.logger.error(f"Invalid due date format: {str(e)}")
            return jsonify({"success": False, "message": "Invalid due date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)."}), 400

    layaway.update(**updates)

    # Get customer name for response
    customer_name = None
    if layaway.customer_id:
        try:
            customer = db.shCustomers.find_one({"id": layaway.customer_id, "username": current_user.username})
            if customer:
                customer_name = f"{customer.get('first_name', '')} {customer.get('last_name', '')}".strip()
        except Exception as e:
            current_app.logger.error(f"Error fetching customer for layaway response: {str(e)}")

    return jsonify({
        "success": True,
        "message": "Layaway updated successfully",
        "layaway": {
            "id": str(layaway.id),
            "customer_id": layaway.customer_id,
            "customer_name": customer_name,
            "items": layaway.items,
            "total": layaway.total,
            "deposit": layaway.deposit,
            "remaining_balance": layaway.remaining_balance,
            "due_date": layaway.due_date.isoformat(),
            "status": layaway.status
        }
    }), 200

@pos_bp.route('/pos/add_layaway_payment/<layaway_id>', methods=['POST'])
@login_required
def add_layaway_payment(layaway_id):
    """
    Add a payment to an existing layaway.
    This reduces the remaining balance without completing the layaway.
    """
    data = request.json
    payment_method = data.get('payment_method')
    amount_paid = float(data.get('amount_paid', 0))
    employee_name = data.get('employee_name')

    if not payment_method or amount_paid <= 0 or not employee_name:
        return jsonify({"success": False, "message": "Payment method, amount paid (> 0), and employee name are required"}), 400

    try:
        layaway = Layaway.objects.get(id=ObjectId(layaway_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid layaway ID format"}), 400
    except Layaway.DoesNotExist:
        return jsonify({"success": False, "message": "Layaway not found"}), 404

    if layaway.status != 'active':
        return jsonify({"success": False, "message": "Only active layaways can receive payments"}), 400

    if amount_paid > layaway.remaining_balance:
        return jsonify({"success": False, "message": "Payment amount exceeds remaining balance"}), 400

    # Create a payment record
    payment = {
        "payment_method": payment_method,
        "amount": amount_paid,
        "date": datetime.utcnow(),
        "employee_name": employee_name
    }

    # Add payment to the layaway's payment history
    # First check if 'payments' field exists, if not create it
    if not hasattr(layaway, 'payments'):
        layaway.payments = []

    # Update remaining balance
    new_remaining = layaway.remaining_balance - amount_paid

    # Check if this payment completes the layaway
    if new_remaining <= 0:
        # Complete the layaway if balance is zero
        return complete_layaway(layaway_id)

    # Otherwise just add the partial payment
    layaway.update(
        add_to_set__payments=payment,
        dec__remaining_balance=amount_paid
    )

    # If this is a cash payment, update the till's running total
    if payment_method == 'cash':
        layaway.till.update(inc__running_total=amount_paid)

    return jsonify({
        "success": True,
        "message": "Payment added successfully",
        "layaway": {
            "id": str(layaway.id),
            "customer_id": layaway.customer_id,
            "total": layaway.total,
            "deposit": layaway.deposit,
            "remaining_balance": new_remaining,
            "due_date": layaway.due_date.isoformat(),
            "status": layaway.status,
            "payments": layaway.payments if hasattr(layaway, 'payments') else []
        }
    }), 200

@pos_bp.route('/pos/get_layaway_items/<layaway_id>', methods=['GET'])
@login_required
def get_layaway_items(layaway_id):
    """
    Get items for a specific layaway.
    Returns detailed information about the items in a layaway, including product details.
    """
    try:
        layaway = Layaway.objects.get(id=ObjectId(layaway_id))
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid layaway ID format"}), 400
    except Layaway.DoesNotExist:
        return jsonify({"success": False, "message": "Layaway not found"}), 404

    # Get the items from the layaway
    items = layaway.items if layaway.items else []

    # Get reservation status from reserved_items_collection
    reserved_items = list(reserved_items_collection.find({
        "reservation_type": "layaway",
        "reservation_id": layaway_id,
        "username": current_user.username
    }))

    # Enhance items with product details
    enhanced_items = []
    for item in items:
        # Check if it's a custom item
        is_custom = item.get("is_custom") or (isinstance(item.get("variant_id"), str) and item["variant_id"].startswith("custom_"))

        enhanced_item = {
            "variant_id": item.get("variant_id"),
            "title": item.get("title", "Unknown Item"),
            "price": item.get("price", "0.00"),
            "quantity": item.get("quantity", 1),
            "is_custom": is_custom,
            "product_title": item.get("title", "Unknown Item"),  # Default to item title
            "variant_title": "",
            "image_url": ""
        }

        # If not a custom item, try to get product details
        if not is_custom:
            try:
                # Normalize variant_id for query
                variant_id = item.get("variant_id")
                normalized_variant_id = str(variant_id)

                # Try different query formats to find the variant
                query_formats = [
                    {"variants.id": normalized_variant_id},
                    {"variants.id": int(normalized_variant_id)} if normalized_variant_id.isdigit() else None,
                    {"variants.id": {"$numberLong": normalized_variant_id}}
                ]

                for query in query_formats:
                    if query:
                        product = db.shProducts.find_one(
                            {**query, "username": current_user.username},
                            {"title": 1, "variants.$": 1, "image": 1}
                        )
                        if product:
                            # Get product title
                            enhanced_item["product_title"] = product.get("title", "Unknown Product")

                            # Get variant title
                            if product.get("variants") and len(product["variants"]) > 0:
                                enhanced_item["variant_title"] = product["variants"][0].get("title", "")

                            # Get image URL
                            image = product.get("image", "")
                            if isinstance(image, dict) and "src" in image:
                                enhanced_item["image_url"] = image["src"]
                            elif isinstance(image, str):
                                enhanced_item["image_url"] = image

                            break
            except Exception as e:
                current_app.logger.error(f"Error getting product details for variant {item.get('variant_id')}: {str(e)}")

        enhanced_items.append(enhanced_item)

    return jsonify({
        "success": True,
        "layaway_id": layaway_id,
        "items": enhanced_items,
        "reserved_items_count": len(reserved_items)
    }), 200

@pos_bp.route('/pos/get_layaway_payments/<layaway_id>', methods=['GET'])
@login_required
def get_layaway_payments(layaway_id):
    """
    Get all payments made for a specific layaway.
    """
    try:
        layaway = Layaway.objects.get(id=ObjectId(layaway_id), username=current_user.username)
    except InvalidId:
        return jsonify({"success": False, "message": "Invalid layaway ID format"}), 400
    except Layaway.DoesNotExist:
        return jsonify({"success": False, "message": "Layaway not found"}), 404

    # Format payments for response
    payments = []

    # Include the initial deposit as the first payment
    payments.append({
        "payment_method": "initial_deposit",
        "amount": layaway.deposit,
        "date": layaway.id.generation_time.isoformat() if hasattr(layaway.id, 'generation_time') else None,
        "employee_name": "Unknown"  # We don't store who created the layaway
    })

    # Add any additional payments
    if hasattr(layaway, 'payments') and layaway.payments:
        for payment in layaway.payments:
            # Format payment date
            if isinstance(payment.get('date'), datetime):
                payment_date = payment['date'].isoformat()
            else:
                payment_date = payment.get('date')

            payments.append({
                "payment_method": payment.get('payment_method'),
                "amount": payment.get('amount'),
                "date": payment_date,
                "employee_name": payment.get('employee_name')
            })

    # Get customer name for context
    customer_name = None
    if layaway.customer_id:
        try:
            customer = db.shCustomers.find_one({"id": layaway.customer_id, "username": current_user.username})
            if customer:
                customer_name = f"{customer.get('first_name', '')} {customer.get('last_name', '')}".strip()
        except Exception as e:
            current_app.logger.error(f"Error fetching customer for layaway payments: {str(e)}")

    return jsonify({
        "success": True,
        "layaway": {
            "id": str(layaway.id),
            "customer_id": layaway.customer_id,
            "customer_name": customer_name,
            "total": layaway.total,
            "deposit": layaway.deposit,
            "remaining_balance": layaway.remaining_balance,
            "status": layaway.status,
            "due_date": layaway.due_date.isoformat()
        },
        "payments": payments
    }), 200

@pos_bp.route('/pos/get_till_closings', methods=['GET'])
@login_required
def get_till_closings():
    """
    Get till closing history for the current user.
    Optionally filter by till_id.
    """
    till_id = request.args.get('till_id')
    limit = int(request.args.get('limit', 50))

    # Build the query
    query = {"username": current_user.username}
    if till_id:
        query["till_id"] = till_id

    try:
        # Import the TillClosing model
        from models.pos_model import TillClosing

        # Get the till closings
        closings = TillClosing.objects(**query).order_by('-closing_time').limit(limit)

        # Format the response
        formatted_closings = []
        for closing in closings:
            formatted_closing = {
                "id": str(closing.id),
                "till_id": closing.till_id,
                "location": closing.location,
                "employee_name": closing.employee_name,
                "business_date": closing.business_date.isoformat() if closing.business_date else None,
                "closing_time": closing.closing_time.isoformat() if closing.closing_time else None,
                "starting_float": closing.starting_float,
                "ending_float": closing.ending_float,
                "expected_cash": closing.expected_cash,
                "cash_discrepancy": closing.cash_discrepancy,
                "total_sales": closing.total_sales,
                "cash_sales": closing.cash_sales,
                "card_sales": closing.card_sales,
                "gift_card_sales": closing.gift_card_sales,
                "store_credit_sales": closing.store_credit_sales,
                "transaction_count": closing.transaction_count,
                "total_tax_collected": closing.total_tax_collected,
                "closing_notes": closing.closing_notes,
                "z_report_number": closing.z_report_number
            }
            formatted_closings.append(formatted_closing)

        return jsonify({
            "success": True,
            "closings": formatted_closings
        }), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching till closings: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error fetching till closings: {str(e)}"
        }), 500

@pos_bp.route('/pos/get_till_closing_report/<closing_id>', methods=['GET'])
@login_required
def get_till_closing_report(closing_id):
    """
    Get the HTML report for a specific till closing.
    """
    try:
        # Import the TillClosingReport model
        from models.pos_model import TillClosingReport, TillClosing

        # First check if the closing exists and belongs to the current user
        closing = TillClosing.objects(id=ObjectId(closing_id), username=current_user.username).first()
        if not closing:
            return jsonify({
                "success": False,
                "message": "Till closing not found or access denied"
            }), 404

        # Get the report
        report = TillClosingReport.objects(till_closing=closing).first()
        if not report:
            return jsonify({
                "success": False,
                "message": "Report not found for this closing"
            }), 404

        return jsonify({
            "success": True,
            "report": {
                "id": str(report.id),
                "html_report": report.html_report,
                "generated_at": report.generated_at.isoformat() if report.generated_at else None
            },
            "closing": {
                "id": str(closing.id),
                "location": closing.location,
                "z_report_number": closing.z_report_number,
                "closing_time": closing.closing_time.isoformat() if closing.closing_time else None
            }
        }), 200
    except InvalidId:
        return jsonify({
            "success": False,
            "message": "Invalid closing ID format"
        }), 400
    except Exception as e:
        current_app.logger.error(f"Error fetching till closing report: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error fetching till closing report: {str(e)}"
        }), 500

@pos_bp.route('/pos/cleanup_expired_holds', methods=['POST'])
@login_required
def cleanup_expired_holds():
    """
    Check for and remove expired held sales, restoring inventory.
    This endpoint can be called manually or scheduled to run periodically.
    """
    try:
        # Get the expiration threshold from request or use default (24 hours)
        hours_threshold = request.json.get('hours_threshold', 24)
        expiration_time = datetime.utcnow() - timedelta(hours=hours_threshold)

        # Find expired held sales
        expired_sales = HeldSale.objects(
            username=current_user.username,
            timestamp__lt=expiration_time
        )

        if not expired_sales:
            return jsonify({
                "success": True,
                "message": "No expired held sales found",
                "count": 0
            }), 200

        # Process each expired sale
        processed_count = 0
        failed_count = 0
        restored_items_count = 0

        for sale in expired_sales:
            sale_id = str(sale.id)
            current_app.logger.info(f"Processing expired held sale {sale_id}, created {sale.timestamp.isoformat()}")

            # Get reserved items for this hold
            reserved_items = list(reserved_items_collection.find({
                "reservation_type": "hold",
                "reservation_id": sale_id,
                "username": current_user.username
            }))

            # If we have reserved items, restore inventory
            if reserved_items:
                items_to_restore = []
                for item in reserved_items:
                    items_to_restore.append({
                        "variant_id": item["variant_id"],
                        "quantity": item["quantity"]
                    })

                # Positive adjustment to restore inventory
                if items_to_restore:
                    try:
                        inventory_restored = adjust_shopify_inventory_for_reserved_items(
                            items_to_restore, 1, current_user.username
                        )

                        if inventory_restored:
                            # Remove reservation records
                            reserved_items_collection.delete_many({
                                "reservation_type": "hold",
                                "reservation_id": sale_id,
                                "username": current_user.username
                            })
                            current_app.logger.info(f"Restored {len(items_to_restore)} items from expired hold sale {sale_id}")
                            restored_items_count += len(items_to_restore)

                            # Delete the held sale
                            sale.delete()
                            processed_count += 1
                        else:
                            current_app.logger.warning(f"Failed to restore inventory for expired hold sale {sale_id}")
                            failed_count += 1
                    except Exception as e:
                        current_app.logger.error(f"Error processing expired hold {sale_id}: {str(e)}")
                        failed_count += 1
            else:
                # No reserved items, just delete the held sale
                sale.delete()
                processed_count += 1

        return jsonify({
            "success": True,
            "message": f"Processed {processed_count} expired held sales, restored {restored_items_count} items",
            "count": processed_count,
            "failed": failed_count,
            "restored_items": restored_items_count
        }), 200
    except Exception as e:
        current_app.logger.error(f"Error cleaning up expired holds: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error cleaning up expired holds: {str(e)}"
        }), 500

@pos_bp.route('/pos/get_terminal_orders', methods=['GET'])
@login_required
def get_terminal_orders():
    """
    Fetch terminal orders for the logged-in user
    """
    try:
        from models.terminal_order_model import TerminalOrder

        # Get terminal orders for the current user, ordered by creation date (newest first)
        orders = TerminalOrder.objects(
            username=current_user.username,
            status='completed'
        ).order_by('-created_at')

        # Convert to list of dictionaries
        orders_list = []
        for order in orders:
            orders_list.append({
                '_id': str(order.id),
                'customerName': order.customerName,
                'total': order.total,
                'created_at': order.created_at.isoformat(),
                'status': order.status,
                'order_type': order.order_type
            })

        return jsonify({
            "success": True,
            "orders": orders_list
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching terminal orders: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error fetching terminal orders: {str(e)}"
        }), 500

@pos_bp.route('/pos/load_terminal_order/<order_id>', methods=['GET'])
@login_required
def load_terminal_order(order_id):
    """
    Load a specific terminal order and return its details
    """
    try:
        from models.terminal_order_model import TerminalOrder

        # Get the specific order
        order = TerminalOrder.objects(
            id=ObjectId(order_id),
            username=current_user.username
        ).first()

        if not order:
            return jsonify({
                "success": False,
                "message": "Order not found"
            }), 404

        # Convert order to dictionary
        order_data = {
            '_id': str(order.id),
            'customerName': order.customerName,
            'items': order.items,
            'total': order.total,
            'created_at': order.created_at.isoformat(),
            'status': order.status,
            'order_type': order.order_type
        }

        # Don't delete the order here - it should only be deleted when payment is completed

        return jsonify({
            "success": True,
            "order": order_data,
            "message": f"Order for {order.customerName} loaded successfully"
        }), 200

    except InvalidId:
        return jsonify({
            "success": False,
            "message": "Invalid order ID"
        }), 400
    except Exception as e:
        current_app.logger.error(f"Error loading terminal order: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error loading terminal order: {str(e)}"
        }), 500

@pos_bp.route('/pos/delete_terminal_order/<order_id>', methods=['DELETE'])
@login_required
def delete_terminal_order(order_id):
    """
    Delete a specific terminal order after payment is completed
    """
    try:
        from models.terminal_order_model import TerminalOrder

        # Get the specific order
        order = TerminalOrder.objects(
            id=ObjectId(order_id),
            username=current_user.username
        ).first()

        if not order:
            return jsonify({
                "success": False,
                "message": "Order not found"
            }), 404

        # Delete the order
        customer_name = order.customerName
        order.delete()

        return jsonify({
            "success": True,
            "message": f"Terminal order for {customer_name} deleted successfully"
        }), 200

    except InvalidId:
        return jsonify({
            "success": False,
            "message": "Invalid order ID"
        }), 400
    except Exception as e:
        current_app.logger.error(f"Error deleting terminal order: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error deleting terminal order: {str(e)}"
        }), 500

def create_pos_bp():
    return pos_bp
