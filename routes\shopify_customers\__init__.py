from flask import Blueprint
from pymongo import MongoClient

def create_customers_bp(mongo_client):
    customers_bp = Blueprint('shopify_customers', __name__)

    # Import route modules
    from . import core, details, gift_cards, sync

    # Register routes from each module
    core.init_routes(customers_bp, mongo_client)
    details.init_routes(customers_bp, mongo_client)
    gift_cards.init_routes(customers_bp, mongo_client)
    sync.init_routes(customers_bp, mongo_client)

    return customers_bp

# Create MongoDB client
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
client = MongoClient(mongo_uri)
shopify_customers_bp = create_customers_bp(client)
