from flask import Blueprint
from .pricing_settings import create_pricing_settings_routes
from .games import create_games_routes
from .main import create_main_routes

def create_autopricing_bp():
    """Create the main autopricing blueprint."""
    autopricing_bp = Blueprint('shopify_autopricing', __name__, url_prefix='/shopify/autopricing')

    # Register main routes
    create_main_routes(autopricing_bp)

    # Register pricing settings routes
    create_pricing_settings_routes(autopricing_bp)

    # Register games routes
    create_games_routes(autopricing_bp)

    return autopricing_bp
