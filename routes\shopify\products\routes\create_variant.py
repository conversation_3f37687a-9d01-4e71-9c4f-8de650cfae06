"""
API endpoint for creating new variants for Shopify products.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
import shopify
import os
import json
import logging
from pymongo import MongoClient
from bson.objectid import ObjectId
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = os.environ.get('MONGO_URI', 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin')
mongo_client = MongoClient(mongo_uri)
db = mongo_client[os.environ.get('MONGO_DBNAME', 'test')]
shopify_collection = db['shProducts']

# Create Blueprint
create_variant_bp = Blueprint('create_variant', __name__)

@create_variant_bp.route('/shopify/products/api/create_variant', methods=['POST'])
@login_required
def create_variant():
    """Create a new variant for a Shopify product."""
    try:
        # Get request data
        data = request.json
        product_id = data.get('productId')
        title = data.get('title')
        price = data.get('price', 0)
        inventory_quantity = data.get('inventory_quantity', 0)
        sku = data.get('sku', '')
        
        if not product_id or not title:
            return jsonify({"error": "Product ID and title are required"}), 400
        
        # Find the product in the database
        product = shopify_collection.find_one({"_id": ObjectId(product_id), "username": current_user.username})
        
        if not product:
            return jsonify({"error": "Product not found"}), 404
        
        # Initialize Shopify API
        shop_url = product.get('shop_url')
        if not shop_url:
            return jsonify({"error": "Shop URL not found for this product"}), 400
        
        api_key = os.environ.get('SHOPIFY_API_KEY')
        api_password = os.environ.get('SHOPIFY_PASSWORD')
        
        if not api_key or not api_password:
            return jsonify({"error": "Shopify API credentials not configured"}), 500
        
        shop_session = shopify.Session(shop_url, '2023-01', api_password)
        shopify.ShopifyResource.activate_session(shop_session)
        
        # Get the Shopify product
        shopify_product_id = product.get('id')
        if not shopify_product_id:
            return jsonify({"error": "Shopify product ID not found"}), 400
        
        shopify_product = shopify.Product.find(shopify_product_id)
        
        # Create a new variant
        new_variant = shopify.Variant({
            'product_id': shopify_product_id,
            'title': title,
            'price': price,
            'sku': sku,
            'inventory_management': 'shopify',
            'inventory_policy': 'deny',  # Don't sell when out of stock
            'inventory_quantity': inventory_quantity,
            'option1': title  # Use title as option1 value
        })
        
        # Save the new variant
        success = new_variant.save()
        
        if not success:
            return jsonify({"error": "Failed to create variant in Shopify"}), 500
        
        # Update the product in the database
        variant_data = {
            'id': new_variant.id,
            'product_id': shopify_product_id,
            'title': title,
            'price': price,
            'sku': sku,
            'inventory_quantity': inventory_quantity,
            'created_at': datetime.now().isoformat()
        }
        
        # Add the new variant to the product's variants array
        shopify_collection.update_one(
            {"_id": ObjectId(product_id)},
            {"$push": {"variants": variant_data}}
        )
        
        # Update the total inventory quantity
        current_total = sum(v.get('inventory_quantity', 0) for v in product.get('variants', []))
        new_total = current_total + inventory_quantity
        
        shopify_collection.update_one(
            {"_id": ObjectId(product_id)},
            {"$set": {"total_inventory": new_total}}
        )
        
        # Return the new variant data
        return jsonify({
            "success": True,
            "message": f"Created new variant: {title}",
            "variant": variant_data
        })
        
    except Exception as e:
        logger.error(f"Error creating variant: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        # Deactivate the Shopify session
        shopify.ShopifyResource.clear_session()

def create_variant_blueprint():
    """Factory function to create the blueprint."""
    return create_variant_bp
