{% extends "base.html" %}

{% block title %}Point of Sale{% endblock %}

{% block content %}
<style>
    /* Custom styles for POS page - Updated to match app theme */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }
    
    .main-content {
        background-color: #6b21a8;
    }
    
    .pos-header {
        background: linear-gradient(to right, #191927, #1f1f33);
        color: white;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        border: 4px solid var(--primary-color);
    }
    
    .pos-card {
        border-radius: 0.5rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        border: none;
        background-color: #1e293b;
    }
    
    .pos-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
    }
    
    .pos-card-header {
        background: linear-gradient(to right, #191927, #1f1f33);
        color: white;
        padding: 1rem;
        border-top-left-radius: 0.5rem;
        border-top-right-radius: 0.5rem;
        font-weight: bold;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .pos-card-body {
        padding: 1.5rem;
        color: var(--sidebar-text);
    }
    
    .pos-btn {
        border-radius: 0.25rem;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .pos-btn-primary {
        background: var(--primary-color);
        border: none;
    }
    
    .pos-btn-primary:hover {
        background: var(--primary-color-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .pos-btn-success {
        background: var(--success-color);
        border: none;
    }
    
    .pos-btn-success:hover {
        background: #27ae60; /* Darker shade of success color */
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .pos-btn-danger {
        background: var(--danger-color);
        border: none;
    }
    
    .pos-btn-danger:hover {
        background: #c0392b; /* Darker shade of danger color */
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .pos-btn-warning {
        background: var(--warning-color);
        border: none;
    }
    
    .pos-btn-warning:hover {
        background: #f39c12; /* Darker shade of warning color */
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .pos-btn-info {
        background: var(--info-color);
        border: none;
    }
    
    .pos-btn-info:hover {
        background: #2980b9; /* Darker shade of info color */
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .pos-form-control {
        border-radius: 0.25rem;
        padding: 0.75rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background-color: #2d3748;
        color: white;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    
    .pos-form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(233, 30, 99, 0.25);
    }
    
    .pos-list-item {
        border-radius: 0.25rem;
        margin-bottom: 0.75rem;
        transition: transform 0.3s ease;
    }
    
    .pos-list-item:hover {
        transform: translateX(5px);
    }
    
    .pos-section {
        margin-bottom: 2rem;
        background: #1e293b;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .pos-section-title {
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 0.5rem;
        margin-bottom: 1.5rem;
        color: white;
        font-weight: 600;
        letter-spacing: 0.5px;
    }
    
    .pos-dark-card {
        background-color: #191927;
        color: white;
    }
    
    .pos-dark-list-item {
        background-color: #1f1f33;
        border: none;
        color: white;
        padding: 1rem;
    }
    
    .pos-icon {
        margin-right: 0.5rem;
        color: var(--primary-color);
    }
    
    .pos-badge {
        background-color: var(--primary-color);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.85rem;
        margin-left: 0.5rem;
    }
    
    .pos-divider {
        height: 1px;
        background: linear-gradient(to right, transparent, var(--primary-color), transparent);
        margin: 1.5rem 0;
    }
    
    /* Improved organization and spacing */
    .container-fluid {
        padding: 2rem;
    }
    
    label {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    small.form-text {
        color: rgba(255, 255, 255, 0.6) !important;
    }
    
    .modal-content {
        background-color: #191927 !important;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .modal-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
    
    .modal-footer {
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
</style>

<div class="container-fluid">
    <!-- Till Management Section -->
    <div class="pos-section">
        <h2 class="pos-section-title">
            <i class="fas fa-money-bill-wave pos-icon"></i>Till Management
        </h2>
        <div class="row">
            <!-- Create Till Section -->
            <div class="col-md-6 mb-4">
                <div class="pos-card">
                    <div class="pos-card-header">
                        <i class="fas fa-plus-circle pos-icon"></i>Create Till
                    </div>
                    <div class="pos-card-body">
                        <form id="createTillForm">
                            <h6><i class="fab fa-shopify pos-icon"></i>Shopify Location Settings</h6>
                            <div class="form-group">
                                <label for="shopifyLocation">Shopify Location</label>
                                <select class="form-control pos-form-control" id="shopifyLocation">
                                    <option value="">Select a Shopify location...</option>
                                    <!-- Locations will be populated dynamically -->
                                </select>
                                <small class="form-text text-muted">Select the Shopify location to use for inventory deductions.</small>
                            </div>
                            
                            <div class="pos-divider"></div>
                            
                            <div class="form-group">
                                <label for="location"><i class="fas fa-map-marker-alt pos-icon"></i>Nickname</label>
                                <input type="text" class="form-control pos-form-control" id="location" required>
                            </div>
                            <div class="form-group">
                                <label for="startingFloat"><i class="fas fa-dollar-sign pos-icon"></i>Starting Float</label>
                                <input type="number" step="0.01" class="form-control pos-form-control" id="startingFloat" required>
                            </div>
                            <div class="form-group">
                                <label for="taxRate"><i class="fas fa-percentage pos-icon"></i>Tax Rate (%)</label>
                                <input type="number" step="0.01" class="form-control pos-form-control" id="taxRate" required>
                            </div>

                            <input type="hidden" id="taxInclusive" name="taxInclusive" value="true">

                            <div class="pos-divider"></div>

                            <!-- Merchant Match Card Machine Settings -->
                            <div class="card bg-secondary mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-credit-card pos-icon"></i>Merchant Match Card Machine Settings</h6>
                                    <small class="text-muted">Optional - Configure card machine integration</small>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="tpn"><i class="fas fa-hashtag pos-icon"></i>TPN (Terminal Provider Number)</label>
                                        <input type="text" class="form-control pos-form-control" id="tpn" placeholder="Enter TPN">
                                        <small class="form-text text-muted">Terminal Provider Number from Merchant Match</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="registerId"><i class="fas fa-cash-register pos-icon"></i>Register ID</label>
                                        <input type="text" class="form-control pos-form-control" id="registerId" placeholder="Enter Register ID">
                                        <small class="form-text text-muted">Register identifier for this till</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="authKey"><i class="fas fa-key pos-icon"></i>Auth Key</label>
                                        <input type="text" class="form-control pos-form-control" id="authKey" placeholder="Enter Auth Key">
                                        <small class="form-text text-muted">Authentication key for card machine</small>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary pos-btn pos-btn-primary btn-block">
                                Create Till
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Existing Tills Section -->
            <div class="col-md-6 mb-4">
                <div class="pos-card pos-dark-card">
                    <div class="pos-card-header">
                        <i class="fas fa-list pos-icon"></i>Existing Tills
                    </div>
                    <div class="pos-card-body">
                        <ul class="list-group" id="tillsList">
                            {% for till in tills %}
                            <li class="list-group-item pos-list-item pos-dark-list-item">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0">{{ till.location }}</h5>
                                    <span class="pos-badge">${{ till.running_total | round(2) }}</span>
                                </div>

                                <!-- Orders and Balance Section -->
                                <div class="mb-3">
                                    <div class="card bg-dark">
                                        <div class="card-body p-2">
                                            <h6 class="mb-2"><i class="fas fa-shopping-cart pos-icon"></i>Today's Orders</h6>
                                            {% set total = namespace(cash=0, card=0, count=0) %}
                                            {% for transaction in till.transactions %}
                                                {% if transaction.timestamp.strftime('%Y-%m-%d') == current_date %}
                                                    {% set total.count = total.count + 1 %}
                                                    {% if transaction.payment_method == 'cash' %}
                                                        {% set total.cash = total.cash + transaction.total %}
                                                    {% elif transaction.payment_method == 'card' %}
                                                        {% set total.card = total.card + transaction.total %}
                                                    {% endif %}
                                                {% endif %}
                                            {% endfor %}
                                            
                                            <div class="row">
                                                <div class="col-6">
                                                    <small>Orders: {{ total.count }}</small>
                                                </div>
                                                <div class="col-6 text-right">
                                                    <small>Total: ${{ (total.cash + total.card)|round(2) }}</small>
                                                </div>
                                            </div>

                                            <div class="mt-2">
                                                <small><strong>Cash:</strong> ${{ total.cash|round(2) }}</small><br>
                                                <small><strong>Card:</strong> ${{ total.card|round(2) }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <strong><i class="fas fa-dollar-sign pos-icon"></i>Starting Float:</strong> ${{ till.starting_float | round(2) }}<br>
                                    <strong><i class="fas fa-percentage pos-icon"></i>Tax Rate:</strong> {{ till.tax_rate }}%
                                </div>
                                
                                {% if till.shopify_location_id %}
                                <div class="mb-2">
                                    <strong><i class="fab fa-shopify pos-icon"></i>Shopify Location:</strong><br>
                                    <span class="ml-3">{{ till.shopify_location_name }} (ID: {{ till.shopify_location_id }})</span>
                                </div>
                                {% endif %}
                                
                                {% if till.tpn or till.register_id or till.auth_key %}
                                <div class="mb-2">
                                    <strong><i class="fas fa-credit-card pos-icon"></i>Card Machine Settings:</strong><br>
                                    {% if till.tpn %}<span class="ml-3">TPN: {{ till.tpn }}</span><br>{% endif %}
                                    {% if till.register_id %}<span class="ml-3">Register ID: {{ till.register_id }}</span><br>{% endif %}
                                    {% if till.auth_key %}<span class="ml-3">Auth Key: {{ till.auth_key }}</span><br>{% endif %}
                                </div>
                                {% endif %}
                                
                                <div class="mt-3">
                                    {% if till.status != 'closed' %}
                                    <a href="{{ url_for('pos.start_pos', till_id=till.id) }}" class="btn btn-success pos-btn pos-btn-success mr-2">
                                        <i class="fas fa-play pos-icon"></i>Start POS
                                    </a>
                                    <a href="#" class="btn btn-warning pos-btn pos-btn-warning mr-2" onclick="event.preventDefault(); closeTill('{{ till.id }}', '{{ till.location }}', {{ till.starting_float }})">
                                        <i class="fas fa-cash-register pos-icon"></i>Close Till
                                    </a>
                                    {% else %}
                                    <a href="#" class="btn btn-info pos-btn pos-btn-info mr-2" onclick="event.preventDefault(); reopenTill('{{ till.id }}', '{{ till.location }}')">
                                        <i class="fas fa-redo pos-icon"></i>Reopen Till
                                    </a>
                                    <span class="badge badge-danger ml-2">Closed</span>
                                    {% endif %}
                                    <a href="#" class="btn btn-primary pos-btn pos-btn-primary mr-2" onclick="event.preventDefault(); editTillLocation('{{ till.id }}', '{{ till.location }}')">
                                        <i class="fas fa-edit pos-icon"></i>Edit Nickname
                                    </a>
                                    <a href="#" class="btn btn-primary pos-btn pos-btn-primary mr-2" onclick="event.preventDefault(); viewTillClosingHistory('{{ till.id }}', '{{ till.location }}')">
                                        <i class="fas fa-history pos-icon"></i>Closing History
                                    </a>
                                    <a href="#" class="btn btn-danger pos-btn pos-btn-danger" onclick="event.preventDefault(); deleteTill('{{ till.id }}')">
                                        <i class="fas fa-trash pos-icon"></i>Delete
                                    </a>
                                </div>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Edit Employee Modal -->
<div class="modal fade" id="editEmployeeModal" tabindex="-1" role="dialog" aria-labelledby="editEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-success" id="editEmployeeModalLabel">
                    <i class="fas fa-user-edit pos-icon"></i>Edit Employee
                </h5>
                <button type="button" class="close text-white" onclick="closeEditEmployeeModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editEmployeeForm">
                    <input type="hidden" id="editEmployeeId">
                    <div class="form-group">
                        <label for="editEmployeeName"><i class="fas fa-user pos-icon"></i>Employee Name</label>
                        <input type="text" class="form-control bg-secondary text-white pos-form-control" id="editEmployeeName" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeePin"><i class="fas fa-key pos-icon"></i>PIN</label>
                        <input type="password" class="form-control bg-secondary text-white pos-form-control" id="editEmployeePin" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary pos-btn" onclick="closeEditEmployeeModal()">
                    <i class="fas fa-times pos-icon"></i>Cancel
                </button>
                <button type="button" class="btn btn-success pos-btn pos-btn-success" onclick="updateEmployee()">
                    <i class="fas fa-save pos-icon"></i>Save changes
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Till Location Modal -->
<div class="modal fade" id="editTillLocationModal" tabindex="-1" role="dialog" aria-labelledby="editTillLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-success" id="editTillLocationModalLabel">
                    <i class="fas fa-edit pos-icon"></i>Edit Till Settings
                </h5>
                <button type="button" class="close text-white" onclick="closeEditTillLocationModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editTillLocationForm">
                    <input type="hidden" id="editTillId">
                    <div class="form-group">
                        <label><i class="fas fa-map-marker-alt pos-icon"></i>Till Nickname: <span id="editTillLocationName" class="text-info"></span></label>
                    </div>
                    <div class="form-group">
                        <label for="editShopifyLocation"><i class="fab fa-shopify pos-icon"></i>Shopify Location</label>
                        <select class="form-control bg-secondary text-white pos-form-control" id="editShopifyLocation">
                            <option value="">Select a Shopify location...</option>
                            <!-- Locations will be populated dynamically -->
                        </select>
                        <small class="form-text text-light">Select the Shopify location to use for inventory deductions.</small>
                    </div>

                    <div class="pos-divider"></div>

                    <!-- Merchant Match Card Machine Settings -->
                    <div class="card bg-secondary mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-credit-card pos-icon"></i>Merchant Match Card Machine Settings</h6>
                            <small class="text-muted">Configure card machine integration</small>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="editTpn"><i class="fas fa-hashtag pos-icon"></i>TPN (Terminal Provider Number)</label>
                                <input type="text" class="form-control bg-secondary text-white pos-form-control" id="editTpn" placeholder="Enter TPN">
                                <small class="form-text text-light">Terminal Provider Number from Merchant Match</small>
                            </div>
                            <div class="form-group">
                                <label for="editRegisterId"><i class="fas fa-cash-register pos-icon"></i>Register ID</label>
                                <input type="text" class="form-control bg-secondary text-white pos-form-control" id="editRegisterId" placeholder="Enter Register ID">
                                <small class="form-text text-light">Register identifier for this till</small>
                            </div>
                            <div class="form-group">
                                <label for="editAuthKey"><i class="fas fa-key pos-icon"></i>Auth Key</label>
                                <input type="text" class="form-control bg-secondary text-white pos-form-control" id="editAuthKey" placeholder="Enter Auth Key">
                                <small class="form-text text-light">Authentication key for card machine</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary pos-btn" onclick="closeEditTillLocationModal()">
                    <i class="fas fa-times pos-icon"></i>Cancel
                </button>
                <button type="button" class="btn btn-success pos-btn pos-btn-success" onclick="updateTillLocation()">
                    <i class="fas fa-save pos-icon"></i>Save changes
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Close Till Modal -->
<div class="modal fade" id="closeTillModal" tabindex="-1" role="dialog" aria-labelledby="closeTillModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-warning" id="closeTillModalLabel">
                    <i class="fas fa-cash-register pos-icon"></i>Close Till
                </h5>
                <button type="button" class="close text-white" onclick="closeCloseTillModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="closeTillForm">
                    <input type="hidden" id="closeTillId">
                    <div class="form-group">
                        <label><i class="fas fa-map-marker-alt pos-icon"></i>Till Nickname: <span id="closeTillLocationName" class="text-info"></span></label>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-secondary mb-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-money-bill-wave pos-icon"></i>Cash Transactions
                                    </div>
                                    <div>
                                        <span id="cashTransactionsTotal" class="badge badge-info mr-2">$0.00</span>
                                        <button class="btn btn-sm btn-dark" type="button" data-toggle="collapse" data-target="#cashTransactionsCollapse" aria-expanded="false" aria-controls="cashTransactionsCollapse">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="collapse" id="cashTransactionsCollapse">
                                        <div id="cashTransactionsList">
                                            <p class="text-center">Loading transactions...</p>
                                        </div>
                                    </div>
                                    <div id="cashTransactionsSummary" class="text-center mt-2">
                                        <p>Loading summary...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-secondary mb-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-credit-card pos-icon"></i>Card Transactions
                                    </div>
                                    <div>
                                        <span id="cardTransactionsTotal" class="badge badge-info mr-2">$0.00</span>
                                        <button class="btn btn-sm btn-dark" type="button" data-toggle="collapse" data-target="#cardTransactionsCollapse" aria-expanded="false" aria-controls="cardTransactionsCollapse">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="collapse" id="cardTransactionsCollapse">
                                        <div id="cardTransactionsList">
                                            <p class="text-center">Loading transactions...</p>
                                        </div>
                                    </div>
                                    <div id="cardTransactionsSummary" class="text-center mt-2">
                                        <p>Loading summary...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card bg-secondary mb-3">
                        <div class="card-header">
                            <i class="fas fa-balance-scale pos-icon"></i>Cash Reconciliation
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="startingFloat"><i class="fas fa-dollar-sign pos-icon"></i>Starting Float</label>
                                        <input type="number" step="0.01" class="form-control bg-dark text-white pos-form-control" id="startingFloat" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label for="cashSales"><i class="fas fa-cash-register pos-icon"></i>Cash Sales</label>
                                        <input type="number" step="0.01" class="form-control bg-dark text-white pos-form-control" id="cashSales" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label for="expectedCash"><i class="fas fa-calculator pos-icon"></i>Expected Cash in Till</label>
                                        <input type="number" step="0.01" class="form-control bg-dark text-white pos-form-control" id="expectedCash" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="actualCash"><i class="fas fa-money-bill-alt pos-icon"></i>Actual Cash Count</label>
                                        <div class="input-group">
                                            <input type="number" step="0.01" class="form-control bg-secondary text-white pos-form-control" id="actualCash" required>
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-info pos-btn" onclick="openCashCountModal()">
                                                    <i class="fas fa-calculator pos-icon"></i>Count Cash
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="discrepancy"><i class="fas fa-exclamation-triangle pos-icon"></i>Discrepancy</label>
                                        <input type="number" step="0.01" class="form-control bg-dark text-white pos-form-control" id="discrepancy" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label for="closingNotes"><i class="fas fa-sticky-note pos-icon"></i>Notes</label>
                                        <textarea class="form-control bg-secondary text-white pos-form-control" id="closingNotes" rows="3" placeholder="Enter any notes about discrepancies or issues..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary pos-btn" onclick="closeCloseTillModal()">
                    <i class="fas fa-times pos-icon"></i>Cancel
                </button>
                <button type="button" class="btn btn-warning pos-btn" onclick="confirmCloseTill()">
                    <i class="fas fa-cash-register pos-icon"></i>Close Till
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reopen Till Modal -->
<div class="modal fade" id="reopenTillModal" tabindex="-1" role="dialog" aria-labelledby="reopenTillModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-info" id="reopenTillModalLabel">
                    <i class="fas fa-redo pos-icon"></i>Reopen Till
                </h5>
                <button type="button" class="close text-white" onclick="closeReopenTillModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="reopenTillForm">
                    <input type="hidden" id="reopenTillId">
                    <div class="form-group">
                        <label><i class="fas fa-map-marker-alt pos-icon"></i>Till Nickname: <span id="reopenTillLocationName" class="text-info"></span></label>
                    </div>
                    <div class="form-group">
                        <label for="reopenStartingFloat"><i class="fas fa-dollar-sign pos-icon"></i>Starting Float</label>
                        <input type="number" step="0.01" class="form-control bg-secondary text-white pos-form-control" id="reopenStartingFloat" required>
                        <small class="form-text text-light">This will be the new starting float for the reopened till.</small>
                    </div>
                    <div class="form-group">
                        <label for="reopenNotes"><i class="fas fa-sticky-note pos-icon"></i>Notes</label>
                        <textarea class="form-control bg-secondary text-white pos-form-control" id="reopenNotes" rows="3" placeholder="Enter any notes about reopening the till..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary pos-btn" onclick="closeReopenTillModal()">
                    <i class="fas fa-times pos-icon"></i>Cancel
                </button>
                <button type="button" class="btn btn-info pos-btn pos-btn-info" onclick="confirmReopenTill()">
                    <i class="fas fa-redo pos-icon"></i>Reopen Till
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Card Machine Modal removed as requested -->

<!-- Till Closing History Modal -->
<div class="modal fade" id="tillClosingHistoryModal" tabindex="-1" role="dialog" aria-labelledby="tillClosingHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-info" id="tillClosingHistoryModalLabel">
                    <i class="fas fa-history pos-icon"></i>Till Closing History
                </h5>
                <button type="button" class="close text-white" onclick="closeTillClosingHistoryModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="historyTillId">
                <div class="form-group">
                    <label><i class="fas fa-map-marker-alt pos-icon"></i>Till Nickname: <span id="historyTillLocationName" class="text-info"></span></label>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>Date/Time</th>
                                <th>Z-Report #</th>
                                <th>Employee</th>
                                <th>Starting Float</th>
                                <th>Ending Float</th>
                                <th>Discrepancy</th>
                                <th>Total Sales</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="closingHistoryTableBody">
                            <tr>
                                <td colspan="8" class="text-center">Loading closing history...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary pos-btn" onclick="closeTillClosingHistoryModal()">
                    <i class="fas fa-times pos-icon"></i>Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Till Closing Report Modal -->
<div class="modal fade" id="tillClosingReportModal" tabindex="-1" role="dialog" aria-labelledby="tillClosingReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-info" id="tillClosingReportModalLabel">
                    <i class="fas fa-file-alt pos-icon"></i>Z-Report
                </h5>
                <button type="button" class="close text-white" onclick="closeTillClosingReportModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="closingReportContent">
                    <p class="text-center">Loading report...</p>
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary pos-btn" onclick="closeTillClosingReportModal()">
                    <i class="fas fa-times pos-icon"></i>Close
                </button>
                <button type="button" class="btn btn-info pos-btn" onclick="printClosingReport()">
                    <i class="fas fa-print pos-icon"></i>Print Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cash Count Modal -->
<div class="modal fade" id="cashCountModal" tabindex="-1" role="dialog" aria-labelledby="cashCountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-info" id="cashCountModalLabel">
                    <i class="fas fa-money-bill-wave pos-icon"></i>Cash Count
                </h5>
                <button type="button" class="close text-white" onclick="closeCashCountModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="cashCountForm">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-money-bill-alt pos-icon"></i>Notes</h5>
                            <div id="notesDenominations">
                                <!-- Notes denominations will be dynamically populated here -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-coins pos-icon"></i>Coins</h5>
                            <div id="coinsDenominations">
                                <!-- Coins denominations will be dynamically populated here -->
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-secondary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="fas fa-calculator pos-icon"></i>Total Cash Count</h5>
                                        <h4 id="totalCashCount" class="mb-0 text-success">$0.00</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary pos-btn" onclick="closeCashCountModal()">
                    <i class="fas fa-times pos-icon"></i>Cancel
                </button>
                <button type="button" class="btn btn-success pos-btn pos-btn-success" onclick="saveCashCount()">
                    <i class="fas fa-save pos-icon"></i>Save Cash Count
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Include CSS files -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom-alerts.css') }}">

<!-- Include JavaScript files -->
<script src="{{ url_for('static', filename='js/custom-alerts.js') }}"></script>
<script src="{{ url_for('static', filename='js/tax-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/tax-handler.js') }}"></script>
<script src="{{ url_for('static', filename='js/pos.js') }}"></script>
{% endblock %}
