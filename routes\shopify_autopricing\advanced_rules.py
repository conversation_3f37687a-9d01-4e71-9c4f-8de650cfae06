from flask import jsonify, request
from flask_login import login_required, current_user
import logging
from models.user_model import User
from models.shproducts_model import ShProducts

logger = logging.getLogger(__name__)

def create_advanced_rules_routes(bp):
    @bp.route('/api/vendors', methods=['GET'])
    @login_required
    def get_vendors():
        vendors = ShProducts.objects(
            username=current_user.username
        ).distinct('vendor')
        # Filter out None values and sort
        vendors = [v for v in vendors if v is not None]
        return jsonify({'vendors': sorted(vendors)})

    @bp.route('/api/autopricing-product-types', methods=['GET'])
    @login_required
    def get_autopricing_product_types():
        vendor = request.args.get('vendor')
        query = {'username': current_user.username}
        if vendor:
            query['vendor'] = vendor
            
        product_types = ShProducts.objects(**query).distinct('product_type')
        # Filter out None values and sort
        product_types = [pt for pt in product_types if pt is not None]
        return jsonify({'productTypes': sorted(product_types)})

    @bp.route('/api/expansion-names', methods=['GET'])
    @login_required
    def get_expansion_names():
        vendor = request.args.get('vendor')
        product_type = request.args.get('productType')
        query = {'username': current_user.username}
        if vendor:
            query['vendor'] = vendor
        if product_type:
            query['product_type'] = product_type
            
        expansion_names = ShProducts.objects(**query).distinct('expansionName')
        # Filter out None values and sort
        expansion_names = [en for en in expansion_names if en is not None]
        return jsonify({'expansionNames': sorted(expansion_names)})

    @bp.route('/api/advanced-pricing-rules', methods=['GET', 'POST', 'DELETE'])
    @login_required
    def manage_advanced_pricing_rules():
        vendor = request.args.get('vendor') if request.method == 'GET' else request.json.get('vendor')
        product_type = request.args.get('productType') if request.method == 'GET' else request.json.get('productType')
        expansion = request.args.get('expansion') if request.method == 'GET' else request.json.get('expansion')

        if not all([vendor, product_type, expansion]):
            return jsonify({'error': 'Missing required parameters'}), 400

        try:
            user = User.objects(username=current_user.username).first()
            if not user:
                return jsonify({'error': 'User not found'}), 404

            key = f"{vendor}_{product_type}_{expansion}"

            # Update affected products' advanced pricing status
            products = ShProducts.objects(
                username=current_user.username,
                vendor=vendor,
                product_type=product_type,
                expansionName=expansion
            )

            if request.method == 'GET':
                rules = user.advancedPricingRules.get(key, user.customStepping)
                return jsonify({
                    'rules': rules,
                    'hasAdvancedPricing': key in user.advancedPricingRules
                })

            elif request.method == 'POST':
                rules = request.json.get('rules')
                if not rules:
                    return jsonify({'error': 'Missing rules'}), 400

                user.advancedPricingRules[key] = rules
                user.save()

                # Mark products as using advanced pricing
                for product in products:
                    product.uses_advanced_pricing = True
                    product.save()

                return jsonify({'message': 'Advanced pricing rules saved successfully'})

            elif request.method == 'DELETE':
                if key in user.advancedPricingRules:
                    del user.advancedPricingRules[key]
                    user.save()

                # Mark products as not using advanced pricing
                for product in products:
                    product.uses_advanced_pricing = False
                    product.save()

                return jsonify({'message': 'Advanced pricing rules removed successfully'})

        except Exception as e:
            logger.error(f"Error managing advanced pricing rules: {str(e)}")
            return jsonify({'error': 'Failed to manage advanced pricing rules'}), 500

    @bp.route('/api/advanced-pricing-rules/all', methods=['GET'])
    @login_required
    def get_all_advanced_pricing_rules():
        """Get all advanced pricing rules for the current user"""
        try:
            user = User.objects(username=current_user.username).first()
            if not user:
                return jsonify({'error': 'User not found'}), 404

            rules = []
            for key, rule in user.advancedPricingRules.items():
                # Split the composite key into its components
                vendor, product_type, expansion = key.split('_')
                rules.append({
                    'id': key,  # Use composite key as ID
                    'vendor': vendor,
                    'productType': product_type,
                    'expansion': expansion,
                    'rules': rule
                })

            return jsonify({
                'rules': sorted(rules, key=lambda x: (x['vendor'], x['productType'], x['expansion']))
            })

        except Exception as e:
            logger.error(f"Error getting advanced pricing rules: {str(e)}")
            return jsonify({'error': 'Failed to get advanced pricing rules'}), 500

    @bp.route('/api/advanced-pricing-rules/<rule_id>', methods=['PUT', 'DELETE'])
    @login_required
    def manage_specific_rule(rule_id):
        """Update or delete a specific advanced pricing rule"""
        try:
            user = User.objects(username=current_user.username).first()
            if not user:
                return jsonify({'error': 'User not found'}), 404

            # Split rule_id into components
            vendor, product_type, expansion = rule_id.split('_')

            # Get affected products
            products = ShProducts.objects(
                username=current_user.username,
                vendor=vendor,
                product_type=product_type,
                expansionName=expansion
            )

            if request.method == 'PUT':
                data = request.json
                rules = data.get('rules')
                if not rules:
                    return jsonify({'error': 'Rules are required'}), 400

                user.advancedPricingRules[rule_id] = rules
                user.save()

                # Ensure products are marked as using advanced pricing
                for product in products:
                    product.uses_advanced_pricing = True
                    product.save()

                return jsonify({'message': 'Rule updated successfully'})

            elif request.method == 'DELETE':
                if rule_id in user.advancedPricingRules:
                    del user.advancedPricingRules[rule_id]
                    user.save()

                    # Mark products as not using advanced pricing
                    for product in products:
                        product.uses_advanced_pricing = False
                        product.save()

                return jsonify({'message': 'Rule deleted successfully'})

        except Exception as e:
            logger.error(f"Error managing advanced pricing rule: {str(e)}")
            return jsonify({'error': 'Failed to manage advanced pricing rule'}), 500
