import logging
from flask import request, session
from flask_login import login_user, current_user
from models.user_model import User
from functools import wraps

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def is_localhost(request):
    """Check if the request is coming from localhost"""
    return request.remote_addr in ['127.0.0.1', 'localhost', '::1'] or request.host.startswith('localhost')

def localhost_auth_middleware(app):
    """Middleware to bypass authentication for localhost requests"""
    @app.before_request
    def auto_login_from_localhost():
        # Skip if user is already authenticated
        if current_user.is_authenticated:
            return
        
        # Check if request is from localhost
        if is_localhost(request):
            # Find or create the admintcg user
            admin_user = User.objects(username='admintcg').first()
            
            if admin_user:
                # Log in as admintcg
                login_user(admin_user)
                logger.info(f"Auto-login from localhost as admintcg")
            else:
                logger.warning(f"Localhost auto-login attempted but admintcg user not found")
