{% extends "base.html" %}
{% block title %}Inventory Dashboard{% endblock %}
{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }

    /* Section header styling */
    .section-header {
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    }

    .section-title {
        color: #ffffff;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .section-subtitle {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.875rem;
        margin-bottom: 0;
    }

    /* Coming soon badge styling */
    .coming-soon-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        z-index: 10;
    }

    /* Locked card styling */
    .card-locked {
        position: relative;
        opacity: 0.7;
        cursor: not-allowed;
    }

    .card-locked .btn {
        pointer-events: none;
        opacity: 0.6;
    }

    .card-locked::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        z-index: 5;
    }
</style>
<div class="container mt-5">
    <!-- Core Inventory Management Section -->
    <div class="section-header">
        <h2 class="section-title">
            <i class="fas fa-boxes me-2" style="color: #e74c3c;"></i>
            Core Inventory Management
        </h2>
        <p class="section-subtitle">Essential tools for viewing your inventory and managing your product catalog</p>
    </div>

    <div class="row">
        <!-- View Inventory Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(231, 76, 60, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-boxes fa-2x" style="color: #e74c3c;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">View Inventory</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Browse and manage your current inventory. View stock levels, prices, and detailed product information with advanced filtering options.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('products.products') }}" class="btn w-100" style="background-color: #e74c3c; border-color: #e74c3c; color: white;">
                            <i class="fas fa-eye me-2"></i>View Inventory
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Catalog Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(46, 204, 113, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-plus-circle fa-2x" style="color: #2ecc71;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Add Catalog</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Update your product catalog with new items. Add detailed information, set competitive prices, and manage product variants efficiently.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('update_catalog.update_catalog') }}" class="btn w-100" style="background-color: #2ecc71; border-color: #2ecc71; color: white;">
                            <i class="fas fa-plus me-2"></i>Add Catalog
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Warehouse Operations Section -->
    <div class="section-header mt-5">
        <h2 class="section-title">
            <i class="fas fa-warehouse me-2" style="color: #3498db;"></i>
            Warehouse Operations
        </h2>
        <p class="section-subtitle">Physical inventory management, location tracking, and warehouse organization</p>
    </div>

    <div class="row">
        <!-- Warehouse Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(52, 152, 219, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-warehouse fa-2x" style="color: #3498db;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Warehouse</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Manage comprehensive warehouse operations. Process inventory movements, handle staging areas, and update stock levels in real-time.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('warehouse.warehouse') }}" class="btn w-100" style="background-color: #3498db; border-color: #3498db; color: white;">
                            <i class="fas fa-dolly me-2"></i>Warehouse Operations
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Locations Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 card-locked" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="coming-soon-badge">Coming Soon</div>
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(231, 76, 60, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-map-marker-alt fa-2x" style="color: #e74c3c;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Locations</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Manage warehouse and store locations. Track where items are situated for efficient organization and quick retrieval.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('inventory.locations') }}" class="btn w-100" style="background-color: #e74c3c; border-color: #e74c3c; color: white;">
                            <i class="fas fa-map-marker-alt me-2"></i>Manage Locations
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Barcodes Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 card-locked" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="coming-soon-badge">Coming Soon</div>
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(142, 68, 173, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-barcode fa-2x" style="color: #8e44ad;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Barcodes</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Manage barcode scanning and generation. Create, print, and scan barcodes for efficient inventory tracking and identification.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('inventory.barcodes') }}" class="btn w-100" style="background-color: #8e44ad; border-color: #8e44ad; color: white;">
                            <i class="fas fa-barcode me-2"></i>Manage Barcodes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Import Methods Section -->
    <div class="section-header mt-5">
        <h2 class="section-title">
            <i class="fas fa-upload me-2" style="color: #9b59b6;"></i>
            Data Import Methods
        </h2>
        <p class="section-subtitle">Various ways to add products to your inventory efficiently</p>
    </div>

    <div class="row">
        <!-- Card Scanning Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(155, 89, 182, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-camera fa-2x" style="color: #9b59b6;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Card Scanning</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Scan trading cards to quickly add them to your inventory. Advanced AI automatically identifies cards and suggests competitive pricing.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('card_scanning.card_scanning') }}" class="btn w-100" style="background-color: #9b59b6; border-color: #9b59b6; color: white;">
                            <i class="fas fa-camera me-2"></i>Scan Cards
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSV Upload Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(243, 156, 18, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-file-csv fa-2x" style="color: #f39c12;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">CSV Upload</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Import inventory data from CSV files. Process bulk inventory updates efficiently with support for multiple data formats and validation.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('csv.csv') }}" class="btn w-100" style="background-color: #f39c12; border-color: #f39c12; color: white;">
                            <i class="fas fa-upload me-2"></i>Upload CSV
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Manual Entry Card -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 card-locked" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="coming-soon-badge">Coming Soon</div>
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(26, 188, 156, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-keyboard fa-2x" style="color: #1abc9c;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Manual Entry</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Manually add individual items to your inventory. Perfect for one-off additions or when you need precise control over item details.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('inventory.manual_entry') }}" class="btn w-100" style="background-color: #1abc9c; border-color: #1abc9c; color: white;">
                            <i class="fas fa-keyboard me-2"></i>Manual Entry
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Features Section -->
    <div class="section-header mt-5">
        <h2 class="section-title">
            <i class="fas fa-cogs me-2" style="color: #e67e22;"></i>
            Advanced Features
        </h2>
        <p class="section-subtitle">Advanced tools for pricing optimization and inventory analytics</p>
    </div>

    <div class="row">
        <!-- Repricing Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(230, 126, 34, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-tags fa-2x" style="color: #e67e22;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Repricing</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Configure automated pricing rules, condition percentages, price preferences, and game-specific minimum prices. Test settings and optimize pricing strategies for maximum profitability.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('shopify_autopricing.index') }}" class="btn w-100" style="background-color: #e67e22; border-color: #e67e22; color: white;">
                            <i class="fas fa-tags me-2"></i>Manage Repricing
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}