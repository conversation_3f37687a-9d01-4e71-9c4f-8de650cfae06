from flask import Blueprint
from routes.shopify.products.routes.products import products_bp
from routes.shopify.products.routes.staged_inventory import staged_inventory_bp
from routes.shopify.products.routes.variants import variants_bp
from routes.shopify.products.routes.pricing import pricing_bp
from routes.shopify.products.routes.auto_reprice import auto_reprice_bp
from routes.shopify.products.routes.create_variant import create_variant_blueprint

shopify_products_bp = Blueprint('shopify_products', __name__, url_prefix='/shopify/products')

# Register child blueprints
shopify_products_bp.register_blueprint(products_bp)
shopify_products_bp.register_blueprint(staged_inventory_bp)
shopify_products_bp.register_blueprint(variants_bp)
shopify_products_bp.register_blueprint(pricing_bp)
shopify_products_bp.register_blueprint(auto_reprice_bp)
shopify_products_bp.register_blueprint(create_variant_blueprint())
