from flask import jsonify, request, Blueprint
from flask_login import login_required, current_user
import logging
from models.user_model import User
from pymongo import MongoClient

logger = logging.getLogger(__name__)

# MongoDB connection
client = MongoClient("mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin")
db = client.test
users_collection = db['user']
rarities_collection = db['rarities']

def get_category_id_for_game(game_name):
    """Get the categoryId for a given game name from the rarities collection"""
    try:
        rarity_doc = rarities_collection.find_one({"gameName": game_name})
        if rarity_doc:
            return rarity_doc.get("categoryId")
        return None
    except Exception as e:
        logger.error(f"Error getting categoryId for game {game_name}: {str(e)}")
        return None

def create_pricing_settings_routes(bp):
    """Create routes for shopify autopricing settings"""

    @bp.route('/api/test')
    def test_api():
        """Test endpoint to check if the API routes are accessible."""
        return jsonify({"message": "Shopify autopricing API is working!"})

    # Route for getting games moved to games.py to avoid duplicate endpoint

    # Route for getting game data moved to games.py to avoid duplicate endpoint

    # Route for managing game minimum prices moved to games.py to avoid duplicate endpoint

    @bp.route('/api/autopricing-settings', methods=['POST'])
    @login_required
    def save_autopricing_settings():
        data = request.json
        lp = data.get('lp_percent')
        mp = data.get('mp_percent')
        hp = data.get('hp_percent')
        dm = data.get('dm_percent')
        nm = data.get('nm_percent')
        min_price = data.get('min_price')
        use_skuid_pricing = data.get('use_skuid_pricing', False)

        try:
            # Get user from MongoDB
            user_data = users_collection.find_one({"username": current_user.username})
            if not user_data:
                return jsonify({'error': 'User not found'}), 404

            # Get current values to use as defaults
            current_stepping = user_data.get('customStepping', {})
            custom_stepping = {
                "lp": float(lp) if lp is not None else current_stepping.get('lp', 80),
                "mp": float(mp) if mp is not None else current_stepping.get('mp', 70),
                "hp": float(hp) if hp is not None else current_stepping.get('hp', 65),
                "dm": float(dm) if dm is not None else current_stepping.get('dm', 50),
                "nm": float(nm) if nm is not None else current_stepping.get('nm', 100)
            }

            # Get current min price to use as default
            current_min_price = user_data.get('minPrice', 0.50)
            new_min_price = float(min_price) if min_price is not None else current_min_price

            # Save price rounding settings
            price_rounding_enabled = data.get('price_rounding_enabled', False)
            price_rounding_thresholds = data.get('price_rounding_thresholds', [49, 99])

            # Validate thresholds
            if not isinstance(price_rounding_thresholds, list):
                return jsonify({'error': 'Invalid price rounding thresholds format'}), 400

            # Filter out invalid thresholds and ensure they're integers between 0 and 99
            price_rounding_thresholds = [
                int(t) for t in price_rounding_thresholds
                if isinstance(t, (int, float)) and 0 <= int(t) <= 99
            ]

            # Update settings directly in MongoDB
            result = users_collection.update_one(
                {"username": current_user.username},
                {"$set": {
                    "price_rounding_enabled": price_rounding_enabled,
                    "price_rounding_thresholds": price_rounding_thresholds,
                    "customStepping": custom_stepping,
                    "minPrice": new_min_price,
                    "use_skuid_pricing": use_skuid_pricing
                }}
            )

            if result.modified_count == 0 and result.matched_count == 0:
                return jsonify({'error': 'User not found'}), 404

            return jsonify({'message': 'Settings saved successfully'})
        except Exception as e:
            logger.error(f"Error saving settings: {str(e)}")
            return jsonify({'error': 'Failed to save settings'}), 500

    @bp.route('/api/autopricing-settings', methods=['GET'])
    @login_required
    def get_autopricing_settings():
        try:
            # Get user from MongoDB
            user_data = users_collection.find_one({"username": current_user.username})
            if user_data:
                # Get custom stepping from user document
                custom_stepping = user_data.get('customStepping', {})

                # Construct settings object
                settings = {
                    "lp_percent": custom_stepping.get("lp", 80),
                    "mp_percent": custom_stepping.get("mp", 70),
                    "hp_percent": custom_stepping.get("hp", 65),
                    "dm_percent": custom_stepping.get("dm", 50),
                    "nm_percent": custom_stepping.get("nm", 100),
                    "min_price": user_data.get('minPrice', 0.50),
                    "use_skuid_pricing": user_data.get('use_skuid_pricing', False),
                    "price_rounding_enabled": user_data.get('price_rounding_enabled', False),
                    "price_rounding_thresholds": user_data.get('price_rounding_thresholds', [49, 99])
                }
                return jsonify(settings)
            else:
                return jsonify({'error': 'User settings not found'}), 404
        except Exception as e:
            logger.error(f"Error getting autopricing settings: {str(e)}")
            return jsonify({'error': 'Failed to get autopricing settings'}), 500

    @bp.route('/api/price-comparison-settings', methods=['GET', 'POST'])
    @login_required
    def manage_price_comparison_settings():

        if request.method == 'GET':
            try:
                # Use PyMongo to get raw user data
                user_data = users_collection.find_one({"username": current_user.username})
                if not user_data:
                    return jsonify({'error': 'User not found'}), 404

                # Get settings directly from MongoDB document
                settings = {
                    'use_highest_price': user_data.get('use_highest_price', False),
                    'price_comparison_pairs': user_data.get('price_comparison_pairs', [['lowPrice', 'marketPrice']]),
                    'price_modifiers': user_data.get('price_modifiers', {}),
                    'available_prices': ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']
                }

                logger.info(f"Retrieved settings for {current_user.username}: {settings}")
                return jsonify(settings)
            except Exception as e:
                logger.error(f"Error getting price comparison settings: {str(e)}")
                return jsonify({'error': 'Failed to get price comparison settings'}), 500

        elif request.method == 'POST':
            try:
                data = request.json
                use_highest_price = data.get('use_highest_price', False)
                price_comparison_pairs = data.get('price_comparison_pairs', [['lowPrice', 'marketPrice']])
                price_modifiers = data.get('price_modifiers', {})

                # Validate price comparison pairs
                allowed_prices = {'lowPrice', 'marketPrice', 'midPrice', 'highPrice'}
                if not all(len(pair) == 2 and all(p in allowed_prices for p in pair)
                          for pair in price_comparison_pairs):
                    return jsonify({'error': 'Invalid price comparison pairs'}), 400

                # Validate price modifiers
                if not all(key in allowed_prices and isinstance(val, (int, float)) and val >= 0
                          for key, val in price_modifiers.items()):
                    return jsonify({'error': 'Invalid price modifiers'}), 400

                # Ensure at least one pair exists
                if not price_comparison_pairs:
                    price_comparison_pairs = [['lowPrice', 'marketPrice']]

                # Update settings directly in MongoDB
                result = users_collection.update_one(
                    {"username": current_user.username},
                    {"$set": {
                        "use_highest_price": use_highest_price,
                        "price_comparison_pairs": price_comparison_pairs,
                        "price_modifiers": price_modifiers
                    }}
                )

                if result.modified_count == 0:
                    # Try to insert if update failed
                    users_collection.update_one(
                        {"username": current_user.username},
                        {"$setOnInsert": {
                            "use_highest_price": use_highest_price,
                            "price_comparison_pairs": price_comparison_pairs,
                            "price_modifiers": price_modifiers
                        }},
                        upsert=True
                    )

                settings = {
                    'use_highest_price': use_highest_price,
                    'price_comparison_pairs': price_comparison_pairs,
                    'price_modifiers': price_modifiers
                }
                logger.info(f"Updated settings for {current_user.username}: {settings}")

                return jsonify({
                    'message': 'Price comparison settings updated successfully',
                    'settings': settings
                })
            except Exception as e:
                logger.error(f"Error saving price comparison settings: {str(e)}")
                return jsonify({'error': 'Failed to save price comparison settings'}), 500

    @bp.route('/api/advanced-pricing-rules', methods=['GET', 'POST', 'DELETE'])
    @login_required
    def manage_advanced_pricing_rules():

        if request.method == 'GET':
            try:
                user_data = users_collection.find_one({"username": current_user.username})
                if not user_data:
                    return jsonify({"error": "User not found"}), 404

                # Get vendors, product types, and expansions from existing rules
                rules = user_data.get("advancedPricingRules", {})
                vendors = set()
                product_types = set()
                expansions = set()

                for key in rules.keys():
                    parts = key.split('_')
                    if len(parts) >= 3:
                        vendors.add(parts[0])
                        product_types.add(parts[1])
                        expansions.add('_'.join(parts[2:]))

                return jsonify({
                    "rules": [
                        {
                            "id": key,
                            "vendor": key.split('_')[0],
                            "productType": key.split('_')[1],
                            "expansion": '_'.join(key.split('_')[2:]),
                            **{k: v for k, v in value.items()}
                        }
                        for key, value in rules.items()
                    ],
                    "vendors": sorted(list(vendors)),
                    "productTypes": sorted(list(product_types)),
                    "expansions": sorted(list(expansions))
                })
            except Exception as e:
                logger.error(f"Error getting advanced pricing rules: {str(e)}")
                return jsonify({"error": "Failed to get advanced pricing rules"}), 500

        elif request.method == 'POST':
            try:
                data = request.json
                vendor = data.get('vendor')
                product_type = data.get('productType')
                expansion = data.get('expansion')

                if not all([vendor, product_type, expansion]):
                    return jsonify({"error": "Missing required fields"}), 400

                rule_key = f"{vendor}_{product_type}_{expansion}"
                rule_data = {
                    "nm": float(data.get('nm_percent', 100)),
                    "lp": float(data.get('lp_percent', 80)),
                    "mp": float(data.get('mp_percent', 70)),
                    "hp": float(data.get('hp_percent', 65)),
                    "dm": float(data.get('dm_percent', 50))
                }

                result = users_collection.update_one(
                    {"username": current_user.username},
                    {"$set": {f"advancedPricingRules.{rule_key}": rule_data}}
                )

                if result.modified_count == 0:
                    return jsonify({"error": "No changes made"}), 400
                return jsonify({"message": "Advanced pricing rule saved successfully"})
            except Exception as e:
                logger.error(f"Error saving advanced pricing rule: {str(e)}")
                return jsonify({"error": "Failed to save advanced pricing rule"}), 500

        elif request.method == 'DELETE':
            try:
                rule_id = request.args.get('id')
                if not rule_id:
                    return jsonify({"error": "Rule ID is required"}), 400

                result = users_collection.update_one(
                    {"username": current_user.username},
                    {"$unset": {f"advancedPricingRules.{rule_id}": ""}}
                )

                if result.modified_count == 0:
                    return jsonify({"error": "Rule not found"}), 404
                return jsonify({"message": "Advanced pricing rule deleted successfully"})
            except Exception as e:
                logger.error(f"Error deleting advanced pricing rule: {str(e)}")
                return jsonify({"error": "Failed to delete advanced pricing rule"}), 500

    @bp.route('/api/product-types/<vendor>', methods=['GET'])
    @login_required
    def get_product_types(vendor):
        try:

            user_data = users_collection.find_one({"username": current_user.username})
            if not user_data:
                return jsonify({"error": "User not found"}), 404

            rules = user_data.get("advancedPricingRules", {})
            product_types = set()

            for key in rules.keys():
                parts = key.split('_')
                if len(parts) >= 2 and parts[0] == vendor:
                    product_types.add(parts[1])

            return jsonify({"productTypes": sorted(list(product_types))})
        except Exception as e:
            logger.error(f"Error getting product types: {str(e)}")
            return jsonify({"error": "Failed to get product types"}), 500

    @bp.route('/api/expansions/<vendor>/<product_type>', methods=['GET'])
    @login_required
    def get_expansions(vendor, product_type):
        try:

            user_data = users_collection.find_one({"username": current_user.username})
            if not user_data:
                return jsonify({"error": "User not found"}), 404

            rules = user_data.get("advancedPricingRules", {})
            expansions = set()

            for key in rules.keys():
                parts = key.split('_')
                if len(parts) >= 3 and parts[0] == vendor and parts[1] == product_type:
                    expansions.add('_'.join(parts[2:]))

            return jsonify({"expansions": sorted(list(expansions))})
        except Exception as e:
            logger.error(f"Error getting expansions: {str(e)}")
            return jsonify({"error": "Failed to get expansions"}), 500

    @bp.route('/api/price-preference', methods=['GET', 'POST'])
    @login_required
    def manage_price_preference():
        try:
            if request.method == 'GET':
                # Get user from MongoDB
                user_data = users_collection.find_one({"username": current_user.username})
                if not user_data:
                    return jsonify({'error': 'User not found'}), 404

                # Get price preference from user document
                price_preference = user_data.get('price_preference_order',
                                           ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])

                return jsonify({
                    'price_preference': price_preference,
                    'available_prices': ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']
                })

            elif request.method == 'POST':
                data = request.json
                price_preference = data.get('price_preference')

                if not price_preference or not isinstance(price_preference, list):
                    return jsonify({'error': 'Invalid price preference order'}), 400

                allowed_prices = ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']
                if not all(price in allowed_prices for price in price_preference):
                    return jsonify({'error': 'Invalid price types'}), 400

                # Update user document in MongoDB
                result = users_collection.update_one(
                    {"username": current_user.username},
                    {"$set": {"price_preference_order": price_preference}}
                )

                if result.modified_count == 0 and result.matched_count == 0:
                    return jsonify({'error': 'User not found'}), 404

                return jsonify({
                    'message': 'Price preference updated successfully',
                    'price_preference': price_preference
                })
        except Exception as e:
            logger.error(f"Error managing price preference: {str(e)}")
            return jsonify({'error': 'Failed to manage price preference'}), 500
