from flask import jsonify, request
from flask_login import login_required, current_user
import logging
import requests
from models.user_model import User
from models.shproducts_model import ShProducts
from pymongo import MongoClient
from utils.pricing_utils import calculate_variant_price
from .utils import get_low_price, get_exchange_rate

logger = logging.getLogger(__name__)

# MongoDB connection
client = MongoClient("mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin")
db = client.test
catalog_collection = db['catalog']
prices_collection = db['prices']
tcgplayer_key_collection = db['tcgplayerKey']

def create_inventory_routes(bp):
    @bp.route('/api/autopricing-reprice-inventory', methods=['POST'])
    @login_required
    def autopricing_reprice_inventory():
        data = request.json
        product_type = data.get('productType')
        if not product_type:
            return jsonify({'error': 'Product type is required'}), 400

        try:
            user = User.objects(username=current_user.username).first()
            if not user:
                return jsonify({'error': 'User settings not found'}), 404

            custom_stepping = getattr(user, 'customStepping', {})
            default_settings = {
                "nm": custom_stepping.get("nm", 100),
                "lp": custom_stepping.get("lp", 80),
                "mp": custom_stepping.get("mp", 70),
                "hp": custom_stepping.get("hp", 65),
                "dm": custom_stepping.get("dm", 50),
                "minPrice": getattr(user, 'minPrice', 0.50),
                "use_highest_price": getattr(user, 'use_highest_price', False),
                "price_comparison_pairs": getattr(user, 'price_comparison_pairs', []),
                "price_modifiers": getattr(user, 'price_modifiers', {})
            }
            
            user_currency = getattr(user, 'currency', 'USD')
            exchange_rate = get_exchange_rate('USD', user_currency)

            price_preference_order = getattr(user, 'price_preference_order', 
                ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])

            products = ShProducts.objects(
                username=current_user.username,
                product_type=product_type
            )
            price_changes = []
            products_to_push = []

            for product in products:
                # Get appropriate pricing rules (advanced or default)
                settings = user.get_pricing_rules(
                    product.vendor,
                    product.product_type,
                    product.expansionName
                )
                
                for variant in product.variants:
                    pricing_data, source = get_low_price(variant, product.to_mongo(), user, tcgplayer_key_collection, prices_collection)
                    if pricing_data is None:
                        continue

                    # For SKU ID pricing, use the price directly
                    if getattr(user, 'use_skuid_pricing', False) and source == 'tcgplayer_sku':
                        new_price = max(pricing_data.get('lowestListingPrice', 0), default_settings['minPrice'])
                        is_missing = False
                    else:
                        # Use calculate_variant_price for condition-based pricing
                        new_price, is_missing = calculate_variant_price(
                            variant=variant,
                            pricing_data=pricing_data,
                            custom_stepping=settings,
                            min_price=default_settings['minPrice'],
                            user_currency=user_currency,
                            exchange_rate=exchange_rate,
                            price_preference_order=price_preference_order,
                            game_minimum_prices=getattr(user, 'game_minimum_prices', {}),
                            game_name=product.product_type,
                            product_id=product.get('productId'),
                            catalog_collection=catalog_collection,
                            use_highest_price=default_settings['use_highest_price'],
                            price_comparison_pairs=default_settings['price_comparison_pairs'],
                            price_modifiers=default_settings['price_modifiers']
                        )
                    
                    if is_missing:
                        continue
                    
                    old_price = float(variant['price'])
                    if new_price != old_price:
                        variant['price'] = str(new_price)
                        condition = variant['option1'].split()[0].lower()
                        key = f"{product.vendor}_{product.product_type}_{product.expansionName}"
                        price_changes.append({
                            "name": product.title,
                            "variant": variant['title'],
                            "condition": condition.upper(),
                            "old_price": old_price,
                            "new_price": new_price,
                            "percentage": settings.get(condition, 100) if not user.use_skuid_pricing else 'SKU',
                            "currency": user_currency,
                            "price_preference_order": price_preference_order,
                            "using_advanced_pricing": product.uses_advanced_pricing,
                            "using_skuid_pricing": user.use_skuid_pricing
                        })

                product.save()
                products_to_push.append(product)

            shopify_store_name = user.shopifyStoreName
            shopify_access_token = user.shopifyAccessToken
            headers = {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": shopify_access_token
            }

            pushed_count = 0
            for product in products_to_push:
                try:
                    shopify_product_id = product.id
                    if not shopify_product_id:
                        logger.error(f"No Shopify ID found for product {product.title}")
                        continue

                    url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_product_id}.json"
                    product_data = {
                        "product": {
                            "id": shopify_product_id,
                            "variants": [
                                {
                                    "id": variant['id'],
                                    "price": variant['price']
                                } for variant in product.variants
                            ]
                        }
                    }

                    response = requests.put(url, headers=headers, json=product_data)
                    response.raise_for_status()
                    pushed_count += 1
                except Exception as e:
                    logger.error(f"Error pushing product {product.title} to Shopify: {str(e)}")

            return jsonify({
                "message": f"Inventory repriced successfully. {len(price_changes)} prices changed. {pushed_count} products pushed to Shopify.", 
                "priceChanges": price_changes,
                "totalChanged": len(price_changes),
                "totalPushed": pushed_count,
                "settings": {
                    "stepping": default_settings,
                    "minPrice": default_settings['minPrice'],
                    "currency": user_currency,
                    "price_preference_order": price_preference_order,
                    "use_skuid_pricing": getattr(user, 'use_skuid_pricing', False)
                }
            })
            
        except Exception as e:
            logger.error(f"Error repricing inventory: {str(e)}")
            return jsonify({'error': 'Failed to reprice inventory'}), 500
