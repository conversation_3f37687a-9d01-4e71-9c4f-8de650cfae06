// Till Closing History Functions
function viewTillClosingHistory(tillId, location) {
    // Set the till ID and location name in the modal
    document.getElementById('historyTillId').value = tillId;
    document.getElementById('historyTillLocationName').textContent = location;
    
    // Fetch the closing history for this till
    fetchTillClosingHistory(tillId);
    
    // Show the modal
    $('#tillClosingHistoryModal').modal('show');
}

function closeTillClosingHistoryModal() {
    $('#tillClosingHistoryModal').modal('hide');
}

function fetchTillClosingHistory(tillId) {
    // Clear the table body
    const tableBody = document.getElementById('closingHistoryTableBody');
    tableBody.innerHTML = '<tr><td colspan="8" class="text-center">Loading closing history...</td></tr>';
    
    // Fetch the closing history from the server
    fetch(`/pos/get_till_closings?till_id=${tillId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTillClosingHistory(data.closings);
            } else {
                tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">${data.message || 'Error fetching closing history'}</td></tr>`;
            }
        })
        .catch(error => {
            console.error('Error fetching till closing history:', error);
            tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">Error fetching closing history: ${error.message}</td></tr>`;
        });
}

function displayTillClosingHistory(closings) {
    const tableBody = document.getElementById('closingHistoryTableBody');
    
    if (!closings || closings.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="8" class="text-center">No closing history found for this till.</td></tr>';
        return;
    }
    
    // Sort closings by date (newest first)
    closings.sort((a, b) => new Date(b.closing_time) - new Date(a.closing_time));
    
    // Clear the table body
    tableBody.innerHTML = '';
    
    // Add each closing to the table
    closings.forEach(closing => {
        const row = document.createElement('tr');
        
        // Format the date
        const closingDate = new Date(closing.closing_time);
        const formattedDate = closingDate.toLocaleDateString() + ' ' + closingDate.toLocaleTimeString();
        
        // Format the discrepancy with color
        const discrepancy = parseFloat(closing.cash_discrepancy);
        const discrepancyClass = discrepancy < 0 ? 'text-danger' : (discrepancy > 0 ? 'text-success' : '');
        
        row.innerHTML = `
            <td>${formattedDate}</td>
            <td>${closing.z_report_number || 'N/A'}</td>
            <td>${closing.employee_name || 'Unknown'}</td>
            <td>$${parseFloat(closing.starting_float).toFixed(2)}</td>
            <td>$${parseFloat(closing.ending_float).toFixed(2)}</td>
            <td class="${discrepancyClass}">$${discrepancy.toFixed(2)}</td>
            <td>$${parseFloat(closing.total_sales).toFixed(2)}</td>
            <td>
                <button class="btn btn-sm btn-info" onclick="viewClosingReport('${closing.id}')">
                    <i class="fas fa-file-alt"></i> View Report
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
}

function viewClosingReport(closingId) {
    // Fetch the closing report from the server
    fetch(`/pos/get_till_closing_report/${closingId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Set the report content
                document.getElementById('closingReportContent').innerHTML = data.report.html_report;
                
                // Update the modal title with the Z-report number
                const reportTitle = document.getElementById('tillClosingReportModalLabel');
                reportTitle.innerHTML = `<i class="fas fa-file-alt pos-icon"></i>Z-Report #${data.closing.z_report_number || 'N/A'}`;
                
                // Show the report modal
                $('#tillClosingReportModal').modal('show');
            } else {
                alert(data.message || 'Error fetching closing report');
            }
        })
        .catch(error => {
            console.error('Error fetching closing report:', error);
            alert(`Error fetching closing report: ${error.message}`);
        });
}

function closeTillClosingReportModal() {
    $('#tillClosingReportModal').modal('hide');
}

function printClosingReport() {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    
    // Get the report content
    const reportContent = document.getElementById('closingReportContent').innerHTML;
    
    // Write the report content to the new window
    printWindow.document.write(`
        <html>
        <head>
            <title>Till Closing Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    padding: 20px;
                }
                .till-closing-report {
                    max-width: 800px;
                    margin: 0 auto;
                }
                h2, h3, h4 {
                    color: #2c3e50;
                }
                .financial-summary, .transaction-summary, .notes {
                    margin-top: 20px;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
            </style>
        </head>
        <body>
            ${reportContent}
        </body>
        </html>
    `);
    
    // Close the document for writing
    printWindow.document.close();
    
    // Print the window
    printWindow.print();
}

// Add event listeners when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Set up the actualCash input to calculate discrepancy
    const actualCashInput = document.getElementById('actualCash');
    if (actualCashInput) {
        actualCashInput.addEventListener('input', function() {
            calculateDiscrepancy();
        });
    }
    
    // Set up the create till form
    const createTillForm = document.getElementById('createTillForm');
    if (createTillForm) {
        createTillForm.addEventListener('submit', function(event) {
            event.preventDefault();
            createTill();
        });
    }
    
    // Set up the create currency form
    const createCurrencyForm = document.getElementById('createCurrencyForm');
    if (createCurrencyForm) {
        createCurrencyForm.addEventListener('submit', function(event) {
            event.preventDefault();
            createCurrency();
        });
    }
    
    // Initialize other functions
    setupCloseTillFunctions();
    setupReopenTillFunctions();
    setupEmployeeManagement();
    
    // Load Shopify locations for the create till form
    loadShopifyLocations();
    
    // Load existing currencies
    loadCurrencies();
});

// Function to create a new till
function createTill() {
    console.log('Creating till...');
    
    // Get form values
    const location = document.getElementById('location').value;
    const startingFloat = document.getElementById('startingFloat').value;
    const taxRate = document.getElementById('taxRate').value;
    const taxInclusive = document.getElementById('taxInclusive').value === 'true';
    const shopifyLocationId = document.getElementById('shopifyLocation').value;

    // Get Merchant Match card machine settings
    const tpn = document.getElementById('tpn').value.trim();
    const registerId = document.getElementById('registerId').value.trim();
    const authKey = document.getElementById('authKey').value.trim();

    // Get shopify location name safely
    let shopifyLocationName = '';
    const shopifyLocationSelect = document.getElementById('shopifyLocation');
    if (shopifyLocationSelect && shopifyLocationSelect.selectedIndex >= 0) {
        shopifyLocationName = shopifyLocationSelect.options[shopifyLocationSelect.selectedIndex].text;
    }

    console.log('Form values:', {
        location,
        startingFloat,
        taxRate,
        taxInclusive,
        shopifyLocationId,
        shopifyLocationName,
        tpn,
        registerId,
        authKey
    });
    
    // Validate required fields
    if (!location || !startingFloat || !taxRate) {
        console.log('Validation failed: Missing required fields');
        showWarningAlert('Please fill in all required fields');
        return;
    }
    
    // Prepare request data
    const requestData = {
        location: location,
        starting_float: startingFloat,
        tax_rate: taxRate,
        tax_inclusive: taxInclusive,
        shopify_location_id: shopifyLocationId,
        shopify_location_name: shopifyLocationName,
        tpn: tpn || null,
        register_id: registerId || null,
        auth_key: authKey || null
    };
    
    console.log('Sending request to create till:', requestData);
    
    // Send the request to create the till
    fetch('/pos/create_till', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('Received response:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        // Check if the response contains an id (indicating success)
        if (data.id) {
            console.log('Till created successfully with ID:', data.id);
            showSuccessAlert('Till created successfully');
            // Clear the form
            document.getElementById('createTillForm').reset();
            // Reload the page to show the new till after a short delay
            console.log('Reloading page in 1.5 seconds...');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else if (data.error || data.message) {
            // Handle error response
            console.error('Error from server:', data.error || data.message);
            showErrorAlert(data.error || data.message || 'Error creating till');
        } else {
            // Fallback for unexpected response format
            console.error('Unexpected server response:', data);
            showErrorAlert('Unexpected server response. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error creating till:', error);
        showErrorAlert(`Error creating till: ${error.message}`);
    });
}

// Function to load Shopify locations
function loadShopifyLocations() {
    const shopifyLocationSelect = document.getElementById('shopifyLocation');
    if (!shopifyLocationSelect) return;
    
    fetch('/pos/get_shopify_locations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate the select dropdown
                data.locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    shopifyLocationSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error fetching Shopify locations:', error);
        });
}

// Function to create a new currency
function createCurrency() {
    const name = document.getElementById('currencyName').value;
    const rate = document.getElementById('currencyRate').value;
    
    if (!name || !rate) {
        alert('Please fill in all required fields');
        return;
    }
    
    // Send the request to create the currency
    fetch('/pos/create_currency', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: name,
            rate: rate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessAlert('Currency created successfully');
            // Clear the form
            document.getElementById('createCurrencyForm').reset();
            // Reload the currencies list
            loadCurrencies();
        } else {
            showErrorAlert(data.message || 'Error creating currency');
        }
    })
    .catch(error => {
        console.error('Error creating currency:', error);
        showErrorAlert(`Error creating currency: ${error.message}`);
    });
}

// Function to load existing currencies
function loadCurrencies() {
    const currenciesList = document.getElementById('currenciesList');
    if (!currenciesList) return;
    
    currenciesList.innerHTML = '<p class="text-center">Loading currencies...</p>';
    
    fetch('/pos/get_currencies')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayCurrencies(data.currencies);
            } else {
                currenciesList.innerHTML = `<p class="text-center text-danger">${data.message || 'Error loading currencies'}</p>`;
            }
        })
        .catch(error => {
            console.error('Error loading currencies:', error);
            currenciesList.innerHTML = `<p class="text-center text-danger">Error loading currencies: ${error.message}</p>`;
        });
}

// Function to display currencies
function displayCurrencies(currencies) {
    const currenciesList = document.getElementById('currenciesList');
    
    if (!currencies || currencies.length === 0) {
        currenciesList.innerHTML = '<p class="text-center">No currencies found</p>';
        return;
    }
    
    // Clear the list
    currenciesList.innerHTML = '';
    
    // Add each currency to the list
    currencies.forEach(currency => {
        const li = document.createElement('li');
        li.className = 'list-group-item pos-list-item pos-dark-list-item';
        
        li.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h5 class="mb-0">${currency.name}</h5>
                <span class="pos-badge">1 ${currency.name} = $${parseFloat(currency.rate).toFixed(4)} USD</span>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary pos-btn pos-btn-primary mr-2" onclick="editCurrency('${currency.id}', '${currency.name}', ${currency.rate})">
                    <i class="fas fa-edit pos-icon"></i>Edit
                </button>
                <button class="btn btn-danger pos-btn pos-btn-danger" onclick="deleteCurrency('${currency.id}', '${currency.name}')">
                    <i class="fas fa-trash pos-icon"></i>Delete
                </button>
            </div>
        `;
        
        currenciesList.appendChild(li);
    });
}

// Cash Count Variables
let cashDenominations = {
    currency: 'USD',
    notes: [
        { value: 100, count: 0 },
        { value: 50, count: 0 },
        { value: 20, count: 0 },
        { value: 10, count: 0 },
        { value: 5, count: 0 },
        { value: 1, count: 0 }
    ],
    coins: [
        { value: 1.00, count: 0 },
        { value: 0.50, count: 0 },
        { value: 0.25, count: 0 },
        { value: 0.10, count: 0 },
        { value: 0.05, count: 0 },
        { value: 0.01, count: 0 }
    ]
};

// Setup functions for closing a till
function setupCloseTillFunctions() {
    // Function to close a till (show the modal)
    window.closeTill = function(tillId, location, startingFloat) {
        document.getElementById('closeTillId').value = tillId;
        document.getElementById('closeTillLocationName').textContent = location;
        document.getElementById('startingFloat').value = startingFloat;
        
        // Fetch the till transactions
        fetchTillTransactions(tillId);
        
        // Show the modal
        $('#closeTillModal').modal('show');
    };
    
    // Function to close the modal
    window.closeCloseTillModal = function() {
        $('#closeTillModal').modal('hide');
    };
}

// Function to open the cash count modal
window.openCashCountModal = function() {
    // Get the current currency from the system
    fetch('/pos/get_system_currency')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Set the currency
                cashDenominations.currency = data.currency;
                
                // Update the denominations based on the currency
                updateDenominationsForCurrency(data.currency);
                
                // Populate the modal with the denominations
                populateCashCountModal();
                
                // Show the modal
                $('#cashCountModal').modal('show');
            } else {
                alert(data.message || 'Error getting system currency');
            }
        })
        .catch(error => {
            console.error('Error getting system currency:', error);
            
            // Default to USD if there's an error
            updateDenominationsForCurrency('USD');
            
            // Populate the modal with the denominations
            populateCashCountModal();
            
            // Show the modal
            $('#cashCountModal').modal('show');
        });
}

// Function to update denominations based on currency
function updateDenominationsForCurrency(currency) {
    // Default to USD
    let notes = [
        { value: 100, count: 0 },
        { value: 50, count: 0 },
        { value: 20, count: 0 },
        { value: 10, count: 0 },
        { value: 5, count: 0 },
        { value: 1, count: 0 }
    ];
    
    let coins = [
        { value: 1.00, count: 0 },
        { value: 0.50, count: 0 },
        { value: 0.25, count: 0 },
        { value: 0.10, count: 0 },
        { value: 0.05, count: 0 },
        { value: 0.01, count: 0 }
    ];
    
    // Update based on currency
    switch (currency) {
        case 'GBP':
            // GBP has £50, £20, £10, and £5 notes (no £100 notes)
            notes = [
                { value: 50, count: 0 },
                { value: 20, count: 0 },
                { value: 10, count: 0 },
                { value: 5, count: 0 }
            ];
            coins = [
                { value: 2.00, count: 0 },
                { value: 1.00, count: 0 },
                { value: 0.50, count: 0 },
                { value: 0.20, count: 0 },
                { value: 0.10, count: 0 },
                { value: 0.05, count: 0 },
                { value: 0.02, count: 0 },
                { value: 0.01, count: 0 }
            ];
            break;
        case 'EUR':
            notes = [
                { value: 500, count: 0 },
                { value: 200, count: 0 },
                { value: 100, count: 0 },
                { value: 50, count: 0 },
                { value: 20, count: 0 },
                { value: 10, count: 0 },
                { value: 5, count: 0 }
            ];
            coins = [
                { value: 2.00, count: 0 },
                { value: 1.00, count: 0 },
                { value: 0.50, count: 0 },
                { value: 0.20, count: 0 },
                { value: 0.10, count: 0 },
                { value: 0.05, count: 0 },
                { value: 0.02, count: 0 },
                { value: 0.01, count: 0 }
            ];
            break;
        // Add more currencies as needed
    }
    
    // Update the cash denominations
    cashDenominations.notes = notes;
    cashDenominations.coins = coins;
}

// Function to populate the cash count modal
function populateCashCountModal() {
    const notesDenominations = document.getElementById('notesDenominations');
    const coinsDenominations = document.getElementById('coinsDenominations');
    
    // Clear the containers
    notesDenominations.innerHTML = '';
    coinsDenominations.innerHTML = '';
    
    // Add notes denominations
    cashDenominations.notes.forEach((note, index) => {
        const denominationRow = document.createElement('div');
        denominationRow.className = 'form-group row align-items-center';
        
        denominationRow.innerHTML = `
            <label class="col-sm-4 col-form-label">${getCurrencySymbol(cashDenominations.currency)}${note.value}</label>
            <div class="col-sm-4">
                <input type="number" min="0" class="form-control bg-secondary text-white pos-form-control" id="note-${index}" value="${note.count}" onchange="updateNoteCount(${index}, this.value)">
            </div>
            <div class="col-sm-4">
                <span class="text-success">${getCurrencySymbol(cashDenominations.currency)}${(note.value * note.count).toFixed(2)}</span>
            </div>
        `;
        
        notesDenominations.appendChild(denominationRow);
    });
    
    // Add coins denominations
    cashDenominations.coins.forEach((coin, index) => {
        const denominationRow = document.createElement('div');
        denominationRow.className = 'form-group row align-items-center';
        
        denominationRow.innerHTML = `
            <label class="col-sm-4 col-form-label">${getCurrencySymbol(cashDenominations.currency)}${coin.value.toFixed(2)}</label>
            <div class="col-sm-4">
                <input type="number" min="0" class="form-control bg-secondary text-white pos-form-control" id="coin-${index}" value="${coin.count}" onchange="updateCoinCount(${index}, this.value)">
            </div>
            <div class="col-sm-4">
                <span class="text-success">${getCurrencySymbol(cashDenominations.currency)}${(coin.value * coin.count).toFixed(2)}</span>
            </div>
        `;
        
        coinsDenominations.appendChild(denominationRow);
    });
    
    // Update the total
    updateTotalCashCount();
}

// Function to get currency symbol
function getCurrencySymbol(currency) {
    switch (currency) {
        case 'USD':
            return '$';
        case 'GBP':
            return '£';
        case 'EUR':
            return '€';
        default:
            return '$';
    }
}

// Function to update note count
function updateNoteCount(index, count) {
    cashDenominations.notes[index].count = parseInt(count) || 0;
    updateTotalCashCount();
    
    // Update the value display
    const noteValue = cashDenominations.notes[index].value * cashDenominations.notes[index].count;
    const noteValueElement = document.querySelector(`#note-${index}`).parentNode.nextElementSibling.querySelector('span');
    noteValueElement.textContent = `${getCurrencySymbol(cashDenominations.currency)}${noteValue.toFixed(2)}`;
}

// Function to update coin count
function updateCoinCount(index, count) {
    cashDenominations.coins[index].count = parseInt(count) || 0;
    updateTotalCashCount();
    
    // Update the value display
    const coinValue = cashDenominations.coins[index].value * cashDenominations.coins[index].count;
    const coinValueElement = document.querySelector(`#coin-${index}`).parentNode.nextElementSibling.querySelector('span');
    coinValueElement.textContent = `${getCurrencySymbol(cashDenominations.currency)}${coinValue.toFixed(2)}`;
}

// Function to update the total cash count
function updateTotalCashCount() {
    let total = 0;
    
    // Add up notes
    cashDenominations.notes.forEach(note => {
        total += note.value * note.count;
    });
    
    // Add up coins
    cashDenominations.coins.forEach(coin => {
        total += coin.value * coin.count;
    });
    
    // Update the total display
    document.getElementById('totalCashCount').textContent = `${getCurrencySymbol(cashDenominations.currency)}${total.toFixed(2)}`;
}

// Function to close the cash count modal
function closeCashCountModal() {
    $('#cashCountModal').modal('hide');
}

// Function to save the cash count
function saveCashCount() {
    let total = 0;
    
    // Add up notes
    cashDenominations.notes.forEach(note => {
        total += note.value * note.count;
    });
    
    // Add up coins
    cashDenominations.coins.forEach(coin => {
        total += coin.value * coin.count;
    });
    
    // Update the actual cash field
    document.getElementById('actualCash').value = total.toFixed(2);
    
    // Calculate the discrepancy
    calculateDiscrepancy();
    
    // Close the modal
    closeCashCountModal();
}

// Function to confirm closing the till
window.confirmCloseTill = function() {
    const tillId = document.getElementById('closeTillId').value;
    const actualCash = document.getElementById('actualCash').value;
    const notes = document.getElementById('closingNotes').value;
    
    if (!actualCash) {
        showWarningAlert('Please enter the actual cash count');
        return;
    }
    
    // Confirm with the user that they want to close the till
    showConfirmDialog('Are you sure you want to close this till? This will save all transactions with today\'s date and restart the till fresh.', 'Close Till', 'Cancel').then(confirmed => {
        if (confirmed) {
            // Prepare the cash count data
            const cashCountData = {
                currency: cashDenominations.currency,
                notes: cashDenominations.notes,
                coins: cashDenominations.coins,
                total: parseFloat(actualCash)
            };
            
            // Send the request to close the till
            fetch('/pos/close_till', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    till_id: tillId,
                    actual_cash: actualCash,
                    notes: notes,
                    business_date: new Date().toISOString(), // Include today's date
                    cash_count: cashCountData // Include the cash count data
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessAlert(`Till closed successfully. Z-Report #${data.z_report_number}`);
                    // Close the modal
                    $('#closeTillModal').modal('hide');
                    // Reload the page to update the till status after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showErrorAlert(data.message || 'Error closing till');
                }
            })
            .catch(error => {
                console.error('Error closing till:', error);
                showErrorAlert(`Error closing till: ${error.message}`);
            });
        }
    });
};

// Setup functions for reopening a till
function setupReopenTillFunctions() {
    // Function to reopen a till (show the modal)
    window.reopenTill = function(tillId, location) {
        document.getElementById('reopenTillId').value = tillId;
        document.getElementById('reopenTillLocationName').textContent = location;
        
        // Fetch the till closing details to get the last ending float
        fetch(`/pos/get_till_closing/${tillId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Set the starting float to the last ending float
                    document.getElementById('reopenStartingFloat').value = data.closing_details.actual_cash;
                }
            })
            .catch(error => {
                console.error('Error fetching till closing details:', error);
            });
        
        // Show the modal
        $('#reopenTillModal').modal('show');
    };
    
    // Function to close the modal
    window.closeReopenTillModal = function() {
        $('#reopenTillModal').modal('hide');
    };
    
    // Function to confirm reopening the till
    window.confirmReopenTill = function() {
        const tillId = document.getElementById('reopenTillId').value;
        const startingFloat = document.getElementById('reopenStartingFloat').value;
        const notes = document.getElementById('reopenNotes').value;
        
        if (!startingFloat) {
            showWarningAlert('Please enter the starting float');
            return;
        }
        
        // Send the request to reopen the till
        fetch(`/pos/reopen_till/${tillId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                starting_float: startingFloat,
                notes: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessAlert('Till reopened successfully');
                // Close the modal
                $('#reopenTillModal').modal('hide');
                // Reload the page to update the till status after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showErrorAlert(data.message || 'Error reopening till');
            }
        })
        .catch(error => {
            console.error('Error reopening till:', error);
            showErrorAlert(`Error reopening till: ${error.message}`);
        });
    };
}

// Function to fetch till transactions
function fetchTillTransactions(tillId) {
    fetch(`/pos/get_till_transactions/${tillId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the cash and card transaction lists
                displayCashTransactions(data.cash_transactions, data.cash_total);
                displayCardTransactions(data.card_transactions, data.card_total);
                
                // Update the cash reconciliation fields
                document.getElementById('startingFloat').value = data.starting_float;
                document.getElementById('cashSales').value = data.cash_total;
                document.getElementById('expectedCash').value = data.expected_cash;
                
                // Set the actual cash to the expected cash by default
                document.getElementById('actualCash').value = data.expected_cash;
                
                // Calculate the discrepancy
                calculateDiscrepancy();
            } else {
                showErrorAlert(data.message || 'Error fetching till transactions');
            }
        })
        .catch(error => {
            console.error('Error fetching till transactions:', error);
            showErrorAlert(`Error fetching till transactions: ${error.message}`);
        });
}

// Function to display cash transactions
function displayCashTransactions(transactions, total) {
    const transactionsList = document.getElementById('cashTransactionsList');
    const transactionsTotal = document.getElementById('cashTransactionsTotal');
    const transactionsSummary = document.getElementById('cashTransactionsSummary');
    
    // Get the currency symbol
    const currencySymbol = getCurrencySymbol(cashDenominations.currency);
    
    // Update the total
    transactionsTotal.textContent = `${currencySymbol}${parseFloat(total).toFixed(2)}`;
    
    // Update the summary
    transactionsSummary.innerHTML = `
        <p><strong>Total Cash Sales:</strong> ${currencySymbol}${parseFloat(total).toFixed(2)}</p>
        <p><strong>Number of Transactions:</strong> ${transactions.length}</p>
    `;
    
    // If there are no transactions, show a message
    if (transactions.length === 0) {
        transactionsList.innerHTML = '<p class="text-center">No cash transactions found</p>';
        return;
    }
    
    // Create a list of transactions
    let html = '<ul class="list-group">';
    
    transactions.forEach(transaction => {
        html += `
            <li class="list-group-item bg-dark text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <span>$${parseFloat(transaction.amount).toFixed(2)}</span>
                    <small>${new Date(transaction.time).toLocaleTimeString()}</small>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-1">
                    <small>${transaction.employee_name || 'Unknown'}</small>
                    <small>Order ID: ${transaction.order_id || 'N/A'}</small>
                </div>
                ${transaction.items && transaction.items.length > 0 ? `
                <div class="mt-2">
                    <small><strong>Items:</strong></small>
                    <ul class="list-unstyled ml-3">
                        ${transaction.items.map(item => `
                            <li><small>${item.quantity || 1} x ${item.name || 'Item'} - $${parseFloat(item.price || 0).toFixed(2)}</small></li>
                        `).join('')}
                    </ul>
                </div>
                ` : ''}
            </li>
        `;
    });
    
    html += '</ul>';
    
    transactionsList.innerHTML = html;
    
    // Automatically show the transactions when there are any
    if (transactions.length > 0) {
        $('#cashTransactionsCollapse').collapse('show');
    }
}

// Function to display card transactions
function displayCardTransactions(transactions, total) {
    const transactionsList = document.getElementById('cardTransactionsList');
    const transactionsTotal = document.getElementById('cardTransactionsTotal');
    const transactionsSummary = document.getElementById('cardTransactionsSummary');
    
    // Get the currency symbol
    const currencySymbol = getCurrencySymbol(cashDenominations.currency);
    
    // Update the total
    transactionsTotal.textContent = `${currencySymbol}${parseFloat(total).toFixed(2)}`;
    
    // Update the summary
    transactionsSummary.innerHTML = `
        <p><strong>Total Card Sales:</strong> ${currencySymbol}${parseFloat(total).toFixed(2)}</p>
        <p><strong>Number of Transactions:</strong> ${transactions.length}</p>
    `;
    
    // If there are no transactions, show a message
    if (transactions.length === 0) {
        transactionsList.innerHTML = '<p class="text-center">No card transactions found</p>';
        return;
    }
    
    // Create a list of transactions
    let html = '<ul class="list-group">';
    
    transactions.forEach(transaction => {
        html += `
            <li class="list-group-item bg-dark text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <span>$${parseFloat(transaction.amount).toFixed(2)}</span>
                    <small>${new Date(transaction.time).toLocaleTimeString()}</small>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-1">
                    <small>${transaction.employee_name || 'Unknown'}</small>
                    <small>Order ID: ${transaction.order_id || 'N/A'}</small>
                </div>
                ${transaction.items && transaction.items.length > 0 ? `
                <div class="mt-2">
                    <small><strong>Items:</strong></small>
                    <ul class="list-unstyled ml-3">
                        ${transaction.items.map(item => `
                            <li><small>${item.quantity || 1} x ${item.name || 'Item'} - $${parseFloat(item.price || 0).toFixed(2)}</small></li>
                        `).join('')}
                    </ul>
                </div>
                ` : ''}
            </li>
        `;
    });
    
    html += '</ul>';
    
    transactionsList.innerHTML = html;
    
    // Automatically show the transactions when there are any
    if (transactions.length > 0) {
        $('#cardTransactionsCollapse').collapse('show');
    }
}

function calculateDiscrepancy() {
    const expectedCash = parseFloat(document.getElementById('expectedCash').value) || 0;
    const actualCash = parseFloat(document.getElementById('actualCash').value) || 0;
    const discrepancy = actualCash - expectedCash;
    
    document.getElementById('discrepancy').value = discrepancy.toFixed(2);
    
    // Add color to the discrepancy field based on the value
    const discrepancyField = document.getElementById('discrepancy');
    if (discrepancy < 0) {
        discrepancyField.classList.add('text-danger');
        discrepancyField.classList.remove('text-success');
    } else if (discrepancy > 0) {
        discrepancyField.classList.add('text-success');
        discrepancyField.classList.remove('text-danger');
    } else {
        discrepancyField.classList.remove('text-danger');
        discrepancyField.classList.remove('text-success');
    }
}

// Function to edit card machine settings removed as requested

// Function to delete a till
function deleteTill(tillId) {
    showConfirmDialog('Are you sure you want to delete this till? This action cannot be undone.', 'Delete', 'Cancel').then(confirmed => {
        if (confirmed) {
        // Send the request to delete the till
        fetch(`/pos/delete_till/${tillId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessAlert('Till deleted successfully');
                // Reload the page to update the till list after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showErrorAlert(data.message || 'Error deleting till');
            }
        })
        .catch(error => {
            console.error('Error deleting till:', error);
            showErrorAlert(`Error deleting till: ${error.message}`);
        });
        }
    });
}

// Function to edit till location
function editTillLocation(tillId, location) {
    // Set the till ID and location name in the modal
    document.getElementById('editTillId').value = tillId;
    document.getElementById('editTillLocationName').textContent = location;

    // Fetch current till data to populate the form
    fetch(`/pos/get_till/${tillId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const till = data.till;

                // Populate Merchant Match fields
                document.getElementById('editTpn').value = till.tpn || '';
                document.getElementById('editRegisterId').value = till.register_id || '';
                document.getElementById('editAuthKey').value = till.auth_key || '';

                // Set current Shopify location if available
                if (till.shopify_location_id) {
                    const editShopifyLocationSelect = document.getElementById('editShopifyLocation');
                    editShopifyLocationSelect.value = till.shopify_location_id;
                }
            }
        })
        .catch(error => {
            console.error('Error fetching till data:', error);
        });

    // Fetch Shopify locations
    fetch('/pos/get_shopify_locations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate the select dropdown
                const select = document.getElementById('editShopifyLocation');
                select.innerHTML = '<option value="">Select a Shopify location...</option>';

                data.locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    select.appendChild(option);
                });

                // Re-fetch till data to set the selected Shopify location after options are populated
                fetch(`/pos/get_till/${tillId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.till.shopify_location_id) {
                            select.value = data.till.shopify_location_id;
                        }
                    })
                    .catch(error => {
                        console.error('Error re-fetching till data:', error);
                    });
            }
        })
        .catch(error => {
            console.error('Error fetching Shopify locations:', error);
        });

    // Show the modal
    $('#editTillLocationModal').modal('show');
}

// Function to close the edit till location modal
function closeEditTillLocationModal() {
    $('#editTillLocationModal').modal('hide');
}

// Function to update till location
function updateTillLocation() {
    const tillId = document.getElementById('editTillId').value;
    const shopifyLocationId = document.getElementById('editShopifyLocation').value;
    const shopifyLocationName = document.getElementById('editShopifyLocation').options[document.getElementById('editShopifyLocation').selectedIndex].text;

    // Get Merchant Match card machine settings
    const tpn = document.getElementById('editTpn').value.trim();
    const registerId = document.getElementById('editRegisterId').value.trim();
    const authKey = document.getElementById('editAuthKey').value.trim();

    // Send the request to update the till location and card machine settings
    fetch(`/pos/update_till_location/${tillId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            shopify_location_id: shopifyLocationId,
            shopify_location_name: shopifyLocationName,
            tpn: tpn || null,
            register_id: registerId || null,
            auth_key: authKey || null
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
                showSuccessAlert('Till settings updated successfully');
                // Close the modal
                $('#editTillLocationModal').modal('hide');
                // Reload the page to update the till information after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showErrorAlert(data.message || 'Error updating till settings');
        }
    })
    .catch(error => {
            console.error('Error updating till settings:', error);
            showErrorAlert(`Error updating till settings: ${error.message}`);
    });
}

// Card machine related functions removed as requested

// Function to handle employee management
function setupEmployeeManagement() {
    // Load existing employees
    loadEmployees();
    
    // Add event listener for the create employee form
    const createEmployeeForm = document.getElementById('createEmployeeForm');
    if (createEmployeeForm) {
        createEmployeeForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const name = document.getElementById('employeeName').value;
            const pin = document.getElementById('employeePin').value;
            
            if (!name || !pin) {
                showWarningAlert('Please enter both name and PIN');
                return;
            }
            
            // Send the request to create the employee
            fetch('/pos/create_employee', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: name,
                    pin: pin
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessAlert('Employee created successfully');
                    // Clear the form
                    createEmployeeForm.reset();
                    // Reload the employees list
                    loadEmployees();
                } else {
                    showErrorAlert(data.message || 'Error creating employee');
                }
            })
            .catch(error => {
                console.error('Error creating employee:', error);
                showErrorAlert(`Error creating employee: ${error.message}`);
            });
        });
    }
}

// Function to load existing employees
function loadEmployees() {
    const employeesList = document.getElementById('employeesList');
    if (!employeesList) return;
    
    employeesList.innerHTML = '<p class="text-center">Loading employees...</p>';
    
    fetch('/pos/get_employees')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayEmployees(data.employees);
            } else {
                employeesList.innerHTML = `<p class="text-center text-danger">${data.message || 'Error loading employees'}</p>`;
            }
        })
        .catch(error => {
            console.error('Error loading employees:', error);
            employeesList.innerHTML = `<p class="text-center text-danger">Error loading employees: ${error.message}</p>`;
        });
}

// Function to display employees
function displayEmployees(employees) {
    const employeesList = document.getElementById('employeesList');
    
    if (!employees || employees.length === 0) {
        employeesList.innerHTML = '<p class="text-center">No employees found</p>';
        return;
    }
    
    // Clear the list
    employeesList.innerHTML = '';
    
    // Add each employee to the list
    employees.forEach(employee => {
        const li = document.createElement('li');
        li.className = 'list-group-item pos-list-item pos-dark-list-item';
        
        li.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h5 class="mb-0">${employee.name}</h5>
                <span class="pos-badge">PIN: ${employee.pin}</span>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary pos-btn pos-btn-primary mr-2" onclick="editEmployee('${employee.id}', '${employee.name}', '${employee.pin}')">
                    <i class="fas fa-edit pos-icon"></i>Edit
                </button>
                <button class="btn btn-danger pos-btn pos-btn-danger" onclick="deleteEmployee('${employee.id}', '${employee.name}')">
                    <i class="fas fa-trash pos-icon"></i>Delete
                </button>
            </div>
        `;
        
        employeesList.appendChild(li);
    });
}

// Function to edit an employee
function editEmployee(employeeId, name, pin) {
    // Set the employee ID and name in the modal
    document.getElementById('editEmployeeId').value = employeeId;
    document.getElementById('editEmployeeName').value = name;
    document.getElementById('editEmployeePin').value = pin;
    
    // Show the modal
    $('#editEmployeeModal').modal('show');
}

// Function to close the edit employee modal
function closeEditEmployeeModal() {
    $('#editEmployeeModal').modal('hide');
}

// Function to update an employee
function updateEmployee() {
    const employeeId = document.getElementById('editEmployeeId').value;
    const name = document.getElementById('editEmployeeName').value;
    const pin = document.getElementById('editEmployeePin').value;
    
    if (!name || !pin) {
        showWarningAlert('Please enter both name and PIN');
        return;
    }
    
    // Send the request to update the employee
    fetch(`/pos/update_employee/${employeeId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: name,
            pin: pin
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessAlert('Employee updated successfully');
            // Close the modal
            $('#editEmployeeModal').modal('hide');
            // Reload the employees list
            loadEmployees();
        } else {
            showErrorAlert(data.message || 'Error updating employee');
        }
    })
    .catch(error => {
        console.error('Error updating employee:', error);
        showErrorAlert(`Error updating employee: ${error.message}`);
    });
}

// Function to delete an employee
function deleteEmployee(employeeId, name) {
    showConfirmDialog(`Are you sure you want to delete employee "${name}"? This action cannot be undone.`, 'Delete', 'Cancel').then(confirmed => {
        if (confirmed) {
        // Send the request to delete the employee
        fetch(`/pos/delete_employee/${employeeId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessAlert('Employee deleted successfully');
                // Reload the employees list
                loadEmployees();
            } else {
                showErrorAlert(data.message || 'Error deleting employee');
            }
        })
        .catch(error => {
            console.error('Error deleting employee:', error);
            showErrorAlert(`Error deleting employee: ${error.message}`);
        });
        }
    });
}

// Function to edit a currency
function editCurrency(currencyId, name, rate) {
    // Create a modal dynamically if it doesn't exist
    if (!document.getElementById('editCurrencyModal')) {
        const modalHtml = `
            <div class="modal fade" id="editCurrencyModal" tabindex="-1" role="dialog" aria-labelledby="editCurrencyModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content bg-dark text-white">
                        <div class="modal-header border-secondary">
                            <h5 class="modal-title text-success" id="editCurrencyModalLabel">
                                <i class="fas fa-money-bill-alt pos-icon"></i>Edit Currency
                            </h5>
                            <button type="button" class="close text-white" onclick="closeEditCurrencyModal()" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="editCurrencyForm">
                                <input type="hidden" id="editCurrencyId">
                                <div class="form-group">
                                    <label for="editCurrencyName"><i class="fas fa-money-bill-alt pos-icon"></i>Currency Name</label>
                                    <input type="text" class="form-control bg-secondary text-white pos-form-control" id="editCurrencyName" required>
                                </div>
                                <div class="form-group">
                                    <label for="editCurrencyRate"><i class="fas fa-exchange-alt pos-icon"></i>Rate to USD</label>
                                    <input type="number" step="0.0001" class="form-control bg-secondary text-white pos-form-control" id="editCurrencyRate" required>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer border-secondary">
                            <button type="button" class="btn btn-secondary pos-btn" onclick="closeEditCurrencyModal()">
                                <i class="fas fa-times pos-icon"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-success pos-btn pos-btn-success" onclick="updateCurrency()">
                                <i class="fas fa-save pos-icon"></i>Save changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Append the modal to the body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }
    
    // Set the currency ID, name, and rate in the modal
    document.getElementById('editCurrencyId').value = currencyId;
    document.getElementById('editCurrencyName').value = name;
    document.getElementById('editCurrencyRate').value = rate;
    
    // Show the modal
    $('#editCurrencyModal').modal('show');
}

// Function to close the edit currency modal
function closeEditCurrencyModal() {
    $('#editCurrencyModal').modal('hide');
}

// Function to update a currency
function updateCurrency() {
    const currencyId = document.getElementById('editCurrencyId').value;
    const name = document.getElementById('editCurrencyName').value;
    const rate = document.getElementById('editCurrencyRate').value;
    
    if (!name || !rate) {
        showWarningAlert('Please enter both name and rate');
        return;
    }
    
    // Send the request to update the currency
    fetch(`/pos/update_currency/${currencyId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: name,
            rate: rate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessAlert('Currency updated successfully');
            // Close the modal
            $('#editCurrencyModal').modal('hide');
            // Reload the currencies list
            loadCurrencies();
        } else {
            showErrorAlert(data.message || 'Error updating currency');
        }
    })
    .catch(error => {
        console.error('Error updating currency:', error);
        showErrorAlert(`Error updating currency: ${error.message}`);
    });
}

// Function to delete a currency
function deleteCurrency(currencyId, name) {
    showConfirmDialog(`Are you sure you want to delete currency "${name}"? This action cannot be undone.`, 'Delete', 'Cancel').then(confirmed => {
        if (confirmed) {
        // Send the request to delete the currency
        fetch(`/pos/delete_currency/${currencyId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessAlert('Currency deleted successfully');
                // Reload the currencies list
                loadCurrencies();
            } else {
                showErrorAlert(data.message || 'Error deleting currency');
            }
        })
        .catch(error => {
            console.error('Error deleting currency:', error);
            showErrorAlert(`Error deleting currency: ${error.message}`);
        });
        }
    });
}
