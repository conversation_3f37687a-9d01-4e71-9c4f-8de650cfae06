// Split Payment Handler
let paymentMethods = [];
let totalSplitAmount = 0;

document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const splitPaymentBtn = document.getElementById('splitPaymentBtn');
    const addPaymentMethodBtn = document.getElementById('addPaymentMethodBtn');
    const cancelSplitPaymentBtn = document.getElementById('cancelSplitPaymentBtn');
    const closeButtons = document.querySelectorAll('#splitPaymentModal .btn-close, #splitPaymentModal [data-bs-dismiss="modal"]');

    console.log('DOMContentLoaded event fired');

    // Add event listeners
    if (splitPaymentBtn) {
        splitPaymentBtn.addEventListener('click', openSplitPaymentModal);
        console.log('Added click event listener to splitPaymentBtn');
    }

    // Add event listeners to all close buttons
    closeButtons.forEach(button => {
        button.addEventListener('click', closeSplitPaymentModal);
    });
    console.log('Added click event listeners to close buttons');

    // Add event listener to the cancel button
    if (cancelSplitPaymentBtn) {
        cancelSplitPaymentBtn.addEventListener('click', closeSplitPaymentModal);
        console.log('Added click event listener to cancelSplitPaymentBtn');
    }

    if (addPaymentMethodBtn) {
        addPaymentMethodBtn.addEventListener('click', addPaymentMethod);
        console.log('Added click event listener to addPaymentMethodBtn');
    }

    // Add event listener to handle modal shown event
    const splitPaymentModalEl = document.getElementById('splitPaymentModal');
    if (splitPaymentModalEl) {
        splitPaymentModalEl.addEventListener('shown.bs.modal', function() {
            // Focus on the payment amount input when the modal is shown
            const paymentMethodAmount = document.getElementById('paymentMethodAmount');
            if (paymentMethodAmount) {
                setTimeout(() => {
                    paymentMethodAmount.focus();
                }, 100);
            }
        });
    }
});

function openSplitPaymentModal() {
    const totalElement = document.getElementById('total');
    const totalSplitAmountDue = document.getElementById('totalSplitAmountDue');
    const splitTotalDue = document.getElementById('splitTotalDue');
    const splitRemaining = document.getElementById('splitRemaining');
    const splitGiftCardAmountUsed = document.getElementById('splitGiftCardAmountUsed');
    const giftCardAmountUsed = document.getElementById('giftCardAmountUsed') || { textContent: '0.00' };
    const splitStoreCreditInfo = document.getElementById('splitStoreCreditInfo');

    if (!totalElement) {
        console.error("Total element not found");
        return;
    }

    // Reset payment methods
    paymentMethods = [];

    // Get the total amount
    totalSplitAmount = parseFloat(totalElement.textContent) || 0;

    if (totalSplitAmount <= 0) {
        alert('Please add items to the cart before using split payment');
        return;
    }

    // Update the modal
    totalSplitAmountDue.textContent = totalSplitAmount.toFixed(2);
    splitTotalDue.textContent = totalSplitAmount.toFixed(2);
    splitRemaining.textContent = totalSplitAmount.toFixed(2);
    splitGiftCardAmountUsed.textContent = giftCardAmountUsed.textContent;

    // Update store credit info if available
    if (splitStoreCreditInfo) {
        const customerId = document.getElementById('customerId').value;
        console.log("Split payment modal - Customer ID:", customerId);

        // Try to get store credit amount from multiple sources
        let storeCreditAmount = 0;
        let storeCreditSource = "none";

        // Get the store credit account ID from the main cart
        let storeCreditAccountId = document.getElementById('storeCreditAccountId')?.value;
        console.log("Split payment modal - Store Credit Account ID:", storeCreditAccountId);

        // First check if there's a store credit balance in the customer details section
        const storeCreditBalanceElement = document.querySelector('#customerDetails #storeCreditBalance');
        if (storeCreditBalanceElement && storeCreditBalanceElement.textContent) {
            const balanceText = storeCreditBalanceElement.textContent.trim();
            console.log("Found store credit balance text:", balanceText);

            // Extract the numeric value using a regex that handles currency symbols
            const balanceMatch = balanceText.match(/[^0-9]*([0-9]+(\.[0-9]+)?)/);
            if (balanceMatch) {
                storeCreditAmount = parseFloat(balanceMatch[1]) || 0;
                storeCreditSource = "balance display";
                console.log("Extracted store credit amount from balance display:", storeCreditAmount);
            }
        }

        // If that didn't work, try the hidden input field
        if (storeCreditAmount <= 0) {
            const storeCreditAmountInput = document.getElementById('storeCreditAmount');
            if (storeCreditAmountInput && storeCreditAmountInput.value) {
                storeCreditAmount = parseFloat(storeCreditAmountInput.value) || 0;
                storeCreditSource = "hidden input";
                console.log("Got store credit amount from hidden input:", storeCreditAmount);
            }
        }

        // If that's not available, try to get it from the displayed applied value
        if (storeCreditAmount <= 0) {
            const storeCreditApplied = document.getElementById('storeCreditApplied');
            if (storeCreditApplied && storeCreditApplied.textContent) {
                storeCreditAmount = parseFloat(storeCreditApplied.textContent) || 0;
                storeCreditSource = "applied value";
                console.log("Got store credit amount from applied value:", storeCreditAmount);
            }
        }

        console.log("Split payment - Customer ID:", customerId);
        console.log("Split payment - Store Credit Amount:", storeCreditAmount);
        console.log("Split payment - Store Credit Source:", storeCreditSource);

        if (customerId && storeCreditAmount > 0) {
            // Get the store credit account ID from the main cart
            let storeCreditAccountId = document.getElementById('storeCreditAccountId')?.value;
            console.log("Split payment - Store Credit Account ID from DOM:", storeCreditAccountId);

            // If we don't have a store credit account ID, create one from the customer ID
            if (!storeCreditAccountId && customerId) {
                if (!customerId.startsWith('gid://')) {
                    storeCreditAccountId = `gid://shopify/Customer/${customerId}`;
                } else {
                    storeCreditAccountId = customerId;
                }
                console.log("Split payment - Created Store Credit Account ID:", storeCreditAccountId);

                // Create a hidden input for the split payment modal to store the store credit account ID
                let splitStoreCreditAccountIdInput = document.getElementById('splitStoreCreditAccountId');
                if (!splitStoreCreditAccountIdInput) {
                    splitStoreCreditAccountIdInput = document.createElement('input');
                    splitStoreCreditAccountIdInput.type = 'hidden';
                    splitStoreCreditAccountIdInput.id = 'splitStoreCreditAccountId';
                    document.body.appendChild(splitStoreCreditAccountIdInput);
                }
                splitStoreCreditAccountIdInput.value = storeCreditAccountId;
                console.log("Split payment - Created hidden input for store credit account ID:", storeCreditAccountId);
            }

            // Update the hidden input in the split payment modal
            const splitStoreCreditAccountIdInput = document.getElementById('splitStoreCreditAccountId');
            if (splitStoreCreditAccountIdInput) {
                splitStoreCreditAccountIdInput.value = storeCreditAccountId;
                console.log("Updated split payment store credit account ID:", splitStoreCreditAccountIdInput.value);
            } else {
                console.warn("Split store credit account ID input not found in the DOM");
            }

            // Also update the main store credit account ID if it's missing
            if (!document.getElementById('storeCreditAccountId')?.value && storeCreditAccountId) {
                const mainStoreCreditAccountIdInput = document.getElementById('storeCreditAccountId');
                if (mainStoreCreditAccountIdInput) {
                    mainStoreCreditAccountIdInput.value = storeCreditAccountId;
                    console.log("Updated main store credit account ID:", mainStoreCreditAccountIdInput.value);
                }
            }

            splitStoreCreditInfo.style.display = 'block';
            splitStoreCreditInfo.innerHTML = `<strong>Available Store Credit:</strong> $${storeCreditAmount.toFixed(2)}`;

            // Add a data attribute to store the amount for easier access
            splitStoreCreditInfo.dataset.amount = storeCreditAmount.toFixed(2);
        } else {
            splitStoreCreditInfo.style.display = 'none';
            splitStoreCreditInfo.dataset.amount = '0.00';
        }
    }

    // Clear the payment methods list
    const paymentMethodsList = document.getElementById('paymentMethodsList');
    paymentMethodsList.innerHTML = '<p class="text-muted text-white-50">No payment methods added yet</p>';

    // Reset the total paid
    document.getElementById('splitTotalPaid').textContent = '0.00';

    // Disable the complete payment button
    document.getElementById('completeSplitPaymentBtn').disabled = true;

    // Show the modal using Bootstrap
    const splitPaymentModalEl = document.getElementById('splitPaymentModal');

    // Remove any existing backdrop before showing the modal
    const existingBackdrops = document.querySelectorAll('.modal-backdrop');
    existingBackdrops.forEach(backdrop => backdrop.remove());

    // Initialize and show the modal
    const splitPaymentModal = new bootstrap.Modal(splitPaymentModalEl, {
        backdrop: 'static', // Prevent closing when clicking outside
        keyboard: false     // Prevent closing with keyboard
    });

    // Clear any inline styles that might be causing issues
    splitPaymentModalEl.style.display = '';
    splitPaymentModalEl.style.paddingRight = '';

    // Show the modal
    splitPaymentModal.show();
}

function closeSplitPaymentModal() {
    // Close the modal using Bootstrap
    const splitPaymentModalEl = document.getElementById('splitPaymentModal');
    const splitPaymentModal = bootstrap.Modal.getInstance(splitPaymentModalEl);

    if (splitPaymentModal) {
        splitPaymentModal.hide();

        // Remove the modal backdrop manually if it's still present
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());

            // Ensure the body doesn't have modal-open class
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, 150);
    }
}

function addPaymentMethod() {
    const paymentMethodType = document.getElementById('paymentMethodType').value;
    const paymentMethodAmount = parseFloat(document.getElementById('paymentMethodAmount').value);
    const paymentMethodsList = document.getElementById('paymentMethodsList');
    const splitTotalPaid = document.getElementById('splitTotalPaid');
    const splitRemaining = document.getElementById('splitRemaining');

    // Validate the amount
    if (isNaN(paymentMethodAmount) || paymentMethodAmount <= 0) {
        alert('Please enter a valid amount greater than 0');
        return;
    }

    // Create a unique ID for this payment method
    const paymentId = `payment-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Create the payment method object
    const paymentMethod = {
        type: paymentMethodType,
        amount: paymentMethodAmount,
        id: paymentId,
        status: 'pending' // Initial status is pending
    };

    // Calculate the remaining amount
    const currentTotalPaid = parseFloat(splitTotalPaid.textContent) || 0;
    const newTotalPaid = currentTotalPaid + paymentMethodAmount;

    // Check if the new total paid exceeds the total amount
    if (newTotalPaid > totalSplitAmount) {
        alert('The total payment amount cannot exceed the total due');
        return;
    }

    // For store credit, check if the customer has enough available credit and apply it immediately
    if (paymentMethodType === 'storecredit') {
        const customerId = document.getElementById('customerId').value;
        if (!customerId) {
            alert('Please select a customer to use store credit');
            return;
        }

        console.log("Store credit payment - Customer ID from DOM:", customerId);

        // Try to get store credit amount from multiple sources
        let availableStoreCredit = 0;
        let creditSource = "none";

        // First check the split payment modal info - use the data attribute we added
        const splitStoreCreditInfo = document.getElementById('splitStoreCreditInfo');
        if (splitStoreCreditInfo && splitStoreCreditInfo.style.display !== 'none') {
            // First try the data attribute we added
            if (splitStoreCreditInfo.dataset.amount) {
                availableStoreCredit = parseFloat(splitStoreCreditInfo.dataset.amount) || 0;
                creditSource = "data attribute";
                console.log("Got store credit amount from data attribute:", availableStoreCredit);
            } else {
                // Fall back to parsing the text content
                const infoText = splitStoreCreditInfo.textContent;
                const creditMatch = infoText.match(/[^0-9]*([0-9]+(\.[0-9]+)?)/);
                if (creditMatch) {
                    availableStoreCredit = parseFloat(creditMatch[1]) || 0;
                    creditSource = "modal text";
                    console.log("Got store credit amount from modal text:", availableStoreCredit);
                }
            }
        }

        // If not found in the modal, try the hidden input field
        if (availableStoreCredit <= 0) {
            const storeCreditAmountInput = document.getElementById('storeCreditAmount');
            if (storeCreditAmountInput && storeCreditAmountInput.value) {
                availableStoreCredit = parseFloat(storeCreditAmountInput.value) || 0;
                creditSource = "hidden input";
                console.log("Got store credit amount from hidden input:", availableStoreCredit);
            }
        }

        // If still not found, try the displayed value in the main cart
        if (availableStoreCredit <= 0) {
            const storeCreditApplied = document.getElementById('storeCreditApplied');
            if (storeCreditApplied && storeCreditApplied.textContent) {
                availableStoreCredit = parseFloat(storeCreditApplied.textContent) || 0;
                creditSource = "applied value";
                console.log("Got store credit amount from applied value:", availableStoreCredit);
            }
        }

        // If still not found, check the customer details section directly
        if (availableStoreCredit <= 0) {
            const storeCreditBalanceElement = document.querySelector('#customerDetails #storeCreditBalance');
            if (storeCreditBalanceElement && storeCreditBalanceElement.textContent) {
                const balanceText = storeCreditBalanceElement.textContent.trim();
                const balanceMatch = balanceText.match(/[^0-9]*([0-9]+(\.[0-9]+)?)/);
                if (balanceMatch) {
                    availableStoreCredit = parseFloat(balanceMatch[1]) || 0;
                    creditSource = "balance display";
                    console.log("Got store credit amount from balance display:", availableStoreCredit);
                }
            }
        }

        console.log("Adding store credit payment - Available credit:", availableStoreCredit);
        console.log("Adding store credit payment - Customer ID:", customerId);

        // In the split payment modal, we should use the splitStoreCreditAccountId
        // This is important because we're in the split payment context
        let storeCreditAccountId = document.getElementById('splitStoreCreditAccountId')?.value ||
                                  document.getElementById('storeCreditAccountId')?.value;

        console.log("Store credit account ID from DOM:", storeCreditAccountId);

        console.log("Adding store credit payment - Store Credit Account ID:", storeCreditAccountId);

        // If we don't have a store credit account ID, we need to get it from the customer ID
        if (!storeCreditAccountId) {
            console.log("Store credit account ID not found, will attempt to get it from customer ID");

            // Format the customer ID for Shopify GraphQL
            if (!customerId.startsWith('gid://')) {
                storeCreditAccountId = `gid://shopify/Customer/${customerId}`;
                console.log("Created store credit account ID from customer ID:", storeCreditAccountId);
            } else {
                storeCreditAccountId = customerId;
                console.log("Using customer ID as store credit account ID:", storeCreditAccountId);
            }
        }

        // Ensure we're using a customer ID, not a store credit account ID
        // The server will handle the conversion to the correct store credit account ID
        if (storeCreditAccountId && !storeCreditAccountId.includes('Customer')) {
            // If it's not a customer ID, convert it to one
            if (customerId) {
                if (!customerId.startsWith('gid://')) {
                    storeCreditAccountId = `gid://shopify/Customer/${customerId}`;
                } else {
                    storeCreditAccountId = customerId;
                }
                console.log("Converted to customer ID for store credit:", storeCreditAccountId);
            }
        }

        // Check if customer has enough store credit
        if (availableStoreCredit < paymentMethodAmount) {
            // Show a more detailed error message in the UI
            const errorMessage = document.createElement('div');
            errorMessage.className = 'alert alert-danger mt-3';
            errorMessage.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Insufficient store credit:</strong> Customer only has $${availableStoreCredit.toFixed(2)} available.
                <br>
                <small>Please enter an amount less than or equal to the available store credit.</small>
            `;
            paymentMethodsList.appendChild(errorMessage);
            return;
        }

        // Log the store credit information for debugging
        console.log("Store credit validation passed:");
        console.log("- Available credit:", availableStoreCredit);
        console.log("- Payment amount:", paymentMethodAmount);
        console.log("- Customer ID:", customerId);
        console.log("- Store credit account ID:", storeCreditAccountId);

        // Check if store credit has already been added
        const existingStoreCredit = paymentMethods.find(method => method.type === 'storecredit');
        if (existingStoreCredit) {
            const totalStoreCreditUsed = existingStoreCredit.amount + paymentMethodAmount;
            if (totalStoreCreditUsed > availableStoreCredit) {
                alert(`Cannot exceed available store credit of ${availableStoreCredit.toFixed(2)}`);
                return;
            }
        }

        // Show processing indicator
        const processingIndicator = document.createElement('div');
        processingIndicator.id = 'storeCreditProcessingIndicator';
        processingIndicator.className = 'text-center my-3';
        processingIndicator.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Processing...</span>
            </div>
            <p class="mt-2 mb-0">Applying store credit...</p>
        `;
        paymentMethodsList.appendChild(processingIndicator);

        // Apply store credit immediately
        console.log('Applying store credit with amount:', paymentMethodAmount, 'for customer:', customerId);

        // Format the customer ID for the API if needed
        let formattedCustomerId = customerId;
        if (!formattedCustomerId.startsWith('gid://')) {
            formattedCustomerId = `gid://shopify/Customer/${customerId}`;
            console.log("Formatted customer ID for API:", formattedCustomerId);
        }

        // Ensure we have a valid store credit account ID - reuse the variable from above
        storeCreditAccountId = document.getElementById('splitStoreCreditAccountId')?.value ||
                              document.getElementById('storeCreditAccountId')?.value;

        // If we still don't have a store credit account ID, use the formatted customer ID
        if (!storeCreditAccountId) {
            storeCreditAccountId = formattedCustomerId;
            console.log("Using formatted customer ID as store credit account ID:", storeCreditAccountId);
        }

        // Prepare the request data
        const requestData = {
            customer_id: formattedCustomerId,
            amount: paymentMethodAmount,
            apply_immediately: true,
            skip_inventory_adjustment: true, // Skip inventory adjustment when applying store credit
            store_credit_account_id: storeCreditAccountId, // Include the store credit account ID
            available_credit: availableStoreCredit // Include the available credit amount for validation
        };

        console.log("Sending store credit application request:", requestData);

        fetch('/pos/apply_store_credit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            console.log('Store credit API response status:', response.status);
            return response.json();
        })
        .then(data => {
            // Remove processing indicator
            const indicator = document.getElementById('storeCreditProcessingIndicator');
            if (indicator) indicator.remove();

            console.log('Store credit API response data:', data);

            // If the response includes a store_credit_account_id, save it for future use
            if (data.success && data.store_credit_account_id) {
                // Update the hidden input in the split payment modal
                let splitStoreCreditAccountIdInput = document.getElementById('splitStoreCreditAccountId');
                if (!splitStoreCreditAccountIdInput) {
                    splitStoreCreditAccountIdInput = document.createElement('input');
                    splitStoreCreditAccountIdInput.type = 'hidden';
                    splitStoreCreditAccountIdInput.id = 'splitStoreCreditAccountId';
                    document.body.appendChild(splitStoreCreditAccountIdInput);
                }
                splitStoreCreditAccountIdInput.value = data.store_credit_account_id;
                console.log("Updated store credit account ID from API response:", data.store_credit_account_id);

                // Also update the main store credit account ID
                let mainStoreCreditAccountIdInput = document.getElementById('storeCreditAccountId');
                if (!mainStoreCreditAccountIdInput) {
                    mainStoreCreditAccountIdInput = document.createElement('input');
                    mainStoreCreditAccountIdInput.type = 'hidden';
                    mainStoreCreditAccountIdInput.id = 'storeCreditAccountId';
                    document.body.appendChild(mainStoreCreditAccountIdInput);
                }
                mainStoreCreditAccountIdInput.value = data.store_credit_account_id;
                console.log("Updated main store credit account ID from API response:", data.store_credit_account_id);
            }

            if (data.success) {
                console.log('Store credit applied successfully. New balance:', data.total_balance);

                // Update the store credit amount in the UI
                const storeCreditAmountInput = document.getElementById('storeCreditAmount');
                if (storeCreditAmountInput) {
                    storeCreditAmountInput.value = data.total_balance;
                }

                // Update the store credit account ID if it was returned
                if (data.store_credit_account_id) {
                    const storeCreditAccountIdInput = document.getElementById('splitStoreCreditAccountId');
                    if (storeCreditAccountIdInput) {
                        storeCreditAccountIdInput.value = data.store_credit_account_id;
                    }

                    // Also update the main store credit account ID
                    const mainStoreCreditAccountIdInput = document.getElementById('storeCreditAccountId');
                    if (mainStoreCreditAccountIdInput) {
                        mainStoreCreditAccountIdInput.value = data.store_credit_account_id;
                    }
                }

                // Mark the payment as completed
                paymentMethod.status = 'completed';
                paymentMethod.transaction_id = data.transaction_id || `SC-${Date.now()}`;

                // Add the payment method to the array
                paymentMethods.push(paymentMethod);

                // Update the UI
                updatePaymentMethodsList();

                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success mt-3';
                successMessage.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    Store credit of $${paymentMethodAmount.toFixed(2)} applied successfully.
                    Remaining balance: $${data.total_balance.toFixed(2)}
                `;
                paymentMethodsList.appendChild(successMessage);

                // Remove the success message after 3 seconds
                setTimeout(() => {
                    successMessage.remove();
                }, 3000);
            } else {
                console.error('Error applying store credit:', data.message);

                // Parse the error message to provide more specific feedback
                let errorMsg = data.message || 'Unknown error';
                let detailedMsg = 'Please try again or use a different payment method.';

                // Check for specific error patterns
                if (errorMsg.includes('exceeds available') || errorMsg.includes('insufficient')) {
                    // This is a balance error - provide more helpful guidance
                    detailedMsg = `The available store credit balance is $${availableStoreCredit.toFixed(2)}. Please enter a smaller amount.`;
                } else if (errorMsg.includes('not found') || errorMsg.includes('invalid') || errorMsg.includes('does not exist')) {
                    // This is an account ID error
                    detailedMsg = 'The store credit account could not be found. Please try selecting the customer again.';

                    // Try to refresh the customer's store credit information
                    if (customerId) {
                        console.log("Attempting to refresh customer store credit information for ID:", customerId);
                        // This will trigger a refresh of the customer's store credit information next time they're selected
                        const customerElement = document.querySelector(`[data-customer-id="${customerId}"]`);
                        if (customerElement) {
                            customerElement.click();
                            detailedMsg += ' Customer information has been refreshed. Please try again.';
                        }
                    }
                }

                // Show a more detailed error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger mt-3';
                errorMessage.innerHTML = `
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Error applying store credit:</strong> ${errorMsg}
                    <br>
                    <small>${detailedMsg}</small>
                `;
                paymentMethodsList.appendChild(errorMessage);

                // Log detailed debugging information
                console.error('Store credit application failed:');
                console.error('- Error message:', errorMsg);
                console.error('- Customer ID:', customerId);
                console.error('- Store credit account ID:', storeCreditAccountId);
                console.error('- Amount requested:', paymentMethodAmount);
                console.error('- Available credit (client-side):', availableStoreCredit);

                // Don't automatically dismiss error messages
            }
        })
        .catch(error => {
            // Remove processing indicator
            const indicator = document.getElementById('storeCreditProcessingIndicator');
            if (indicator) indicator.remove();

            console.error('Error applying store credit:', error);

            // Show a more detailed error message in the UI
            const errorMessage = document.createElement('div');
            errorMessage.className = 'alert alert-danger mt-3';
            errorMessage.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Network Error:</strong> ${error.message || 'Failed to connect to server'}
                <br>
                <small>Please check your connection and try again.</small>
            `;
            paymentMethodsList.appendChild(errorMessage);

            // Log additional debugging information
            console.log('Store credit account ID used:', storeCreditAccountId);
            console.log('Customer ID used:', customerId);
            console.log('Payment amount attempted:', paymentMethodAmount);
        });

        return; // Exit early since we're handling this asynchronously
    }

    // For card payments, we'll handle them separately
    if (paymentMethodType === 'card') {
        // Add the payment method to the array with pending status
        paymentMethods.push(paymentMethod);

        // Update the UI
        updatePaymentMethodsList();

        // No automatic processing - user will manually confirm the payment
        return; // Exit early since we're handling this manually
    }

    // For cash payments, mark as completed immediately
    if (paymentMethodType === 'cash') {
        paymentMethod.status = 'completed';
    }

    // Add the payment method to the array
    paymentMethods.push(paymentMethod);

    // Update the payment methods list
    updatePaymentMethodsList();

    // Clear the amount input
    document.getElementById('paymentMethodAmount').value = '';
}

// Function to update the payment methods list in the UI
function updatePaymentMethodsList() {
    const paymentMethodsList = document.getElementById('paymentMethodsList');
    const splitTotalPaid = document.getElementById('splitTotalPaid');
    const splitRemaining = document.getElementById('splitRemaining');

    paymentMethodsList.innerHTML = '';

    if (paymentMethods.length === 0) {
        paymentMethodsList.innerHTML = '<p class="text-muted text-white-50">No payment methods added yet</p>';
    } else {
        paymentMethods.forEach((method, i) => {
            // Create a new payment method element
            const paymentMethodElement = document.createElement('div');
            paymentMethodElement.className = 'payment-method-item d-flex justify-content-between align-items-center mb-2 p-2 border rounded';

            // Add status-specific class
            if (method.status === 'pending') {
                paymentMethodElement.classList.add('payment-pending');
            } else if (method.status === 'completed') {
                paymentMethodElement.classList.add('payment-completed');
            } else if (method.status === 'failed') {
                paymentMethodElement.classList.add('payment-failed');
            }

            // Set the icon based on the payment method type
            let icon;
            if (method.type === 'cash') {
                icon = 'fa-money-bill-wave';
            } else if (method.type === 'card') {
                icon = 'fa-credit-card';
            } else if (method.type === 'storecredit') {
                icon = 'fa-wallet';
            } else {
                icon = 'fa-credit-card'; // Default icon
            }

            // Create status indicator
            let statusIndicator = '';
            if (method.status === 'pending') {
                statusIndicator = '<span class="badge bg-warning ms-2">Pending</span>';
            } else if (method.status === 'completed') {
                statusIndicator = '<span class="badge bg-success ms-2">Completed</span>';
            } else if (method.status === 'failed') {
                statusIndicator = '<span class="badge bg-danger ms-2">Failed</span>';
            }

            // Create action buttons based on status
            let actionButtons = '';
            if (method.status === 'pending' && method.type === 'card') {
                actionButtons = `
                    <button class="btn btn-sm btn-success confirm-card-payment me-1" data-index="${i}">
                        <i class="fas fa-check"></i> Confirm Payment
                    </button>
                `;
            }

            // Add remove button only if payment is not completed
            const removeButton = method.status !== 'completed' ?
                `<button class="btn btn-sm btn-danger remove-payment-method" data-index="${i}">
                    <i class="fas fa-times"></i>
                </button>` : '';

            paymentMethodElement.innerHTML = `
                <div>
                    <i class="fas ${icon} me-2 text-info"></i>
                    <span>${method.type.charAt(0).toUpperCase() + method.type.slice(1)}</span>
                    ${statusIndicator}
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-2">$${method.amount.toFixed(2)}</span>
                    ${actionButtons}
                    ${removeButton}
                </div>
            `;

            // Add event listener to the remove button if it exists
            const removeBtn = paymentMethodElement.querySelector('.remove-payment-method');
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    removePaymentMethod(index);
                });
            }

            // Add event listener to the confirm card payment button if it exists
            const confirmCardBtn = paymentMethodElement.querySelector('.confirm-card-payment');
            if (confirmCardBtn) {
                confirmCardBtn.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    confirmCardPaymentCompleted(paymentMethods[index]);
                });
            }

            // Add the payment method element to the list
            paymentMethodsList.appendChild(paymentMethodElement);
        });
    }

    // Calculate total paid amount from completed payments only
    const totalPaid = paymentMethods
        .filter(method => method.status === 'completed')
        .reduce((sum, method) => sum + method.amount, 0);

    // Update the total paid and remaining
    splitTotalPaid.textContent = totalPaid.toFixed(2);
    splitRemaining.textContent = (totalSplitAmount - totalPaid).toFixed(2);

    // Enable the complete button only if all payments are completed and the total matches
    const allCompleted = paymentMethods.every(method => method.status === 'completed');
    const totalMatches = Math.abs(totalPaid - totalSplitAmount) < 0.01; // Allow for small rounding errors

    // Add debugging logs
    console.log('Payment methods:', paymentMethods);
    console.log('All completed:', allCompleted);
    console.log('Total matches:', totalMatches);
    console.log('Total paid:', totalPaid);
    console.log('Total amount:', totalSplitAmount);
    console.log('Difference:', Math.abs(totalPaid - totalSplitAmount));

    // Get the button and enable/disable it
    const completeBtn = document.getElementById('completeSplitPaymentBtn');
    if (completeBtn) {
        completeBtn.disabled = !(allCompleted && totalMatches);

        // Log button state
        console.log('Complete button disabled:', completeBtn.disabled);

        // Add a direct click handler to ensure it works
        completeBtn.onclick = function() {
            console.log('Complete button clicked directly');
            window.completeSplitPayment();
        };
    } else {
        console.error('Complete button not found in updatePaymentMethodsList');
    }
}

function removePaymentMethod(index) {
    // Get the payment method to be removed
    const paymentMethod = paymentMethods[index];

    // Check if the payment is completed - if so, don't allow removal
    if (paymentMethod.status === 'completed') {
        alert('Cannot remove a completed payment. Please start over if you need to change the payment methods.');
        return;
    }

    // Remove the payment method from the array
    paymentMethods.splice(index, 1);

    // Update the UI
    updatePaymentMethodsList();
}

// Function to process a card payment
function processCardPayment(paymentMethod) {
    const tillId = document.getElementById('tillId').value;
    if (!tillId) {
        alert('Till ID is required for card payments');
        return;
    }

    // Show processing indicator
    const paymentMethodsList = document.getElementById('paymentMethodsList');
    const processingIndicator = document.createElement('div');
    processingIndicator.id = 'cardProcessingIndicator';
    processingIndicator.className = 'text-center my-3';
    processingIndicator.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Processing...</span>
        </div>
        <p class="mt-2 mb-0">Connecting to card machine...</p>
    `;
    paymentMethodsList.appendChild(processingIndicator);

    // Process the card payment
    fetch('/pos/process_card_payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            till_id: tillId,
            amount: paymentMethod.amount,
            reference_id: `SPLIT-${paymentMethod.id}`,
            is_split_payment: true,
            payment_index: paymentMethods.indexOf(paymentMethod)
        })
    })
    .then(response => response.json())
    .then(data => {
        // Remove processing indicator
        const indicator = document.getElementById('cardProcessingIndicator');
        if (indicator) indicator.remove();

        if (data.success) {
            // Check for approval by examining multiple fields in the response
            const isApproved = checkIfTransactionApproved(data);

            if (isApproved) {
                // Get the approval code from the response
                const approvalCode = getApprovalCode(data);

                // Mark the payment as completed
                paymentMethod.status = 'completed';
                paymentMethod.approval_code = approvalCode;
                paymentMethod.transaction_id = data.transaction_id || data.reference_id;

                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success mt-3';
                successMessage.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    Card payment of $${paymentMethod.amount.toFixed(2)} approved.
                    Approval code: ${approvalCode || 'N/A'}
                `;
                paymentMethodsList.appendChild(successMessage);

                // Remove the success message after 3 seconds
                setTimeout(() => {
                    successMessage.remove();
                }, 3000);
            } else {
                // Mark the payment as failed
                paymentMethod.status = 'failed';
                paymentMethod.error = data.message || 'Payment declined';

                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger mt-3';
                errorMessage.innerHTML = `
                    <i class="fas fa-times-circle me-2"></i>
                    Card payment declined: ${data.message || 'Unknown reason'}
                `;
                paymentMethodsList.appendChild(errorMessage);
            }
        } else {
            // Mark the payment as failed
            paymentMethod.status = 'failed';
            paymentMethod.error = data.message || data.error || 'Unknown error';

            // Show error message
            const errorMessage = document.createElement('div');
            errorMessage.className = 'alert alert-danger mt-3';
            errorMessage.innerHTML = `
                <i class="fas fa-times-circle me-2"></i>
                Error processing card payment: ${data.message || data.error || 'Unknown error'}
            `;
            paymentMethodsList.appendChild(errorMessage);
        }

        // Update the UI
        updatePaymentMethodsList();
    })
    .catch(error => {
        // Remove processing indicator
        const indicator = document.getElementById('cardProcessingIndicator');
        if (indicator) indicator.remove();

        console.error('Error processing card payment:', error);

        // Mark the payment as failed
        paymentMethod.status = 'failed';
        paymentMethod.error = error.message;

        // Show error message
        const errorMessage = document.createElement('div');
        errorMessage.className = 'alert alert-danger mt-3';
        errorMessage.innerHTML = `
            <i class="fas fa-times-circle me-2"></i>
            Error processing card payment: ${error.message}
        `;
        paymentMethodsList.appendChild(errorMessage);

        // Update the UI
        updatePaymentMethodsList();
    });
}

// Helper function to check if a card transaction was approved
function checkIfTransactionApproved(data) {
    // Check various fields that might indicate approval
    if (data.approved === true) return true;
    if (data.status === 'approved') return true;
    if (data.result === 'approved') return true;

    // Check for approval code
    if (data.approval_code) return true;
    if (data.approvalCode) return true;
    if (data.approval) return true;

    // Check raw response for approval indicators
    const rawResponse = data.raw_response || '';
    if (typeof rawResponse === 'string') {
        if (rawResponse.toLowerCase().includes('approved')) return true;
        if (rawResponse.toLowerCase().includes('approval')) return true;
    }

    return false;
}

// Helper function to get the approval code from the response
function getApprovalCode(data) {
    return data.approval_code || data.approvalCode || data.approval || 'N/A';
}

// Function to confirm a card payment as completed
function confirmCardPaymentCompleted(paymentMethod) {
    // Check if the payment is already completed
    if (paymentMethod.status === 'completed') {
        alert('This payment is already completed.');
        return;
    }

    // Show confirmation dialog
    const confirmed = confirm(`Please confirm that the card payment of $${paymentMethod.amount.toFixed(2)} has been successfully processed on the card machine.`);

    if (!confirmed) {
        return; // User cancelled the confirmation
    }

    // Mark the payment as completed
    paymentMethod.status = 'completed';
    paymentMethod.approval_code = 'CONFIRMED';
    paymentMethod.transaction_id = `CARD-${Date.now()}`;

    // Show success message
    const paymentMethodsList = document.getElementById('paymentMethodsList');
    const successMessage = document.createElement('div');
    successMessage.className = 'alert alert-success mt-3';
    successMessage.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        Card payment of $${paymentMethod.amount.toFixed(2)} confirmed as completed.
    `;
    paymentMethodsList.appendChild(successMessage);

    // Remove the success message after 3 seconds
    setTimeout(() => {
        successMessage.remove();
    }, 3000);

    // Update the UI
    updatePaymentMethodsList();
}

// Helper function to show a loading overlay
function showLoadingOverlay(message = 'Processing...') {
    // Close any open modals first
    const splitPaymentModalEl = document.getElementById('splitPaymentModal');
    const splitPaymentModal = bootstrap.Modal.getInstance(splitPaymentModalEl);
    if (splitPaymentModal) {
        splitPaymentModal.hide();

        // Remove modal backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        // Reset body styles
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }

    // Check if overlay already exists
    let overlay = document.getElementById('loadingOverlay');

    if (!overlay) {
        // Create overlay if it doesn't exist
        overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        overlay.style.display = 'flex';
        overlay.style.flexDirection = 'column';
        overlay.style.justifyContent = 'center';
        overlay.style.alignItems = 'center';
        overlay.style.zIndex = '2000'; // Higher z-index to ensure it's above everything

        // Add spinner
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border text-light';
        spinner.style.width = '3rem';
        spinner.style.height = '3rem';
        spinner.setAttribute('role', 'status');

        // Add loading text
        const loadingText = document.createElement('h5');
        loadingText.id = 'loadingText';
        loadingText.className = 'text-light mt-3';
        loadingText.textContent = message;

        // Add processing details
        const processingDetails = document.createElement('p');
        processingDetails.id = 'processingDetails';
        processingDetails.className = 'text-light mt-2';
        processingDetails.style.fontSize = '0.9rem';
        processingDetails.textContent = 'Please wait...';

        // Append elements to overlay
        overlay.appendChild(spinner);
        overlay.appendChild(loadingText);
        overlay.appendChild(processingDetails);

        // Append overlay to body
        document.body.appendChild(overlay);
    } else {
        // Update existing overlay
        const loadingText = document.getElementById('loadingText');
        if (loadingText) {
            loadingText.textContent = message;
        }

        const processingDetails = document.getElementById('processingDetails');
        if (processingDetails) {
            processingDetails.textContent = 'Please wait...';
        }

        overlay.style.display = 'flex';
    }
}

// Helper function to hide the loading overlay
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        // First set display to none
        overlay.style.display = 'none';

        // Then remove it from the DOM after a short delay
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 100);
    }
}

// Helper function to reset customer details
function resetCustomerDetails() {
    const customerDetailsEl = document.getElementById('customerDetails');
    const customerNameEl = document.getElementById('customerName');
    const customerEmailEl = document.getElementById('customerEmail');
    const giftCardBalanceEl = document.getElementById('giftCardBalance');
    const customerIdEl = document.getElementById('customerId');
    const giftCardIdEl = document.getElementById('giftCardId');
    const giftCardAmountEl = document.getElementById('giftCardAmount');
    const giftCardAppliedEl = document.getElementById('giftCardApplied');
    const storeCreditAmountEl = document.getElementById('storeCreditAmount');
    const storeCreditAppliedEl = document.getElementById('storeCreditApplied');

    if (customerDetailsEl) customerDetailsEl.style.display = 'none';
    if (customerNameEl) customerNameEl.innerText = 'Name: ';
    if (customerEmailEl) customerEmailEl.innerText = 'Email: ';
    if (giftCardBalanceEl) giftCardBalanceEl.innerText = 'Gift Card Balance: $';
    if (customerIdEl) customerIdEl.value = '';
    if (giftCardIdEl) giftCardIdEl.value = '';
    if (giftCardAmountEl) giftCardAmountEl.value = '';
    if (giftCardAppliedEl) giftCardAppliedEl.textContent = '0.00';
    if (storeCreditAmountEl) storeCreditAmountEl.value = '';
    if (storeCreditAppliedEl) storeCreditAppliedEl.textContent = '0.00';

    // Hide any credit section that might be visible
    const creditSection = document.getElementById('creditSection');
    if (creditSection) {
        creditSection.style.display = 'none';
    }
}

// Function to get all items currently in the cart
function getCartItems() {
    // Check if there's a global getCartItems function
    // Use a different name to avoid infinite recursion
    if (typeof window.getCartItemsFromGlobal === 'function') {
        return window.getCartItemsFromGlobal();
    }

    // Fallback implementation
    const cartList = document.getElementById('cartList');
    if (!cartList) return []; // Return empty array if cartList doesn't exist

    return [...cartList.children].map(item => {
        if (item.classList.contains('list-group-item')) {
            const titleElement = item.querySelector('.cart-item-details');
            const title = titleElement ? titleElement.textContent.split(' - ')[0].trim() : '';
            const priceElement = item.querySelector('.price');
            const taxExemptCheckbox = item.querySelector('.tax-exempt-checkbox');
            const quantityInput = item.querySelector('.quantity');

            return {
                variant_id: item.dataset.id,
                quantity: quantityInput ? parseInt(quantityInput.value) : 0,
                title: title,
                price: priceElement ? parseFloat(priceElement.textContent) : 0,
                tax_exempt: taxExemptCheckbox ? taxExemptCheckbox.checked : false
            };
        }
        return null;
    }).filter(item => item !== null && !isNaN(item.quantity) && item.quantity > 0);
}

// Make completeSplitPayment globally accessible
window.completeSplitPayment = function() {
    console.log('completeSplitPayment function called');

    // Show loading overlay
    showLoadingOverlay('Processing split payment...');

    // Get the cart items before clearing the cart
    const cartItems = getCartItems();

    // Get the transaction data
    const transaction = {
        till_id: document.getElementById('tillId').value,
        items: cartItems, // Use the items we captured before clearing the cart
        total: totalSplitAmount,
        employee_name: document.getElementById('employeeName').value || 'Unknown',
        customer_id: document.getElementById('customerId').value || null,
        payment_method: 'split', // Use 'split' as the payment method
        payment_methods: paymentMethods,
        gift_card_id: document.getElementById('giftCardId').value || null,
        gift_card_amount: parseFloat(document.getElementById('giftCardApplied').textContent) || 0,
        store_credit_account_id: document.getElementById('storeCreditAccountId').value || null,
        store_credit_amount: parseFloat(document.getElementById('storeCreditApplied').textContent) || 0,
        use_store_credit: document.getElementById('storeCreditAccountId').value ? true : false
    };

    // Log the transaction data for debugging
    console.log('Sending transaction data:', JSON.stringify(transaction, null, 2));

    // Check if we have items in the cart
    if (!transaction.items || transaction.items.length === 0) {
        console.error('No items in cart to process');
        hideLoadingOverlay();
        alert('Error: No items in cart to process. Please add items to your cart and try again.');
        return;
    }

    // Update processing details
    const processingDetailsEl = document.getElementById('processingDetails');
    if (processingDetailsEl) processingDetailsEl.textContent = 'Sending transaction to server...';

    // Process the transaction with the server
    // Use the regular transaction endpoint instead of split transaction
    fetch('/pos/complete_transaction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            till_id: transaction.till_id,
            payment_method: 'split', // Use 'split' as the payment method
            total: transaction.total,
            items: transaction.items,
            customer_id: transaction.customer_id,
            employee_name: transaction.employee_name,
            gift_card_id: transaction.gift_card_id,
            gift_card_amount: transaction.gift_card_amount,
            store_credit_account_id: transaction.store_credit_account_id,
            store_credit_amount: transaction.store_credit_amount,
            use_store_credit: transaction.use_store_credit,
            payment_methods: transaction.payment_methods, // Include the payment methods for reference
            is_split_payment: true // Flag to indicate this is a split payment
        })
    })
    .then(response => {
        console.log('Transaction response status:', response.status);
        if (processingDetailsEl) processingDetailsEl.textContent = 'Processing response...';

        // Check if the response is ok
        if (!response.ok) {
            throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('Transaction response:', data);
        const loadingTextEl = document.getElementById('loadingText');

        if (data.success) {
            // Update loading overlay with success message
            if (loadingTextEl) loadingTextEl.textContent = 'Payment Successful!';
            if (processingDetailsEl) processingDetailsEl.textContent = 'Order has been created in Shopify.';

            // Reset customer details
            if (typeof window.resetCustomerDetails === 'function') {
                window.resetCustomerDetails();
            } else {
                // Fallback implementation
                resetCustomerDetails();
            }

            // Close the split payment modal
            closeSplitPaymentModal();

            // IMMEDIATELY clear the cart and local storage to prevent items from reappearing
            if (typeof window.clearCart === 'function') {
                window.clearCart();
            } else {
                // Fallback implementation
                const cartList = document.getElementById('cartList');
                if (cartList) {
                    cartList.innerHTML = '<li class="list-group-item">No items in cart</li>';
                }
            }

            // Explicitly remove from local storage in multiple ways to ensure it's gone
            localStorage.removeItem('posCartItems');
            localStorage.removeItem('lastSearchQuery');

            // Update totals to reflect empty cart
            if (typeof window.updateTotals === 'function') {
                window.updateTotals();
            }

            // Hide the loading overlay after a short delay
            setTimeout(function() {
                hideLoadingOverlay();

                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success fixed-top w-50 mx-auto mt-2 text-center';
                successMessage.innerHTML = `<strong>Success!</strong> Order completed successfully.`;
                document.body.appendChild(successMessage);

                // Remove success message after a delay
                setTimeout(function() {
                    // Remove success message
                    successMessage.remove();

                    // Force a page refresh to ensure a clean state
                    window.location.reload();
                }, 2000);
            }, 1000);
        } else {
            // Hide loading overlay
            hideLoadingOverlay();

            // Show error alert
            alert('Error processing payment: ' + (data.message || 'Unknown error'));

            // Log the error
            console.error('Error from server:', data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('Error processing transaction:', error);
        hideLoadingOverlay();

        // Show detailed error message
        alert(`An error occurred while processing the transaction: ${error.message}. Please try again.`);

        // Log the error details
        console.error('Transaction that failed:', transaction);
    });
}
