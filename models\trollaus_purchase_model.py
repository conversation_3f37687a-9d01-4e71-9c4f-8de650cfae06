from mongoengine import Document, StringField, FloatField, IntField, ListField, DictField, DateTimeField, BooleanField
from datetime import datetime

class TrollAusPurchase(Document):
    """
    Model for storing items purchased by TrollAus that need to be added to inventory.
    This collection stores items purchased through the POS Buy tab that will later
    be pushed to Shopify inventory.
    """
    # Purchase information
    purchase_id = StringField(required=True)  # Unique ID for this purchase
    username = StringF<PERSON>(required=True, default="TrollAus")  # Should always be TrollAus
    customer_id = StringField(required=True)  # Shopify customer ID
    customer_name = StringField(required=True)  # Customer name for reference
    customer_email = StringField()  # Customer email for reference
    
    # Item details
    items = ListField(DictField(), required=True)  # List of purchased items with product details
    
    # Financial information
    total_credit = FloatField(required=True)  # Total credit amount
    
    # Status tracking
    processed = BooleanField(default=False)  # Whether this has been processed and added to inventory
    processed_date = DateTimeField()  # When it was processed
    
    # Timestamps
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'trollAusPurchases',
        'indexes': [
            'username',
            'customer_id',
            'processed',
            'created_at'
        ]
    }
    
    def to_dict(self):
        """Convert the document to a dictionary"""
        return {
            "id": str(self.id),
            "purchase_id": self.purchase_id,
            "username": self.username,
            "customer_id": self.customer_id,
            "customer_name": self.customer_name,
            "customer_email": self.customer_email,
            "items": self.items,
            "total_credit": self.total_credit,
            "processed": self.processed,
            "processed_date": self.processed_date,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }
