<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&family=Roboto:wght@400;600&display=swap" rel="stylesheet">
    <!-- POS custom styles -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/pos_styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/split-payment.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pos_draft_orders.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pos-cart-thumbnails.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/line-item-discount-enhanced.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom-dialogs.css') }}">
<style>
        :root {
            /* Base colors - enhanced for better contrast and visual appeal */
            --primary-color: #6366f1; /* Indigo */
            --primary-color-dark: #4f46e5;
            --primary-color-light: #818cf8;
            --primary-color-rgb: 99, 102, 241; /* RGB values for primary color */
            --secondary-color: #f59e0b; /* Amber */
            --background-color: #0f172a; /* Dark blue/slate */
            --sidebar-bg: #1e293b;
            --card-bg: #1e293b;
            --card-bg-hover: #2d3748;
            --text-color: #f8fafc;
            --text-muted: #94a3b8;
            --sidebar-text: #cbd5e1;
            --nav-hover: rgba(99, 102, 241, 0.15);
            --border-color: rgba(255, 255, 255, 0.1);
            --modal-bg: rgba(0, 0, 0, 0.8);

            /* Status colors */
            --danger-color: #ef4444;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;

            /* Shadows */
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4);

            /* Transitions */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
        }

        /* Enhanced Sidebar styling */
        .pos-sidebar {
            background-color: var(--sidebar-bg);
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            padding: 20px;
            margin-bottom: 20px;
            height: calc(100vh - 100px);
            overflow-y: auto;
            border: 1px solid var(--border-color);
            transition: box-shadow var(--transition-normal);
        }

        .pos-sidebar:hover {
            box-shadow: var(--shadow-lg);
        }

        .sidebar-section {
            margin-bottom: 25px;
            position: relative;
        }

        .sidebar-section:after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, var(--border-color), transparent);
        }

        .sidebar-title {
            color: var(--text-color);
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            position: relative;
        }

        .sidebar-title:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 50px;
            height: 2px;
            background: var(--primary-color);
        }

        /* Card font size reduction */
        .card-title {
            font-size: 1.2rem; /* 20% smaller than default h5 */
        }

        .card-body {
            font-size: 0.8rem; /* 20% smaller than default body text */
        }

        .card-body h6 {
            font-size: 0.96rem; /* 20% smaller than default h6 */
        }

        .card-body .btn {
            font-size: 0.8rem; /* 20% smaller buttons in cards */
        }

        /* Product title container with tooltip */
        .product-title-container {
            position: relative;
            cursor: pointer;
        }

        .product-card .card-title {
            position: relative;
            cursor: pointer;
        }

        .product-card .card-title:hover::after {
            content: attr(title);
            position: absolute;
            top: -5px;
            left: 0;
            transform: translateY(-100%);
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            max-width: 250px;
            z-index: 100;
            word-wrap: break-word;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            white-space: normal;
            line-height: 1.2;
        }

        .product-title-container:hover .title-tooltip {
            display: block;
        }

        .title-tooltip {
            display: none;
            position: absolute;
            top: -5px;
            left: 0;
            transform: translateY(-100%);
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            max-width: 250px;
            z-index: 100;
            word-wrap: break-word;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        /* Product card styles */
        .product-card {
            transition: transform 0.3s, box-shadow 0.3s, background-color 0.2s;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-color: rgba(45, 45, 61, 0.7);
            height: 100%;
            font-size: 0.9rem;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            cursor: pointer; /* Add cursor pointer to indicate interactivity */
        }

        /* Removed hover effect */

        /* Enhanced stock level indicators with animation */
        .stock-indicator {
            position: relative;
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-card:hover .stock-indicator {
            transform: scale(1.2);
            box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
        }

        .stock-high {
            background-color: #10b981;
            box-shadow: 0 0 3px #10b981;
        }

        .stock-medium {
            background-color: #f59e0b;
            box-shadow: 0 0 3px #f59e0b;
        }

        .stock-low {
            background-color: #ef4444;
            box-shadow: 0 0 3px #ef4444;
            animation: pulse 2s infinite;
        }

        /* Style for out of stock items that can still be added to cart */
        .out-of-stock-btn {
            background-color: #ef4444;
            border-color: #dc2626;
        }

        .out-of-stock-btn:hover {
            background-color: #dc2626;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .product-card:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
            opacity: 0;
            transition: opacity 0.3s;
        }

        /* Removed hover effect for card after element */

        .card-img-container {
            height: 140px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(30, 30, 40, 0.8);
            padding: 10px;
            margin-bottom: 5px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            position: relative;
            cursor: zoom-in;
        }

        .card-img-top {
            max-height: 100%;
            max-width: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        /* Image hover zoom effect */
        .card-img-container:hover .card-img-top {
            transform: scale(1.1);
        }

        /* Large image preview on hover */
        .card-img-container:hover::after,
        .card-img-container.force-hover::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            pointer-events: none;
            animation: fadeIn 0.2s ease-in-out;
        }

        .card-img-container:hover::before,
        .card-img-container.force-hover::before {
            content: '';
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
            max-width: 600px;
            max-height: 600px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            z-index: 1001;
            pointer-events: none;
            background-image: var(--hover-image-url);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            animation: zoomIn 0.3s ease-in-out;
        }

        /* Animations for image hover */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes zoomIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        /* Focus styles for keyboard accessibility */
        .card-img-container:focus {
            outline: 2px solid var(--primary-color);
            box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.3);
        }

        /* Enhanced grid layout for better results display */
        #productList {
            width: 100%;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
            padding-bottom: 20px;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) rgba(0, 0, 0, 0.1);
        }

        #productList::-webkit-scrollbar {
            width: 8px;
        }

        #productList::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        #productList::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: 10px;
        }

        #productCardsContainer {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            width: 100%;
            padding: 15px 15px 60px 15px; /* Added bottom padding to ensure buttons are visible */
        }

        /* Category product grid styling - matches main search grid */
        .category-product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            width: 100%;
            padding: 10px;
            overflow: visible;
        }

        /* Removed hover effects for category product grid items */

        .product-grid-item {
            display: flex;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            min-height: 350px; /* Ensure consistent height */
            width: 100%;
        }

        /* Removed hover effect */

        /* Improved title display - ensure 2 lines with proper wrapping */
        .product-card .card-title {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.2;
            min-height: 2.4rem;
            max-height: 2.4rem;
            font-weight: 600;
            margin-bottom: 8px;
            position: relative;
            transition: color 0.2s ease;
            word-wrap: break-word;
            word-break: break-word;
            hyphens: auto;
            text-overflow: ellipsis;
        }

        /* Removed hover effect for card title */

        /* Enhanced quantity input */
        .product-card .qty-input {
            width: 50px;
            text-align: center;
            padding: 4px;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background-color: rgba(0, 0, 0, 0.2);
            color: var(--text-color);
            transition: border-color 0.2s ease, background-color 0.2s ease;
        }

        .product-card .qty-input:focus {
            border-color: var(--primary-color);
            background-color: rgba(0, 0, 0, 0.3);
            outline: none;
        }


        /* Enhanced variant selection styling */
        .select-variant-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease !important;
        }

        /* Variant name styling */
        .variant-name {
            color: #adb5bd;
            font-style: italic;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 5px;
        }

        /* Price section styling */
        .price-section {
            font-weight: bold;
            color: var(--primary-color-light);
        }

        /* Stock section styling */
        .stock-section {
            font-size: 0.85rem;
            color: #adb5bd;
        }

        .select-variant-btn:after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                rgba(255,255,255,0) 0%,
                rgba(255,255,255,0.2) 50%,
                rgba(255,255,255,0) 100%);
            transition: all 0.6s ease;
        }

        .select-variant-btn:hover:after {
            left: 100%;
        }

        .select-variant-btn i {
            transition: transform 0.3s ease;
        }

        .select-variant-btn:hover i {
            transform: translateX(3px);
        }

        /* Improved add to cart button */
        .add-to-cart-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease !important;
        }

        .add-to-cart-btn:after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                rgba(255,255,255,0) 0%,
                rgba(255,255,255,0.2) 50%,
                rgba(255,255,255,0) 100%);
            transition: all 0.6s ease;
        }

        .add-to-cart-btn:hover:after {
            left: 100%;
        }

        .add-to-cart-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .add-to-cart-btn:disabled:after {
            display: none;
        }

        .add-to-cart-btn i {
            transition: transform 0.3s ease;
        }

        .add-to-cart-btn:hover:not(:disabled) i {
            transform: translateY(-3px);
        }

        /* Enhanced results counter styling */
        .results-counter {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.95rem;
            color: var(--text-color);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 15px 20px !important;
            margin-bottom: 25px;
            background-color: rgba(30, 41, 59, 0.8);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
            transition: all 0.3s ease;
            position: sticky;
            top: 0;
            z-index: 10;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        .results-counter:hover {
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .results-counter .badge {
            font-size: 1.1rem;
            padding: 6px 14px;
            margin-right: 12px;
            border-radius: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
            box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
            font-weight: 600;
            transition: transform 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .results-counter .badge::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .results-counter:hover .badge::after {
            opacity: 1;
        }

        .results-counter .badge:hover {
            transform: translateY(-2px) scale(1.05);
        }

        .results-counter .results-actions {
            display: flex;
            gap: 12px;
        }

        .results-counter .btn-sm {
            padding: 6px 14px;
            font-size: 0.85rem;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-weight: 500;
            letter-spacing: 0.2px;
        }

        .results-counter .btn-sm:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .results-counter .btn-sm.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .results-counter .btn-sm.active::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 2px;
            background-color: var(--primary-color-light);
            border-radius: 2px;
        }

        /* Quicklink delete button */
        .remove-quicklink-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #e74c3c;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            cursor: pointer;
            z-index: 10;
        }

        /* Saved search delete button (adjusted to match quicklink style) */
        .remove-saved-search-btn {
            position: absolute;
            top: 5px; /* Match quicklink */
            right: 5px; /* Match quicklink */
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 9px;
            color: #fff;
            background-color: #e74c3c;
            border-radius: 50%;
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
            cursor: pointer;
            z-index: 10;
            opacity: 0.9;
            transition: all 0.2s;
        }

        .remove-saved-search-btn:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .remove-quicklink-btn i,
        .remove-saved-search-btn i {
            font-size: 8px;
            line-height: 1;
        }

        /* Saved searches layout */
        #savedSearchCards {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .saved-search-container {
            position: relative;
            margin-bottom: 8px;
            margin-right: 8px;
        }

        .load-saved-search-btn {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 30px;
            border: none;
            position: relative;
            transition: all 0.2s;
            margin: 0 !important;
            background-color: #4a90e2;
            color: white;
            font-weight: 500;
            letter-spacing: 0.2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.15);
            text-transform: capitalize;
        }

        .load-saved-search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 6px rgba(0,0,0,0.2);
            background-color: #357abd;
        }

        .quicklink-product-name {
            font-size: 0.88rem !important; /* 20% smaller than original 1.1rem */
            font-weight: 600;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Enhanced Card styling */
        .card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: transform var(--transition-normal), box-shadow var(--transition-normal);
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-3px);
        }

        .card-body {
            padding: 22px;
        }

        .card-title {
            color: var(--text-color);
            font-weight: 600;
            position: relative;
            padding-bottom: 8px;
            margin-bottom: 16px;
        }

        .card-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background: var(--primary-color);
        }

/* Home button styling - moved to top left */
.home-button {
    position: fixed;
    top: 12px; /* Centered vertically with tabs */
    left: 15px; /* Slightly more margin from the edge */
    z-index: 1100; /* Increased to appear above tabs */
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    text-decoration: none;
    opacity: 1; /* Fully visible */
}

.home-button:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.home-button:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.home-button i {
    font-size: 1.1rem;
    color: white;
}

        .container-fluid {
            padding-top: 20px;
        }

        /* Held Sales Styling */
        .held-sale-item {
            margin-bottom: 10px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-color: #2c3e50;
        }

        .held-sale-header {
            padding: 10px;
        }

        .held-sale-info {
            margin-bottom: 10px;
        }

        .held-sale-actions {
            display: flex;
            gap: 8px;
        }

        .held-sale-items {
            padding: 0 10px 10px 10px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sale-items-list {
            max-height: 200px;
            overflow-y: auto;
            border-radius: 4px;
        }

        .sale-items-list .list-group-item {
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.9);
            color: #333;
            border: none;
            margin-bottom: 2px;
        }

        .toggle-items-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            transition: all 0.2s ease;
        }

        .toggle-items-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .badge.bg-info {
            background-color: #3498db !important;
        }

        .badge.bg-success {
            background-color: #2ecc71 !important;
        }

        /* Enhanced Tabs Styling */
        .pos-tabs-container {
            width: 100%;
            background-color: rgba(15, 23, 42, 0.95); /* Slightly transparent dark background */
            padding: 0 20px 0 60px; /* Increased left padding to make room for home icon */
            box-shadow: var(--shadow-md);
            position: sticky;
            top: 0;
            z-index: 100;
            height: 60px;
            backdrop-filter: blur(10px); /* Modern glass effect */
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
        }

        .pos-tabs-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .pos-tab-item {
            padding: 0 25px;
            color: var(--text-muted);
            cursor: pointer;
            font-weight: 600;
            font-size: 15px;
            transition: all var(--transition-normal);
            border-bottom: 3px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .pos-tab-item:before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
            transform: translateX(-100%);
            transition: transform var(--transition-normal);
        }

        .pos-tab-item:hover {
            color: var(--text-color);
            background-color: rgba(255, 255, 255, 0.05);
        }

        .pos-tab-item:hover:before {
            transform: translateX(0);
        }

        .pos-tab-item.active {
            color: var(--primary-color);
            background-color: rgba(99, 102, 241, 0.1);
        }

        .pos-tab-item.active:before {
            transform: translateX(0);
        }

        .pos-tab-item i {
            font-size: 18px;
            transition: transform var(--transition-fast);
        }

        .pos-tab-item:hover i {
            transform: translateY(-2px);
        }

        .direct-link {
            margin-left: auto;
            padding: 0;
        }

        .direct-link a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            color: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            transition: all 0.3s ease;
            margin: 5px 0;
        }

        .direct-link a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }

.pos-tabs-content {
    position: relative;
    height: calc(100vh - 60px);
    min-height: 0;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.pos-tab-pane {
    display: none;
    height: 100%;
    padding: 0;
    margin: 0;
}

.pos-tab-pane.active {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0;
    margin: 0;
}

/* Buylist iframe styling */
.buylist-iframe-container {
    width: 100%;
    height: 100vh;
    flex: 1 1 auto;
    overflow: hidden;
    padding: 0;
    margin: 0;
    position: relative;
    display: flex;
    flex-direction: column;
    top: 0;
    left: 0;
}

#buylistIframe {
    width: 100%;
    height: 100vh;
    border: none;
    background-color: #ffffff;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
}

        /* Loading indicator for iframe */
        .iframe-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(26, 26, 46, 0.9);
            z-index: 10;
        }

        .iframe-loading p {
            color: #ffffff;
            font-size: 18px;
            margin-top: 20px;
            font-weight: 500;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Card Grader Styling */
        .card-grader-container {
            background-color: #243447;
            color: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .card-grader-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .card-grader-header h1 {
            font-size: 2.5rem;
            margin-left: 0.5rem;
            font-weight: 300;
        }

        .star-icon {
            font-size: 2.5rem;
            color: white;
        }

        .header-divider {
            height: 2px;
            background-color: #3498db;
            margin: 1.5rem 0;
            opacity: 0.5;
        }

        .upload-section {
            background-color: #2c3e50;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .choose-files-btn {
            background-color: #3498db;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            display: inline-block;
            transition: background-color 0.3s;
        }

        .choose-files-btn:hover {
            background-color: #2980b9;
        }

        .file-input-wrapper input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-list-item {
            background-color: #2c3e50;
            border: 1px solid #3498db;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .file-list-item i {
            margin-right: 0.5rem;
            color: #3498db;
        }

        .file-size {
            background-color: #34495e;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            color: #a0aec0;
        }

        .results-section {
            margin-top: 2rem;
        }

        .grade-badge {
            display: inline-block;
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border-radius: 50%;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .grade-badge.high {
            background-color: #2ecc71;
            color: white;
        }

        .grade-badge.medium {
            background-color: #f39c12;
            color: white;
        }

        .grade-badge.low {
            background-color: #e74c3c;
            color: white;
        }

        .detail-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .detail-btn:hover {
            background-color: #2980b9;
        }

        /* Enhanced Modal Styling */
        .modal {
            display: none;
            position: fixed;
            z-index: 1050;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: var(--card-bg);
            margin: 5% auto;
            width: 90%;
            max-width: 600px; /* 75% wider than default */
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            position: relative;
            animation: modalFadeIn 0.3s ease-out;
            overflow: hidden;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-content h5 {
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            position: relative;
        }

        .modal-content h5:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 50px;
            height: 2px;
            background: var(--primary-color);
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            color: var(--text-muted);
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            transition: all var(--transition-fast);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.05);
        }

        .close:hover {
            color: var(--danger-color);
            background-color: rgba(239, 68, 68, 0.1);
            transform: rotate(90deg);
        }

        .modal-backdrop {
            z-index: 9998;
        }

        #purchaseScansModal.show {
            display: block;
            background-color: rgba(0, 0, 0, 0.5);
        }

        #purchaseScansModal .modal-content {
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        #purchaseScansModal .modal-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        #purchaseScansModal .modal-body {
            padding: 2rem;
        }

        #purchaseScansModal .modal-footer {
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--border-color);
        }

        #purchaseScansModal .card {
            border-radius: 8px;
            box-shadow: var(--shadow-md);
        }

        #purchaseScansModal .form-control-lg {
            height: 60px;
            font-size: 1.25rem;
        }

        /* Error message styling */
        .iframe-error {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(26, 26, 46, 0.95);
            z-index: 10;
        }

        .error-content {
            background-color: #1e293b;
            border-radius: 8px;
            padding: 30px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .error-content i {
            font-size: 48px;
            color: #f59e0b;
            margin-bottom: 20px;
        }

        .error-content h3 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 22px;
        }

        .error-content p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
        }

        .error-content ul {
            text-align: left;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            padding-left: 20px;
        }

        .error-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        /* Enhanced Form Controls */
        .form-control {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-color);
            padding: 10px 15px;
            transition: all var(--transition-fast);
        }

        .form-control:focus {
            background-color: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-color-light);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.25);
            outline: none;
        }

        .form-control::placeholder {
            color: var(--text-muted);
            opacity: 0.7;
        }

        /* Enhanced Button Styling */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 8px 16px;
            transition: all var(--transition-fast);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn:after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform 0.5s, opacity 1s;
        }

        .btn:active:after {
            transform: scale(0, 0);
            opacity: 0.3;
            transition: 0s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-color-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .btn-secondary {
            background-color: #475569;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #334155;
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: #0d9488;
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .btn-info {
            background-color: var(--info-color);
            color: white;
        }

        .btn-info:hover {
            background-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        /* Enhanced Cart Styling */
        .list-group-item {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            margin-bottom: 8px;
            border-radius: 8px;
            transition: all var(--transition-fast);
        }

        /* Sticky Cart */
        .sticky-cart {
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }

        /* Payment section styling */
        .payment-section {
            border-top: 1px solid var(--border-color);
            margin-top: auto;
            box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.1);
        }

        .payment-buttons-primary, .payment-buttons-secondary {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .payment-button {
            min-width: 80px;
            padding: 8px 12px;
            font-size: 0.85rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .payment-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* Cart list container */
        .cart-list-container {
            flex: 1;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background-color: rgba(255, 255, 255, 0.02);
            padding: 5px;
        }

        /* Customers Tab Styles */
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .custom-toast {
            min-width: 300px;
            margin-bottom: 10px;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideIn 0.3s ease-out forwards;
            border-left: 4px solid;
        }

        .toast-success {
            background-color: rgba(46, 204, 113, 0.2);
            border-color: #2ecc71;
        }

        .toast-error {
            background-color: rgba(231, 76, 60, 0.2);
            border-color: #e74c3c;
        }

        .toast-warning {
            background-color: rgba(241, 196, 15, 0.2);
            border-color: #f1c40f;
        }

        .toast-info {
            background-color: rgba(52, 152, 219, 0.2);
            border-color: #3498db;
        }

        .toast-icon {
            margin-right: 12px;
            font-size: 20px;
        }

        .toast-content {
            flex-grow: 1;
        }

        .toast-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: white;
        }

        .toast-message {
            color: rgba(255, 255, 255, 0.8);
        }

        .toast-close {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            font-size: 18px;
            padding: 0;
            margin-left: 10px;
        }

        .toast-close:hover {
            color: white;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }

        /* Sortable table headers */
        th.sortable {
            cursor: pointer;
            position: relative;
            padding-right: 20px;
        }

        th.sortable:after {
            content: '↕';
            position: absolute;
            right: 5px;
            color: var(--text-muted);
        }

        th.sortable.asc:after {
            content: '↑';
            color: var(--primary-color);
        }

        th.sortable.desc:after {
            content: '↓';
            color: var(--primary-color);
        }

        /* Customer card styles */
        .bg-darker {
            background-color: #0f172a;
        }

        /* Cart list container scrollbar */
        .cart-list-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #0f172a;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) rgba(0, 0, 0, 0.1);
        }

        .cart-list-container::-webkit-scrollbar {
            width: 6px;
        }

        .cart-list-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        .cart-list-container::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: 10px;
        }

        .list-group-item:hover {
            background-color: var(--card-bg-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .cart-item {
            padding: 10px;
        }

        .cart-remove-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .cart-remove-btn:hover {
            background-color: var(--danger-color);
            color: white;
            transform: scale(1.1);
        }

        .item-title {
            font-size: 0.95rem;
            margin-bottom: 4px;
            color: var(--text-color);
        }

        .item-variant {
            color: var(--text-muted);
        }

        .cart-item-controls {
            margin-top: 10px;
            background-color: rgba(255, 255, 255, 0.03);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .quantity-control {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            overflow: hidden;
            display: flex;
            align-items: center;
            border: 1px solid var(--border-color);
        }

        .quantity-btn {
            background-color: transparent;
            border: none;
            color: var(--text-muted);
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .quantity-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .edit-price-btn {
            background-color: rgba(99, 102, 241, 0.1);
            border: none;
            color: var(--primary-color);
            width: 28px;
            height: 28px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all var(--transition-fast);
        }

        .edit-price-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .item-total {
            font-size: 0.9rem;
        }

        .item-total-price {
            color: var(--success-color);
        }

        /* Discount Section */
        .discount-header {
            border-bottom: 1px solid var(--border-color);
            transition: background-color var(--transition-fast);
        }

        .discount-header:hover {
            background-color: rgba(255, 255, 255, 0.03);
        }

        #toggleDiscountBtn {
            transition: all var(--transition-fast);
        }

        #toggleDiscountBtn.active {
            background-color: var(--primary-color);
            color: white;
        }

        #toggleDiscountBtn.active .fa-plus:before {
            content: "\f068"; /* fa-minus */
        }

        /* Product Grid Styles */
        .product-grid-container {
            margin-bottom: 20px;
        }

        .product-card {
            transition: transform 0.2s, box-shadow 0.2s;
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            height: 100%;
            border-radius: 8px;
            overflow: hidden;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .card-img-container {
            height: 140px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid var(--border-color);
            padding: 10px;
        }

        .card-img-top {
            max-height: 140px;
            max-width: 100%;
            object-fit: contain;
        }

        .card-title {
            font-size: 0.9rem;
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: 0.5rem;
            height: 2.4rem;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2; /* Standard property for compatibility */
            -webkit-box-orient: vertical;
        }

        .price-tag {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .stock-tag {
            color: var(--text-muted);
            font-size: 0.8rem;
        }

        /* Enhanced Payment Buttons */
        .payment-section {
            margin-top: 30px;
            border-top: 1px solid var(--border-color);
            padding-top: 20px;
        }

        .payment-buttons-primary {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .payment-buttons-secondary {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .payment-button {
            padding: 15px;
            border-radius: 10px;
            border: none;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .payment-button:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
            opacity: 0;
            transition: opacity var(--transition-normal);
        }

        .payment-button:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
        }

        .payment-button:hover:before {
            opacity: 1;
        }

        .payment-button i {
            font-size: 1.5rem;
            margin-bottom: 5px;
            transition: transform var(--transition-fast);
        }

        .payment-button:hover i {
            transform: scale(1.2);
        }

        .payment-button span {
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Customer details card styling */
        .customer-details-card {
            background-color: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            border: 1px solid var(--border-color);
            padding: 15px;
            margin-bottom: 20px;
            transition: all var(--transition-normal);
        }

        .customer-details-card:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color-light);
        }

        .customer-details-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .customer-details-header h5 {
            margin: 0;
            color: var(--text-color);
            font-weight: 600;
        }

        .customer-detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .detail-label {
            color: var(--text-muted);
        }

        .detail-value {
            color: var(--text-color);
            font-weight: 500;
        }

        .credit-info {
            background-color: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            border: 1px solid var(--border-color);
        }

        .total-balance {
            font-weight: 600;
            margin-top: 5px;
            padding-top: 5px;
            border-top: 1px dashed var(--border-color);
        }

        .total-balance .detail-value {
            color: var(--success-color);
        }

        /* Enhanced Product Grid Styling */
        .product-card {
            transition: transform var(--transition-normal), box-shadow var(--transition-normal), background-color var(--transition-fast);
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: var(--card-bg);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
            border-radius: 12px;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            background-color: var(--card-bg-hover);
            z-index: 1;
        }

        .product-card:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
            opacity: 0;
            transition: opacity var(--transition-normal);
        }

        .product-card:hover:after {
            opacity: 1;
        }

        .product-image-container {
            height: 160px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.2);
            padding: 10px;
            position: relative;
        }

        .product-image {
            max-height: 140px;
            max-width: 100%;
            object-fit: contain;
            transition: transform var(--transition-normal);
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .product-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 5px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            height: 2.5rem;
            color: var(--text-color);
        }

        .product-info {
            color: var(--text-muted);
            font-size: 0.8rem;
            margin-bottom: 10px;
        }

        .variant-info {
            background-color: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: background-color var(--transition-fast);
        }

        .product-card:hover .variant-info {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .variant-title {
            font-size: 0.85rem;
            color: var(--text-color);
            font-weight: 500;
        }

        .variant-price {
            font-weight: 600;
            color: var(--success-color);
            font-size: 0.95rem;
        }

        .variant-stock {
            font-size: 0.75rem;
            margin-top: 3px;
            color: var(--text-muted);
        }

        /* Improved buttons in product cards */
        .product-card .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transition: all var(--transition-fast);
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        .product-card .btn-primary:hover {
            background-color: var(--primary-color-dark);
            border-color: var(--primary-color-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .product-card .form-control {
            border-color: var(--border-color);
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
        }

        .product-card .form-control:focus {
            border-color: var(--primary-color-light);
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
        }

        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: var(--modal-bg);
            padding-top: 60px;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: var(--card-bg);
            margin: 5% auto;
            padding: 30px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            width: 50%;
            max-width: 600px;
            color: var(--text-color);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        /* Modal close button */
        .modal .close {
            position: absolute;
            right: 20px;
            top: 15px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .modal .close:hover,
        .modal .close:focus {
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
<!-- Home button to return to appropriate dashboard -->
<a href="{{ url_for('pos.pos') }}" class="home-button" title="Return to POS">
    <i class="fas fa-home"></i>
</a>

<!-- Tabs Navigation -->
<div class="pos-tabs-container">
    <ul class="pos-tabs-nav">
        <li class="pos-tab-item active" data-tab="pos-tab">
            <i class="fas fa-cash-register"></i> POS System
        </li>
        <li class="pos-tab-item" data-tab="buylist-tab">
            <i class="fas fa-list-alt"></i> Buylist Builder
        </li>
        <li class="pos-tab-item" data-tab="psa-tab">
            <i class="fas fa-certificate"></i> PSA Checker
        </li>
        <li class="pos-tab-item" data-tab="card-grader-tab">
            <i class="fas fa-star"></i> Card Grader
        </li>
        <li class="pos-tab-item" data-tab="buy-tab" id="buyTabItem" style="display: none;">
            <i class="fas fa-shopping-basket"></i> Buy
        </li>
        <li class="pos-tab-item" data-tab="warehouse-tab" id="warehouseTabItem" style="display: none;">
            <i class="fas fa-warehouse"></i> Warehouse
        </li>
    </ul>
</div>

<!-- Tab Content -->
<div class="pos-tabs-content">
    <!-- POS Tab Content -->
    <div class="pos-tab-pane active" id="pos-tab">
        <div class="container-fluid" style="padding-top: 15px;">
            <div class="row">
        <!-- Main Content -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex gap-2 mb-3">
                        <div class="input-group" style="width: 70%;">
                            <input type="text" id="searchInput" class="form-control" placeholder="Search by name or scan barcode..." autocomplete="off">
                            <button class="btn btn-primary" id="searchButton">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                        <button class="btn btn-warning" id="addCustomItemBtn">Custom Item</button>
                        <button class="btn btn-success btn-sm" id="addNewQuicklinkBtn">
                            <i class="fas fa-plus"></i> Add Quicklink
                        </button>
                    </div>
        <div class="form-check mb-3">
            <input type="checkbox" class="form-check-input" id="inStockOnly" checked>
            <label class="form-check-label" for="inStockOnly">In Stock Only</label>
        </div>

        <div class="category-filter-container mb-3">
            <div class="category-filter-header" id="headingVendorFilter">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 text-primary">Filter by Vendor & Product Type</h6>
                    <button class="btn btn-link category-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="#collapseVendorFilter" aria-expanded="false" aria-controls="collapseVendorFilter">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div id="collapseVendorFilter" class="collapse" aria-labelledby="headingVendorFilter">
                <div class="category-filter-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <select id="vendorSelect" class="form-control">
                                <option value="">Select a vendor</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <select id="productTypeSelect" class="form-control" disabled>
                                <option value="">Select vendor first</option>
                            </select>
                        </div>
                    </div>
                    <div id="saveSearchContainer" class="mb-3" style="display: none;">
                        <div class="d-flex align-items-center">
                            <button id="saveSearchBtn" class="btn btn-sm btn-primary me-2">
                                <i class="fas fa-save"></i> Save this search
                            </button>
                            <span id="saveSearchMessage" class="text-success" style="display: none;">Search saved!</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <input type="text" id="categoryProductFilter" class="form-control" placeholder="Type to filter products...">
                    </div>
                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="categoryInStockOnly" checked>
                        <label class="form-check-label" for="categoryInStockOnly">In Stock Only</label>
                    </div>
                    <div id="categoryProductsContainer">
                        <p class="text-muted">Select a vendor and product type to view products</p>
                        <ul class="list-group" id="categoryProductList"></ul>
                    </div>
                </div>
            </div>
        </div>

            <!-- Saved Searches Section -->
        <div class="card mb-3" id="savedSearchesSection" style="display: none;">
            <div class="card-body">
                <h5 class="card-title">Saved Searches</h5>
                <div class="row g-2" id="savedSearchCards" style="flex-wrap: wrap; overflow-x: visible;">
                    <!-- Saved Searches will be loaded here by JS -->
                </div>
            </div>
        </div>

        <!-- Quick Access Section -->
        <div class="card mb-3" id="quickAccessSection" style="display: none;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">Quick Access</h5>
                </div>
                <div class="row g-2" id="quicklinkCards" style="flex-wrap: wrap; overflow-x: visible;">
                    <!-- Saved Searches will be prepended here by JS -->

                    <!-- Original Quicklinks -->
                    {% for i in range(4) %}
                    <div class="col-md-3 col-sm-6 col-6 mb-2 quicklink-container">
                        <div class="product-type-card" data-quicklink-number="{{ i }}">
                            <div class="remove-quicklink-btn" title="Remove Quicklink">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="card-body">
                                <p class="quicklink-product-name" style="font-size: 1rem; font-weight: 600; margin-bottom: 10px; color: #ecf0f1;">{{ quicklinks[i] if quicklinks[i] else "No product selected" }}</p>
                                <div class="mt-auto">
                                    {% if quicklinks[i] %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div class="d-flex align-items-center">
                                            <button class="btn btn-sm btn-outline-primary select-quicklink-btn" title="Edit product">
                                                <i class="fas fa-pencil-alt"></i>
                                            </button>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <label for="quicklink-qty-{{ i }}" class="me-2 text-white-50">Qty:</label>
                                            <div class="quantity-control">
                                                <button class="btn btn-sm btn-outline-secondary quantity-btn quantity-decrease">-</button>
                                                <input type="number" id="quicklink-qty-{{ i }}" class="form-control form-control-sm quicklink-qty" value="1" min="1" style="width: 40px; text-align: center;">
                                                <button class="btn btn-sm btn-outline-secondary quantity-btn quantity-increase">+</button>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn btn-secondary w-100 add-quicklink-item-btn">{{ quicklinks[i] }}</button>
                                    {% else %}
                                    <button class="btn btn-primary w-100 select-quicklink-btn">Select Product</button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div id="productList" class="product-grid-container"></div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card sticky-cart">
                <div class="card-body">
                    <!-- Cart title, customer search, and width controls on same row -->
                    <div class="d-flex justify-content-between align-items-center mb-2 gap-2">
                        <h5 class="card-title mb-0">
                            Cart <span id="cartItemCount" class="badge bg-primary">0</span>
                        </h5>
                        <div class="customer-search-container position-relative flex-grow-1 mx-2">
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control" id="inlineCustomerSearch" placeholder="Search customer..." autocomplete="off">
                                <button class="btn btn-primary" id="createCustomerBtn">
                                    <i class="fas fa-user-plus"></i>
                                </button>
                            </div>
                            <div id="inlineCustomerSearchResults" class="position-absolute w-100 mt-1 d-none" style="z-index: 1000; max-height: 300px; overflow-y: auto; background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 0.375rem;">
                                <!-- Search results will appear here -->
                            </div>
                        </div>
                        <div class="cart-width-controls btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary cart-width-btn" data-width="35">35%</button>
                            <button class="btn btn-outline-secondary cart-width-btn" data-width="40">40%</button>
                            <button class="btn btn-outline-secondary cart-width-btn" data-width="45">45%</button>
                            <button class="btn btn-outline-secondary cart-width-btn" data-width="50">50%</button>
                        </div>
                    </div>

                    <!-- Action buttons row -->
                    <div class="d-flex flex-wrap gap-2 mb-3">
                        <button class="btn btn-info" id="retrieveSalesBtn">Retrieve Sales</button>
                        <button class="btn btn-info" id="viewLayawaysBtn">
                            <i class="fas fa-list"></i>
                            <span>View Layaways</span>
                        </button>
                        <button class="btn btn-info" id="viewDraftOrdersBtn">
                            <i class="fas fa-file-alt"></i>
                            <span>Draft Orders</span>
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-info dropdown-toggle" type="button" id="instoreOrdersDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-shopping-cart"></i>
                                <span>Instore Orders</span>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="instoreOrdersDropdown" id="instoreOrdersList">
                                <li><a class="dropdown-item text-muted">Loading orders...</a></li>
                            </ul>
                        </div>
                        <button class="btn btn-outline-danger" id="emptyCartBtn">
                            <i class="fas fa-trash-alt me-1"></i>Reset Cart
                        </button>
                    </div>

                    <style>
                        /* Styles for customer search results */
                        .customer-result {
                            cursor: pointer;
                            transition: background-color 0.2s;
                        }
                        .customer-result:hover {
                            background-color: #3a4a5e !important;
                        }
                        .select-customer-btn {
                            z-index: 10;
                        }
                        .customer-result .select-customer-btn:hover {
                            background-color: #ffffff !important;
                            color: #2d3748 !important;
                        }
                    </style>

                    <div id="customerDetails" class="customer-details-card mb-4" style="display: none;">
                        <div class="customer-details-header d-flex justify-content-between align-items-center">
                            <h5>Customer Details</h5>
                            <button id="removeCustomerBtn" class="btn btn-sm btn-danger">
                                <i class="fas fa-user-times"></i> Remove
                            </button>
                        </div>
                        <div class="customer-details-content">
                            <p id="customerName" class="customer-detail-row"><span class="detail-label">Name:</span> <span class="detail-value"></span></p>
                            <p id="customerEmail" class="customer-detail-row"><span class="detail-label">Email:</span> <span class="detail-value"></span></p>
                            <div class="credit-info">
                                <p id="giftCardBalance" class="customer-detail-row"><span class="detail-label">Gift Card Balance:</span> <span class="detail-value"><span class="currency-symbol">$</span><span class="balance-amount">0.00</span></span></p>
                                <p id="storeCreditBalance" class="customer-detail-row"><span class="detail-label">Store Credit Balance:</span> <span class="detail-value"><span class="currency-symbol">$</span><span class="balance-amount">0.00</span></span></p>
                                <p id="totalCreditBalance" class="customer-detail-row total-balance"><span class="detail-label">Total Credit Balance:</span> <span class="detail-value"><span class="currency-symbol">$</span><span class="balance-amount">0.00</span></span></p>
                            </div>
                        </div>
                    </div>

                    <div class="cart-list-container mb-4" style="max-height: 40vh; overflow-y: auto;">
                        <ul class="list-group" id="cartList">
                            <li class="list-group-item">No items in cart</li>
                        </ul>
                    </div>

                    <div class="card mb-4">
                        <div class="card-body p-0">
                            <div class="d-flex justify-content-between align-items-center p-3 discount-header">
                                <h5 class="card-title mb-0">Discount</h5>
                                <button class="btn btn-sm btn-outline-primary" id="toggleDiscountBtn">
                                    <i class="fas fa-plus"></i> Add Discount
                                </button>
                            </div>
                            <div id="discountContent" class="p-3 pt-0" style="display: none;">
                                <div class="d-flex gap-2 mb-2">
                                    <input type="text" id="discount" class="form-control" placeholder="Enter discount...">
                                    <select id="discountType" class="form-control" style="width: 140px;">
                                        <option value="percent" selected>Percentage</option>
                                        <option value="amount">Fixed Amount</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <h6 class="mb-0">Subtotal:</h6>
                                <h6 class="mb-0">$<span id="subtotal">0.00</span></h6>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <h6 class="mb-0">Tax:</h6>
                                <h6 class="mb-0">$<span id="tax">0.00</span></h6>
                            </div>
                            <!-- Line item discounts are now combined with the regular discount -->
                            <div class="d-flex justify-content-between mb-2" id="discountRow">
                                <h6 class="mb-0">Discount:</h6>
                                <h6 class="mb-0 text-danger">-$<span id="discountAmount">0.00</span></h6>
                            </div>
                            <div class="d-flex justify-content-between">
                                <h5 class="mb-0">Total:</h5>
                                <h5 class="mb-0">$<span id="total">0.00</span></h5>
                            </div>
                        </div>
                    </div>

                    <div id="creditSection" class="mb-4" style="display: none;">
                        <div class="card">
                            <div class="card-body p-0">
                                <div id="giftCardSection" class="p-3 rounded-top border-bottom border-secondary">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">Gift Card: <span class="currency-symbol">$</span><span id="giftCardApplied">0.00</span></h6>
                                            <small class="text-muted" id="giftCardAvailable"></small>
                                        </div>
                                        <button id="applyGiftCardBtn" class="btn btn-sm btn-success">Apply</button>
                                    </div>
                                </div>
<div id="storeCreditSection" class="p-3 rounded-bottom">
    <div class="d-flex justify-content-between align-items-center mb-2">
        <div>
            <h6 class="mb-0">Store Credit: <span class="currency-symbol">$</span><span id="storeCreditApplied">0.00</span></h6>
            <small class="text-muted" id="storeCreditAvailable"></small>
        </div>
        <button id="applyStoreCreditBtn" class="btn btn-sm btn-success">Apply</button>
    </div>
</div>
                            </div>
                        </div>
                    </div>

                    <div class="payment-section sticky-bottom bg-dark py-3 mt-3" style="position: sticky; bottom: 0; z-index: 100;">
                        <h5 class="mb-3">Payment Options</h5>
                        <div class="payment-buttons-primary mb-3 d-flex flex-wrap gap-2">
                            <button class="payment-button btn-success btn-sm" id="cashBtn">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>Cash</span>
                            </button>
                            <button class="payment-button btn-primary btn-sm" id="cardBtn">
                                <i class="fas fa-credit-card"></i>
                                <span>Card</span>
                            </button>
                        </div>
                        <div class="payment-buttons-secondary d-flex flex-wrap gap-2">
                            <button class="payment-button btn-warning btn-sm" id="splitPaymentBtn">
                                <i class="fas fa-random"></i>
                                <span>Split</span>
                            </button>
                            <button class="payment-button btn-info btn-sm" id="layawayBtn">
                                <i class="fas fa-clock"></i>
                                <span>Layaway</span>
                            </button>
                            <button class="payment-button btn-sm" id="showReceiptBtn" style="background-color: #17a2b8; border-color: #17a2b8;">
                                <i class="fas fa-print"></i>
                                <span>Receipt</span>
                            </button>
                            <button class="payment-button btn-sm" id="payoutBtn" style="background-color: #e74c3c; border-color: #c0392b;">
                                <i class="fas fa-hand-holding-usd"></i>
                                <span>Payout</span>
                            </button>
                            <button class="payment-button btn-sm" id="holdSaleBtn" style="background-color: #8e44ad; border-color: #7d3c98;">
                                <i class="fas fa-pause"></i>
                                <span>Hold</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Variant Modal -->
<div id="variantModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Select a Variant</h5>
        <ul class="list-group" id="variantList"></ul>
    </div>
</div>

<!-- Quicklink Modal -->
<div id="quicklinkModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Select a Product for Quicklink</h5>
        <div class="row">
            <div class="col-md-6">
                <div class="input-group mb-3">
                    <input type="text" id="quicklinkSearchInput" class="form-control" placeholder="Search for a product..." autocomplete="off">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="button" id="quicklinkSearchBtn">Search</button>
                    </div>
                </div>
                <ul class="list-group" id="quicklinkProductList"></ul>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Quicklink</h5>
                        <input type="hidden" id="selectedQuicklinkNumber">
                        <input type="hidden" id="selectedQuicklinkProductId">
                        <p id="selectedQuicklinkProductName">No product selected</p>
                        <button class="btn btn-primary mt-2" id="saveQuicklinkBtn">Save Quicklink</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cash Payment Modal -->
<div id="cashPaymentModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Cash Payment</h5>
        <div>
            <h6>Total Amount Due: $<span id="totalAmountDue"></span></h6>
            <label for="amountGiven">Amount Given:</label>
            <input type="number" id="amountGiven" class="form-control" placeholder="Enter amount given">
            <h6 class="mt-3">Change Due: $<span id="changeDue">0.00</span></h6>
            <button class="btn btn-success mt-3" id="completeCashPaymentBtn">Complete Payment</button>
            <h6 class="mt-3">Gift Card Amount Used: $<span id="giftCardAmountUsed">0.00</span></h6>
        </div>
    </div>
</div>

<!-- Card Payment Modal -->
<div id="cardPaymentModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Card Payment</h5>
        <div>
            <h6>Total Amount Due: $<span id="totalCardAmountDue"></span></h6>
            <h6 class="mt-3">Gift Card Amount Used: $<span id="cardGiftCardAmountUsed">0.00</span></h6>
            <div class="d-flex justify-content-between mt-3">
                <button class="btn btn-primary" id="completeCardPaymentBtn">Complete Payment</button>
                <button class="btn btn-success" id="sendToCCMachineBtn">Send to Card Machine</button>
            </div>
        </div>
    </div>
</div>

<!-- Split Payment Modal -->
<div id="splitPaymentModal" class="modal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg" style="max-width: 225% !important; width: 225% !important;">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-info">Split Payment</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6>Total Amount Due: $<span id="totalSplitAmountDue"></span></h6>
                    </div>
                    <div class="col-md-6">
                        <h6>Gift Card Amount Used: $<span id="splitGiftCardAmountUsed">0.00</span></h6>
                    </div>
                </div>

                <div id="splitStoreCreditInfo" class="alert alert-info mt-3" style="display: none;"></div>
                <input type="hidden" id="splitStoreCreditAccountId" value="">

                <div class="split-payment-container mt-3 bg-secondary rounded p-3">
                    <div class="payment-methods-container">
                        <h6 class="text-white mb-3">Payment Methods</h6>
                        <div id="paymentMethodsList" class="mb-3">
                            <!-- Payment methods will be added here dynamically -->
                        </div>

                        <div class="mb-3">
                            <div class="d-flex mb-2">
                                <select id="paymentMethodType" class="form-control bg-dark text-white border-secondary me-2">
                                    <option value="cash">Cash</option>
                                    <option value="card">Card</option>
                                    <option value="storecredit">Store Credit</option>
                                </select>
                                <input type="number" id="paymentMethodAmount" class="form-control bg-dark text-white border-secondary" placeholder="Amount" step="0.01">
                            </div>
                            <div class="mt-2">
                                <button id="addPaymentMethodBtn" class="btn btn-info w-100">
                                    <i class="fas fa-plus me-1"></i> Add Payment Method
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="payment-summary mt-3 bg-dark p-3 rounded border border-secondary">
                        <h6 class="text-info">Payment Summary</h6>
                        <div class="d-flex justify-content-between">
                            <span>Total Due:</span>
                            <span>$<span id="splitTotalDue"></span></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Total Paid:</span>
                            <span>$<span id="splitTotalPaid">0.00</span></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Remaining:</span>
                            <span>$<span id="splitRemaining"></span></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" id="cancelSplitPaymentBtn">Cancel</button>
                <button type="button" id="completeSplitPaymentBtn" class="btn btn-success" onclick="completeSplitPayment()" disabled>
                    <i class="fas fa-check me-1"></i> Complete Order
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Creation Modal -->
<div id="createCustomerModal" class="modal" style="display:none; position: fixed; z-index: 1050; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.7);">
    <div class="modal-content" style="background-color: #2c3e50; color: #ffffff; margin: 10% auto; padding: 20px; border: 1px solid #444; width: 90%; max-width: 400px; border-radius: 8px;">
        <span class="close" style="float:right; font-size:28px; font-weight:bold; cursor:pointer; color:#fff;">&times;</span>
        <h5>Create New Customer</h5>
        <form id="createCustomerForm">
            <div class="mb-3">
                <input type="text" class="form-control" id="newCustomerFirstName" placeholder="First Name" required>
            </div>
            <div class="mb-3">
                <input type="text" class="form-control" id="newCustomerLastName" placeholder="Last Name" required>
            </div>
            <div class="mb-3">
                <input type="email" class="form-control" id="newCustomerEmail" placeholder="Email" required>
            </div>
            <div class="mb-3">
                <input type="number" class="form-control" id="newCustomerInitialCredit" placeholder="Initial Store Credit" min="0" step="0.01">
            </div>
            <button type="submit" class="btn btn-success w-100">Create Customer</button>
        </form>
        <div id="createCustomerMessage" class="mt-2"></div>
    </div>
</div>

<!-- Customer Search Modal -->
<div id="customerSearchModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Search Customer</h5>
<div class="input-group mb-3">
            <select id="customerSearchType" class="form-control">
                <option value="last_name">Last Name</option>
                <option value="email">Email</option>
            </select>
            <input type="text" id="customerSearchInput" class="form-control" placeholder="Enter customer's last name or email">
            <div class="input-group-append">
                <button class="btn btn-primary" id="searchCustomerBtn">Search</button>
            </div>
        </div>
        <ul id="customerSearchResults" class="list-group"></ul>
<div class="mt-3 text-center">
            <button class="btn btn-success" id="openCreateCustomerModalBtn">
                <i class="fas fa-user-plus"></i> Create New Customer
            </button>
        </div>
    </div>
</div>

<!-- Layaway Modal -->
<div id="layawayModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Create Layaway</h5>
        <div>
            <label for="layawayDeposit">Deposit Amount:</label>
            <input type="number" id="layawayDeposit" class="form-control" placeholder="Enter deposit amount">
            <button class="btn btn-info mt-3" id="createLayawayBtn">Create Layaway</button>
        </div>
    </div>
</div>

<!-- Payout Modal -->
<div id="payoutModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Process Payout</h5>

        <div class="payout-options mb-4">
            <h6>Select Payout Type</h6>
            <div class="d-flex gap-2">
                <button id="payoutBuylistBtn" class="btn btn-primary">
                    <i class="fas fa-list-alt me-2"></i>Buylist Payout
                </button>
                <button id="manualPayoutBtn" class="btn btn-secondary">
                    <i class="fas fa-hand-holding-usd me-2"></i>Manual Payout
                </button>
            </div>
        </div>

        <!-- Buylist Payout Section -->
        <div id="buylistPayoutSection" style="display: none;">
            <h6>Select Buylist to Pay Out</h6>

            <div class="list-group mb-3" id="buylistsList">
                <!-- Buylists will be loaded here -->
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading buylists...</p>
                </div>
            </div>

            <!-- Buylist Details Section -->
            <div id="buylistDetailsSection" style="display: none;">
                <h6>Buylist Details</h6>
                <div class="card mb-3">
                    <div class="card-body" id="buylistDetails">
                        <div class="row mb-2">
                            <div class="col-4 fw-bold">Order ID:</div>
                            <div class="col-8" id="buylistOrderId"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-4 fw-bold">Customer:</div>
                            <div class="col-8" id="buylistCustomer"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-4 fw-bold">Date:</div>
                            <div class="col-8" id="buylistDate"></div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-4 fw-bold">Amount:</div>
                            <div class="col-8">$<span id="buylistAmount"></span></div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="employeeName" class="form-label">Employee Name</label>
                    <input type="text" class="form-control" id="employeeName" value="{{ current_user.username }}">
                    <!-- Using till.id instead of till_id to match the main hidden input -->
                    <input type="hidden" id="payoutTillId" value="{{ till.id }}">
                </div>

                <div class="d-flex justify-content-end">
                    <button id="confirmPayoutBtn" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Confirm Payout
                    </button>
                </div>
            </div>
        </div>

        <!-- Manual Payout Section -->
        <div id="manualPayoutSection" style="display: none;">
            <h6>Manual Payout Details</h6>
            <div class="card mb-3">
                <div class="card-body">
                    <div class="mb-3">
                        <label for="manualPayoutAmount" class="form-label">Payout Amount</label>
                        <div class="input-group">
                            <span class="input-group-text currency-symbol">$</span>
                            <input type="number" class="form-control" id="manualPayoutAmount" step="0.01" min="0.01" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="manualPayoutStaffName" class="form-label">Staff Name</label>
                        <input type="text" class="form-control" id="manualPayoutStaffName" value="{{ current_user.username }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="manualPayoutNotes" class="form-label">Notes (Required)</label>
                        <textarea class="form-control" id="manualPayoutNotes" rows="3" placeholder="Enter detailed reason for this payout" required></textarea>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end">
                <button id="confirmManualPayoutBtn" class="btn btn-success">
                    <i class="fas fa-check me-2"></i>Confirm Manual Payout
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Held Sales Modal -->
<div id="heldSalesModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Held Sales</h5>
        <ul class="list-group" id="heldSalesList"></ul>
    </div>
</div>

<!-- Edit Price Modal -->
<div id="editPriceModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Edit Item Price</h5>
        <div class="form-group">
            <p id="editItemName" class="mb-3"></p>
            <label for="editItemPrice">New Price:</label>
            <input type="number" id="editItemPrice" class="form-control mb-3" placeholder="Enter new price" step="0.01">
            <input type="hidden" id="editItemId">
            <button id="saveItemPriceBtn" class="btn btn-primary">Save Price</button>
        </div>
    </div>
</div>

<!-- Customer Name Modal -->
<div id="customerNameModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Enter Customer's Name</h5>
        <input type="text" id="customerNameInput" class="form-control" placeholder="Enter customer's name">
        <button class="btn btn-primary mt-3" id="saveCustomerNameBtn">Save</button>
    </div>
</div>

<!-- View Layaways Modal -->
<div id="viewLayawaysModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Customer Layaways</h5>
        <div class="mb-3">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Select a customer to view their layaways or search for layaways by ID.
            </div>
            <div class="input-group">
                <input type="text" id="layawaySearchInput" class="form-control" placeholder="Search by layaway ID...">
                <button class="btn btn-primary" id="searchLayawayBtn">Search</button>
            </div>
        </div>
        <div id="layawaysList" class="list-group">
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading layaways...</p>
            </div>
        </div>
    </div>
</div>

<!-- View Draft Orders Modal -->
<div id="viewDraftOrdersModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Shopify Draft Orders</h5>
        <div class="mb-3">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                View and process draft orders from your Shopify store.
            </div>
            <div class="input-group">
                <input type="text" id="draftOrderSearchInput" class="form-control" placeholder="Search by order number or customer name...">
                <button class="btn btn-primary" id="searchDraftOrderBtn">Search</button>
            </div>
        </div>
        <div id="draftOrdersList" class="list-group">
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading draft orders...</p>
            </div>
        </div>
    </div>
</div>

    <input type="hidden" id="tillId" value="{{ till.id }}">
    <input type="hidden" id="taxRate" value="{{ till.tax_rate }}">
    <input type="hidden" id="taxInclusive" value="{{ till.tax_inclusive|default(true)|lower }}">
    <input type="hidden" id="customerId" value="">
    <input type="hidden" id="giftCardId" value="">
    <input type="hidden" id="giftCardAmount" value="">
    <input type="hidden" id="storeCreditAmount" value="">
    <input type="hidden" id="storeCreditAccountId" value="">
    <input type="hidden" id="employeeId" value="">
    <input type="hidden" id="employeeName" value="">
<input type="hidden" id="heldSaleId" value="">
<input type="hidden" id="hasMerchantMatchSettings" value="{{ till.has_merchant_match_settings|default(False)|lower }}">
<input type="hidden" id="businessName" value="{{ current_user.business_name }}">
<input type="hidden" id="username" value="{{ current_user.username }}">

<script src="{{ url_for('static', filename='js/discount.js') }}"></script>
<script src="{{ url_for('static', filename='js/tax-init-helper.js') }}"></script>
<script src="{{ url_for('static', filename='js/tax-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/tax-handler.js') }}"></script>
<script src="{{ url_for('static', filename='js/payment-handler.js') }}"></script>
<script src="{{ url_for('static', filename='js/split-payment-handler.js') }}"></script>
<script src="{{ url_for('static', filename='js/pos_draft_orders.js') }}"></script>
<script src="{{ url_for('static', filename='js/pos_payout.js') }}"></script>
<script src="{{ url_for('static', filename='js/cart-image-zoom.js') }}"></script>
<script src="{{ url_for('static', filename='js/line-item-discount-handler.js') }}"></script>
<script src="{{ url_for('static', filename='js/pos_buy.js') }}"></script>
<script>
let shopifyLocationAddress = '';
// Define currency symbol with a default value
let currencySymbol = "$"; // Default currency symbol

document.addEventListener('DOMContentLoaded', function() {
    // --- Sticky Cart Scroll Behavior ---
    const stickyCart = document.querySelector('.sticky-cart');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const st = window.pageYOffset || document.documentElement.scrollTop;

        // Add a visual indicator when scrolling
        if (st > 100) {
            stickyCart.classList.add('is-scrolling');
            stickyCart.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.3)';
            stickyCart.style.transform = 'translateY(5px)';
        } else {
            stickyCart.classList.remove('is-scrolling');
            stickyCart.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.2)';
            stickyCart.style.transform = 'translateY(0)';
        }

        if (st > lastScrollTop) {
            // Scrolling down
            stickyCart.style.top = '20px';
        } else {
            // Scrolling up
            stickyCart.style.top = '20px';
        }
        lastScrollTop = st <= 0 ? 0 : st;
    }, false);

    // --- Product Type Sidebar Logic ---
    // --- Product Type Sidebar with Search and Debounce ---
    let allProductTypes = [];
    let productTypeSidebarDebounceTimeout = null;

    function renderProductTypeSidebar(filteredTypes) {
        const sidebar = document.getElementById('productTypeSidebar');
        sidebar.innerHTML = '';
        filteredTypes.forEach(pt => {
            const li = document.createElement('li');
            li.className = 'list-group-item list-group-item-action py-2 px-2';
            li.style.cursor = 'pointer';
            li.textContent = pt;
            li.addEventListener('click', function() {
                // Highlight selected
                sidebar.querySelectorAll('li').forEach(el => el.classList.remove('active'));
                li.classList.add('active');
                // Filter products by product type
                filterProductsBySidebarProductType(pt);
            });
            sidebar.appendChild(li);
        });
    }

    function filterProductsBySidebarProductType(productType) {
        // If the category filter is present, set the product type and trigger change
        const productTypeSelect = document.getElementById('productTypeSelect');
        if (productTypeSelect) {
            productTypeSelect.value = productType;
            const event = new Event('change');
            productTypeSelect.dispatchEvent(event);
        }
    }

    function handleProductTypeSidebarSearchInput() {
        const searchBox = document.getElementById('productTypeSidebarSearch');
        if (!searchBox) return;
        const searchTerm = searchBox.value.trim().toLowerCase();
        if (!searchTerm) {
            renderProductTypeSidebar(allProductTypes);
        } else {
            const filtered = allProductTypes.filter(pt => pt.toLowerCase().includes(searchTerm));
            renderProductTypeSidebar(filtered);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Always fetch all product types on load
        fetch('/pos/get_product_types')
            .then(response => response.json())
            .then(data => {
                if (data.success && Array.isArray(data.product_types)) {
                    allProductTypes = data.product_types;
                    renderProductTypeSidebar(allProductTypes);
                }
            })
            .catch(err => {
                console.error('Failed to load product types for sidebar:', err);
            });

        // Setup search box event listener for narrowing down
        const searchBox = document.getElementById('productTypeSidebarSearch');
        if (searchBox) {
            searchBox.addEventListener('input', function() {
                if (productTypeSidebarDebounceTimeout) clearTimeout(productTypeSidebarDebounceTimeout);
                productTypeSidebarDebounceTimeout = setTimeout(handleProductTypeSidebarSearchInput, 200);
            });
        }

        // Add event listener for the main search input to auto-hide saved searches and quick access
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            // Function to toggle visibility of saved searches and quick access sections
            function toggleSidebarSections(show) {
                // Find the saved searches section by looking for the card that contains the savedSearchCards element
                const savedSearchesCards = document.querySelectorAll('.card.mb-3');
                let savedSearchesCard = null;
                let quickAccessCard = null;

                // Find the correct cards by their titles
                savedSearchesCards.forEach(card => {
                    const title = card.querySelector('.card-title');
                    if (title && title.textContent === 'Saved Searches') {
                        savedSearchesCard = card;
                    } else if (title && title.textContent === 'Quick Access') {
                        quickAccessCard = card;
                    }
                });

                if (savedSearchesCard) {
                    savedSearchesCard.style.display = show ? 'block' : 'none';
                }

                if (quickAccessCard) {
                    quickAccessCard.style.display = show ? 'block' : 'none';
                }
            }

            // Add input event listener to hide/show sections based on search input
            searchInput.addEventListener('input', function() {
                if (this.value.trim() === '') {
                    // Show saved searches and quick access sections when search is cleared
                    toggleSidebarSections(true);
                } else {
                    // Hide saved searches and quick access sections when searching
                    toggleSidebarSections(false);
                }
            });

            // Add keypress event listener for Enter key
            searchInput.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    searchProducts();
                }
            });
        }
    });
    const shopifyTillId = document.getElementById('tillId')?.value;
    if (shopifyTillId) {
        fetch(`/shopify/get_location/${shopifyTillId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.location) {
                    const loc = data.location;
                    shopifyLocationAddress = `${loc.name || ''}<br>${loc.address1 || ''}<br>${loc.address2 || ''}<br>${loc.city || ''}, ${loc.province || ''} ${loc.zip || ''}<br>${loc.country || ''}`;
                } else {
                    console.warn('Failed to fetch Shopify location details');
                }
            })
            .catch(err => console.error('Error fetching Shopify location:', err));
    }

    // Open Create Customer Modal
    document.getElementById('openCreateCustomerModalBtn').addEventListener('click', function() {
        document.getElementById('createCustomerModal').style.display = 'block';
    });

    // Handle Create Customer Form Submission
    document.getElementById('createCustomerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const firstName = document.getElementById('newCustomerFirstName').value.trim();
        const lastName = document.getElementById('newCustomerLastName').value.trim();
        const email = document.getElementById('newCustomerEmail').value.trim();
        const messageDiv = document.getElementById('createCustomerMessage');
        messageDiv.innerHTML = '';

        const initialCredit = parseFloat(document.getElementById('newCustomerInitialCredit').value);
        const payload = {
            firstName: firstName,
            lastName: lastName,
            email: email
        };
        if (!isNaN(initialCredit) && initialCredit > 0) {
            payload.initialCredit = initialCredit;
        }

        fetch('/shopify/customers/api/customer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                messageDiv.innerHTML = '<div class="alert alert-success">Customer created successfully.</div>';
                setTimeout(() => {
                    document.getElementById('createCustomerModal').style.display = 'none';
                    messageDiv.innerHTML = '';
                    document.getElementById('createCustomerForm').reset();
                }, 1500);
            } else {
                messageDiv.innerHTML = '<div class="alert alert-danger">Error: ' + (data.message || 'Failed to create customer') + '</div>';
            }
        })
        .catch(error => {
            console.error('Error creating customer:', error);
            messageDiv.innerHTML = '<div class="alert alert-danger">Error: ' + error.message + '</div>';
        });
    });

    const employeeNameDisplay = document.getElementById('employeeNameDisplay');
    const tillId = document.getElementById('tillId').value;
    const quicklinkCards = document.getElementById('quicklinkCards');

    // Store the user's currency symbol
    let currencySymbol = "$"; // Default currency symbol

    // Fetch the user's currency symbol
    fetch('/pos/get_user_currency')
        .then(response => response.json())
        .then(data => {
            if (data.currency_symbol) {
                currencySymbol = data.currency_symbol;
                console.log("Using currency symbol:", currencySymbol);

                // Update all currency symbol placeholders in the DOM
                document.querySelectorAll('.currency-symbol').forEach(el => {
                    el.textContent = currencySymbol;
                });

                // Update all hardcoded $ symbols in the DOM
                updateCurrencySymbolsInDOM();
            }
        })
        .catch(error => {
            console.error("Error fetching currency symbol:", error);
        });

    // Function to update all hardcoded $ symbols in the DOM
    function updateCurrencySymbolsInDOM() {
        // Update customer details
        const giftCardBalance = document.getElementById('giftCardBalance');
        if (giftCardBalance && giftCardBalance.textContent.includes('$')) {
            giftCardBalance.innerHTML = `<span class="detail-label">Gift Card Balance:</span> <span class="detail-value"><span class="currency-symbol">${currencySymbol}</span></span>`;
        }

        const storeCreditBalance = document.getElementById('storeCreditBalance');
        if (storeCreditBalance && storeCreditBalance.textContent.includes('$')) {
            storeCreditBalance.innerHTML = `<span class="detail-label">Store Credit Balance:</span> <span class="detail-value"><span class="currency-symbol">${currencySymbol}</span></span>`;
        }

        const totalCreditBalance = document.getElementById('totalCreditBalance');
        if (totalCreditBalance && totalCreditBalance.textContent.includes('$')) {
            totalCreditBalance.innerHTML = `<span class="detail-label">Total Credit Balance:</span> <span class="detail-value"><span class="currency-symbol">${currencySymbol}</span></span>`;
        }

        // Update totals section
        const subtotalElement = document.getElementById('subtotal');
        if (subtotalElement && subtotalElement.parentElement.textContent.includes('$')) {
            subtotalElement.parentElement.innerHTML = `<span class="currency-symbol">${currencySymbol}</span><span id="subtotal">0.00</span>`;
        }

        const taxElement = document.getElementById('tax');
        if (taxElement && taxElement.parentElement.textContent.includes('$')) {
            taxElement.parentElement.innerHTML = `<span class="currency-symbol">${currencySymbol}</span><span id="tax">0.00</span>`;
        }

        const totalElement = document.getElementById('total');
        if (totalElement && totalElement.parentElement.textContent.includes('$')) {
            totalElement.parentElement.innerHTML = `<span class="currency-symbol">${currencySymbol}</span><span id="total">0.00</span>`;
        }

        // Update credit section
        const giftCardApplied = document.getElementById('giftCardApplied');
        if (giftCardApplied && giftCardApplied.parentElement.textContent.includes('$')) {
            giftCardApplied.parentElement.innerHTML = `Gift Card: <span class="currency-symbol">${currencySymbol}</span><span id="giftCardApplied">0.00</span>`;
        }

        const storeCreditApplied = document.getElementById('storeCreditApplied');
        if (storeCreditApplied && storeCreditApplied.parentElement.textContent.includes('$')) {
            storeCreditApplied.parentElement.innerHTML = `Store Credit: <span class="currency-symbol">${currencySymbol}</span><span id="storeCreditApplied">0.00</span>`;
        }

        // Update payment modals
        const totalAmountDue = document.getElementById('totalAmountDue');
        if (totalAmountDue && totalAmountDue.parentElement.textContent.includes('$')) {
            totalAmountDue.parentElement.innerHTML = `Total Amount Due: <span class="currency-symbol">${currencySymbol}</span><span id="totalAmountDue"></span>`;
        }

        const changeDue = document.getElementById('changeDue');
        if (changeDue && changeDue.parentElement.textContent.includes('$')) {
            changeDue.parentElement.innerHTML = `Change Due: <span class="currency-symbol">${currencySymbol}</span><span id="changeDue">0.00</span>`;
        }

        const giftCardAmountUsed = document.getElementById('giftCardAmountUsed');
        if (giftCardAmountUsed && giftCardAmountUsed.parentElement.textContent.includes('$')) {
            giftCardAmountUsed.parentElement.innerHTML = `Gift Card Amount Used: <span class="currency-symbol">${currencySymbol}</span><span id="giftCardAmountUsed">0.00</span>`;
        }

        const totalCardAmountDue = document.getElementById('totalCardAmountDue');
        if (totalCardAmountDue && totalCardAmountDue.parentElement.textContent.includes('$')) {
            totalCardAmountDue.parentElement.innerHTML = `Total Amount Due: <span class="currency-symbol">${currencySymbol}</span><span id="totalCardAmountDue"></span>`;
        }

        const cardGiftCardAmountUsed = document.getElementById('cardGiftCardAmountUsed');
        if (cardGiftCardAmountUsed && cardGiftCardAmountUsed.parentElement.textContent.includes('$')) {
            cardGiftCardAmountUsed.parentElement.innerHTML = `Gift Card Amount Used: <span class="currency-symbol">${currencySymbol}</span><span id="cardGiftCardAmountUsed">0.00</span>`;
        }
    }

    // Load quicklinks from database when page loads
    loadQuicklinks();

    // Add event listener for the "Add New Quicklink" button
    document.getElementById('addNewQuicklinkBtn').addEventListener('click', function() {
        // Get the current highest quicklink number
        const quicklinkElements = document.querySelectorAll('.product-type-card');
        let highestNumber = -1;

        quicklinkElements.forEach(element => {
            const number = parseInt(element.dataset.quicklinkNumber);
            if (!isNaN(number) && number > highestNumber) {
                highestNumber = number;
            }
        });

        // Create a new quicklink with the next number
        const newNumber = highestNumber + 1;
        createNewQuicklink(newNumber);
    });

    // Add event delegation for remove quicklink buttons
    quicklinkCards.addEventListener('click', function(event) {
        if (event.target.closest('.remove-quicklink-btn')) {
            const quicklinkCard = event.target.closest('.quicklink-container');
            const productCard = event.target.closest('.product-type-card');
            const quicklinkNumber = productCard.dataset.quicklinkNumber;

            // Remove the quicklink from the database
            removeQuicklinkFromDatabase(quicklinkNumber);

            // Remove the quicklink from the UI
            if (quicklinkCard) {
                quicklinkCard.remove();
            }
        }
    });

    // Function to create a new quicklink in the UI
    function createNewQuicklink(number) {
        const container = document.createElement('div');
        container.className = 'col-3 mb-3 quicklink-container';
        container.innerHTML = `
            <div class="product-type-card" data-quicklink-number="${number}">
                <div class="remove-quicklink-btn" title="Remove Quicklink">
                    <i class="fas fa-times"></i>
                </div>
                <div class="card-body">
                    <h5 class="card-title">Quicklink ${number + 1}</h5>
                    <p class="quicklink-product-name">No product selected</p>
                    <div class="mt-auto">
                        <button class="btn btn-primary w-100 select-quicklink-btn">Select Product</button>
                    </div>
                </div>
            </div>
        `;

        // Add event listener for the select button
        const selectBtn = container.querySelector('.select-quicklink-btn');
        selectBtn.addEventListener('click', function() {
            selectedQuicklinkNumber.value = number;
            selectedQuicklinkProductId.value = '';
            selectedQuicklinkProductName.textContent = 'No product selected';
            quicklinkModal.style.display = 'block';
        });

        // Add the new quicklink to the quicklinkCards container
        quicklinkCards.appendChild(container);
    }

    // Function to remove a quicklink from the database
    function removeQuicklinkFromDatabase(quicklinkNumber) {
        fetch('/pos/remove_quicklink', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                till_id: tillId,
                quicklink_number: quicklinkNumber
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Error removing quicklink:', data.message);
            }
            // Update the visibility of the Quick Access section
            updateQuickAccessSectionVisibility();
        })
        .catch(error => {
            console.error('Error removing quicklink:', error);
            // Still update visibility even if there was an error
            updateQuickAccessSectionVisibility();
        });
    }

    // Function to check if there are any active quicklinks and update section visibility
    function updateQuickAccessSectionVisibility() {
        const quicklinkCards = document.getElementById('quicklinkCards');
        const quickAccessSection = document.getElementById('quickAccessSection');

        // Check if there are any quicklinks with product names (not "No product selected")
        const activeQuicklinks = quicklinkCards.querySelectorAll('.quicklink-product-name');
        let hasActiveQuicklinks = false;

        activeQuicklinks.forEach(element => {
            const text = element.textContent.trim();
            if (text && text !== 'No product selected') {
                hasActiveQuicklinks = true;
            }
        });

        // Show or hide the section based on whether there are active quicklinks
        quickAccessSection.style.display = hasActiveQuicklinks ? 'block' : 'none';
        console.log('Quick Access section visibility updated:', hasActiveQuicklinks ? 'visible' : 'hidden');
    }

    // Function to load quicklinks from the database with improved error handling and retry logic
    function loadQuicklinks(retryCount = 0) {
        const tillId = document.getElementById('tillId').value;
        const maxRetries = 3;
        const retryDelay = 1000; // 1 second

        // Check if tillId is valid before making the request
        if (!tillId) {
            console.log('No valid till ID found, skipping quicklinks fetch');
            updateQuickAccessSectionVisibility();
            return;
        }

        console.log(`Fetching quicklinks for till ID: ${tillId} (attempt ${retryCount + 1})`);

        // Add timeout to the fetch request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        fetch(`/pos/get_quicklinks/${tillId}`, {
            signal: controller.signal,
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        })
            .then(response => {
                clearTimeout(timeoutId);
                console.log(`Quicklinks response status: ${response.status}`);

                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status} - ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Quicklinks response data:', data);

                if (data.success) {
                    if (data.quicklinks && data.quicklinks.length > 0) {
                        console.log(`Found ${data.quicklinks.length} quicklinks to load`);

                        // Process each quicklink
                        data.quicklinks.forEach((quicklink, index) => {
                            console.log(`Processing quicklink ${index + 1}:`, quicklink);

                            const quicklinkNumber = quicklink.quicklink_number;
                            const productId = quicklink.product_id;
                            const productName = quicklink.product_name;

                            // Validate quicklink data
                            if (quicklinkNumber === undefined || !productId || !productName) {
                                console.warn('Invalid quicklink data:', quicklink);
                                return;
                            }

                            // Find the quicklink card with better error handling
                            const quicklinkCard = document.querySelector(`.product-type-card[data-quicklink-number="${quicklinkNumber}"]`);
                            if (!quicklinkCard) {
                                console.warn(`Quicklink card not found for number: ${quicklinkNumber}`);
                                return;
                            }

                            console.log(`Updating quicklink card ${quicklinkNumber} with product: ${productName}`);

                            // Update the product name
                            const productNameElement = quicklinkCard.querySelector('.quicklink-product-name');
                            if (productNameElement) {
                                productNameElement.textContent = productName;
                            } else {
                                console.warn(`Product name element not found for quicklink ${quicklinkNumber}`);
                            }

                            // Update the UI to show the "Add Item" button instead of "Select Product"
                            const mtAutoDiv = quicklinkCard.querySelector('.mt-auto');
                            if (mtAutoDiv) {
                                mtAutoDiv.innerHTML = `
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div class="d-flex align-items-center">
                                            <button class="btn btn-sm btn-outline-primary select-quicklink-btn" title="Edit product">
                                                <i class="fas fa-pencil-alt"></i>
                                            </button>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <label for="quicklink-qty-${quicklinkNumber}" class="me-2 text-white-50">Qty:</label>
                                            <div class="quantity-control">
                                                <button class="btn btn-sm btn-outline-secondary quantity-btn quantity-decrease">-</button>
                                                <input type="number" id="quicklink-qty-${quicklinkNumber}" class="form-control form-control-sm quicklink-qty" value="1" min="1" style="width: 40px; text-align: center;">
                                                <button class="btn btn-sm btn-outline-secondary quantity-btn quantity-increase">+</button>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn btn-secondary w-100 add-quicklink-item-btn" data-product-id="${productId}">${productName}</button>
                                `;

                                // Add event listeners for quantity buttons and edit button
                                const decreaseBtn = mtAutoDiv.querySelector('.quantity-decrease');
                                const increaseBtn = mtAutoDiv.querySelector('.quantity-increase');
                                const qtyInput = mtAutoDiv.querySelector('.quicklink-qty');
                                const editBtn = mtAutoDiv.querySelector('.select-quicklink-btn');

                                if (decreaseBtn && increaseBtn && qtyInput) {
                                    decreaseBtn.addEventListener('click', function() {
                                        const currentValue = parseInt(qtyInput.value) || 1;
                                        if (currentValue > 1) {
                                            qtyInput.value = currentValue - 1;
                                        }
                                    });

                                    increaseBtn.addEventListener('click', function() {
                                        const currentValue = parseInt(qtyInput.value) || 1;
                                        qtyInput.value = currentValue + 1;
                                    });
                                } else {
                                    console.warn(`Quantity controls not found for quicklink ${quicklinkNumber}`);
                                }

                                // Add event listener for the edit button
                                if (editBtn) {
                                    editBtn.addEventListener('click', function() {
                                        const quicklinkNumber = quicklinkCard.dataset.quicklinkNumber;
                                        selectedQuicklinkNumber.value = quicklinkNumber;
                                        selectedQuicklinkProductId.value = '';
                                        selectedQuicklinkProductName.textContent = 'No product selected';
                                        quicklinkModal.style.display = 'block';
                                    });
                                } else {
                                    console.warn(`Edit button not found for quicklink ${quicklinkNumber}`);
                                }
                            } else {
                                console.warn(`mt-auto div not found for quicklink ${quicklinkNumber}`);
                            }
                        });

                        console.log('Successfully loaded all quicklinks');
                    } else {
                        console.log('No quicklinks found in database');
                    }
                } else {
                    console.warn('Quicklinks request failed:', data.message || 'Unknown error');
                }

                // Update the visibility of the Quick Access section
                updateQuickAccessSectionVisibility();
            })
            .catch(error => {
                clearTimeout(timeoutId);
                console.error(`Error loading quicklinks (attempt ${retryCount + 1}):`, error);

                // Retry logic
                if (retryCount < maxRetries && error.name !== 'AbortError') {
                    console.log(`Retrying quicklinks load in ${retryDelay}ms...`);
                    setTimeout(() => {
                        loadQuicklinks(retryCount + 1);
                    }, retryDelay);
                } else {
                    console.error('Failed to load quicklinks after all retry attempts');
                    // Even on error, update visibility
                    updateQuickAccessSectionVisibility();
                }
            });
    }

    // Function to fetch currencies from the server
    function fetchCurrencies() {
        return fetch('/pos/get_currencies')
            .then(response => response.json())
            .then(data => {
                if (data.currencies) {
                    return data.currencies;
                } else {
                    console.error('No currencies found in response');
                    return [];
                }
            })
            .catch(error => {
                console.error('Error fetching currencies:', error);
                return [];
            });
    }

    const variantModal = document.getElementById('variantModal');
    const variantList = document.getElementById('variantList');
    const quicklinkModal = document.getElementById('quicklinkModal');
    const quicklinkSearchBtn = document.getElementById('quicklinkSearchBtn');
    const quicklinkSearchInput = document.getElementById('quicklinkSearchInput');
    const quicklinkProductList = document.getElementById('quicklinkProductList');
    const saveQuicklinkBtn = document.getElementById('saveQuicklinkBtn');
    const selectedQuicklinkNumber = document.getElementById('selectedQuicklinkNumber');
    const selectedQuicklinkProductId = document.getElementById('selectedQuicklinkProductId');
    const selectedQuicklinkProductName = document.getElementById('selectedQuicklinkProductName');
    const cashPaymentModal = document.getElementById('cashPaymentModal');
    const cardPaymentModal = document.getElementById('cardPaymentModal');
    const customerSearchModal = document.getElementById('customerSearchModal');
    const layawayModal = document.getElementById('layawayModal');
    const heldSalesModal = document.getElementById('heldSalesModal');
    const customerNameModal = document.getElementById('customerNameModal');
    const customItemModal = document.getElementById('customItemModal');
    const receiptModal = document.getElementById('receiptModal');
    const receiptContent = document.getElementById('receiptContent');
    const closeModalButtons = document.getElementsByClassName('close');

    // Set default employee info
    document.getElementById('employeeId').value = 'default';
    document.getElementById('employeeName').value = 'Default Employee';
    // Initialize POS immediately
    initPOS();

    // Only search when Enter key is pressed or search button is clicked
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                searchProducts();
            }
        });
    }

    const searchButton = document.getElementById('searchButton');
    if (searchButton) {
        searchButton.addEventListener('click', function() {
            searchProducts();
        });
    }

    const inStockOnly = document.getElementById('inStockOnly');
    if (inStockOnly) {
        inStockOnly.addEventListener('change', function() {
            if (searchInput && searchInput.value.trim().length > 0) {
                searchProducts();
            }
        });
    }

    for (let btn of closeModalButtons) {
        btn.onclick = function() {
            // Find the parent modal of this close button
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = "none";
            } else {
                // Fallback to the old approach if we can't find the parent modal
                if (variantModal) variantModal.style.display = "none";
                if (quicklinkModal) quicklinkModal.style.display = "none";
                if (cashPaymentModal) cashPaymentModal.style.display = "none";
                if (cardPaymentModal) cardPaymentModal.style.display = "none";
                if (customerSearchModal) customerSearchModal.style.display = "none";
                if (layawayModal) layawayModal.style.display = "none";
                if (heldSalesModal) heldSalesModal.style.display = "none";
                if (customerNameModal) customerNameModal.style.display = "none";
                if (customItemModal) customItemModal.style.display = "none";
                if (receiptModal) receiptModal.style.display = "none";
                if (previousSalesModal) previousSalesModal.style.display = "none";
                if (paymentOptionModal) paymentOptionModal.style.display = "none";
                if (editPriceModal) editPriceModal.style.display = "none";
            }
        }
    }

    window.onclick = function(event) {
        if (event.target == variantModal) {
            variantModal.style.display = "none";
        }
        if (event.target == quicklinkModal) {
            quicklinkModal.style.display = "none";
        }
        if (event.target == cashPaymentModal) {
            cashPaymentModal.style.display = "none";
        }
        if (event.target == cardPaymentModal) {
            cardPaymentModal.style.display = "none";
        }
        if (event.target == layawayModal) {
            layawayModal.style.display = "none";
        }
        if (event.target == heldSalesModal) {
            heldSalesModal.style.display = "none";
        }
        if (event.target == customerNameModal) {
            customerNameModal.style.display = "none";
        }
        if (event.target == receiptModal) {
            receiptModal.style.display = "none";
        }
        // Check for previousSalesModal if it exists
        if (typeof previousSalesModal !== 'undefined' && event.target == previousSalesModal) {
            previousSalesModal.style.display = "none";
        }
    }

    // Category filter functionality
    const vendorSelect = document.getElementById('vendorSelect');
    const productTypeSelect = document.getElementById('productTypeSelect');
    const categoryProductFilter = document.getElementById('categoryProductFilter');
    const categoryInStockOnly = document.getElementById('categoryInStockOnly');
    const categoryProductList = document.getElementById('categoryProductList');

    // Load vendors when page loads
    loadVendors();

    // Event listeners for category filters
    vendorSelect.addEventListener('change', function() {
        const selectedVendor = this.value;
        if (selectedVendor) {
            loadProductTypes(selectedVendor);
            productTypeSelect.disabled = false;
        } else {
            productTypeSelect.innerHTML = '<option value="">Select vendor first</option>';
            productTypeSelect.disabled = true;
            categoryProductList.innerHTML = '';
        }
    });

    productTypeSelect.addEventListener('change', function() {
        const selectedVendor = vendorSelect.value;
        const selectedProductType = this.value;
        if (selectedVendor && selectedProductType) {
            loadCategoryProducts(selectedVendor, selectedProductType);
            // Show the save search button when both vendor and product type are selected
            document.getElementById('saveSearchContainer').style.display = 'block';
        } else {
            document.getElementById('saveSearchContainer').style.display = 'none';
        }
    });

    // Add event listener for save search button
    document.getElementById('saveSearchBtn').addEventListener('click', function() {
        const selectedVendor = vendorSelect.value;
        const selectedProductType = productTypeSelect.value;
        const inStockOnly = categoryInStockOnly.checked;

        if (!selectedVendor || !selectedProductType) {
            alert('Please select both vendor and product type before saving the search.');
            return;
        }

        // Show a prompt to get a display name for the saved search
        const displayName = prompt('Enter a name for this saved search:', selectedProductType);
        if (!displayName) return; // User cancelled

        // Save the search to the database
        fetch('/pos/save_category_search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                vendor: selectedVendor,
                product_type: selectedProductType,
                display_name: displayName,
                in_stock_only: inStockOnly
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                const saveSearchMessage = document.getElementById('saveSearchMessage');
                saveSearchMessage.style.display = 'inline';
                saveSearchMessage.textContent = data.message;

                // Hide the message after 3 seconds
                setTimeout(() => {
                    saveSearchMessage.style.display = 'none';
                }, 3000);

                // Refresh the saved searches display
                loadSavedSearches();
            } else {
                alert('Error saving search: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error saving search:', error);
            alert('An error occurred while saving the search.');
        });
    });

    categoryProductFilter.addEventListener('input', function() {
        filterCategoryProducts();
    });

    categoryInStockOnly.addEventListener('change', function() {
        const selectedVendor = vendorSelect.value;
        const selectedProductType = productTypeSelect.value;
        if (selectedVendor && selectedProductType) {
            loadCategoryProducts(selectedVendor, selectedProductType);
        }
    });

    function loadVendors() {
        fetch('/pos/get_distinct_vendors')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    vendorSelect.innerHTML = '<option value="">Select a vendor</option>';
                    data.vendors.forEach(vendor => {
                        const option = document.createElement('option');
                        option.value = vendor;
                        option.textContent = vendor;
                        vendorSelect.appendChild(option);
                    });
                } else {
                    console.error('Error loading vendors:', data.message);
                    vendorSelect.innerHTML = '<option value="">Error loading vendors</option>';
                }
            })
            .catch(error => {
                console.error('Error loading vendors:', error);
                vendorSelect.innerHTML = '<option value="">Error loading vendors</option>';
            });
    }

    function loadProductTypes(vendor) {
        fetch(`/pos/get_product_types?vendor=${encodeURIComponent(vendor)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    productTypeSelect.innerHTML = '<option value="">Select a product type</option>';
                    data.product_types.forEach(productType => {
                        const option = document.createElement('option');
                        option.value = productType;
                        option.textContent = productType;
                        productTypeSelect.appendChild(option);
                    });
                } else {
                    console.error('Error loading product types:', data.message);
                    productTypeSelect.innerHTML = '<option value="">Error loading product types</option>';
                }
            })
            .catch(error => {
                console.error('Error loading product types:', error);
                productTypeSelect.innerHTML = '<option value="">Error loading product types</option>';
            });
    }

    function loadCategoryProducts(vendor, productType, page = 1) {
        const inStockOnly = categoryInStockOnly.checked;
        const limit = 50; // Number of products per page

        // Show loading indicator
        categoryProductList.innerHTML = '<li class="list-group-item text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Loading products...</p></li>';

        console.log(`Loading category products: vendor=${vendor}, product_type=${productType}, in_stock_only=${inStockOnly}, page=${page}, limit=${limit}`);

        fetch(`/pos/get_products_by_category?vendor=${encodeURIComponent(vendor)}&product_type=${encodeURIComponent(productType)}&in_stock_only=${inStockOnly}&page=${page}&limit=${limit}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    console.log(`Loaded ${data.products.length} products for category (page ${page} of ${data.pagination.total_pages})`);
                    console.log(`Total products: ${data.pagination.total}`);
                    displayCategoryProducts(data.products, data.pagination);
                } else {
                    console.error('Error loading products:', data.message);
                    categoryProductList.innerHTML = '<li class="list-group-item">Error loading products: ' + data.message + '</li>';
                }
            })
            .catch(error => {
                console.error('Error loading products:', error);
                categoryProductList.innerHTML = '<li class="list-group-item">Error loading products: ' + error.message + '</li>';
            });
    }

    function displayCategoryProducts(products, pagination) {
        categoryProductList.innerHTML = '';

        if (products.length === 0) {
            categoryProductList.innerHTML = '<li class="list-group-item">No products found</li>';
            return;
        }

        // Add a counter at the top with pagination info
        const counterItem = document.createElement('li');
        counterItem.className = 'list-group-item bg-primary text-white d-flex justify-content-between align-items-center';

        // Create counter text
        const counterText = document.createElement('div');
        counterText.innerHTML = `<strong>Showing ${products.length} of ${pagination.total} products</strong>`;

        // Create pagination controls
        const paginationControls = document.createElement('div');
        paginationControls.className = 'pagination-controls';

        // Only show pagination if there are multiple pages
        if (pagination.total_pages > 1) {
            paginationControls.innerHTML = `
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-light" ${pagination.page <= 1 ? 'disabled' : ''} data-page="${pagination.page - 1}">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span class="btn btn-light disabled">Page ${pagination.page} of ${pagination.total_pages}</span>
                    <button class="btn btn-light" ${pagination.page >= pagination.total_pages ? 'disabled' : ''} data-page="${pagination.page + 1}">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            `;
        }

        counterItem.appendChild(counterText);
        counterItem.appendChild(paginationControls);
        categoryProductList.appendChild(counterItem);

        // Add event listeners to pagination buttons
        const paginationButtons = paginationControls.querySelectorAll('button:not([disabled])');
        paginationButtons.forEach(button => {
            button.addEventListener('click', function() {
                const page = parseInt(this.dataset.page);
                const vendor = vendorSelect.value;
                const productType = productTypeSelect.value;
                loadCategoryProducts(vendor, productType, page);
            });
        });

        // Create a grid container for products
        const gridContainer = document.createElement('li');
        gridContainer.className = 'list-group-item p-3';
        gridContainer.innerHTML = '<div class="category-product-grid"></div>';
        categoryProductList.appendChild(gridContainer);

        const productGrid = gridContainer.querySelector('.category-product-grid');

        // Log the number of products being displayed
        console.log(`Displaying ${products.length} products in category grid view`);

        // Sort products alphabetically by title
        products.sort((a, b) => a.title.localeCompare(b.title));

        products.forEach((product, index) => {
            const imageUrl = product.image ? product.image : 'https://via.placeholder.com/150';

            // Create grid item - use the same class as main search results for consistent hover effects
            const gridItem = document.createElement('div');
            gridItem.className = 'product-grid-item';
            gridItem.dataset.title = product.title.toLowerCase();
            gridItem.dataset.index = index;

            // Create the product card
            const productCard = document.createElement('div');
            productCard.className = 'product-card h-100';

            // Image container with larger image
            const imageContainer = document.createElement('div');
            imageContainer.className = 'card-img-container';
            imageContainer.innerHTML = `<img src="${imageUrl}" class="card-img-top" alt="${product.title}" loading="lazy">`;

            // Product details container
            const detailsContainer = document.createElement('div');
            detailsContainer.className = 'card-body d-flex flex-column p-2';

            // Product title and info
            const titleElement = document.createElement('h6');
            titleElement.className = 'card-title fs-6 mb-1';
            titleElement.title = product.title;
            titleElement.textContent = product.title;

            const infoElement = document.createElement('p');
            infoElement.className = 'variant-name small mb-1';
            infoElement.innerHTML = `${product.vendor} - ${product.productType}`;

            detailsContainer.appendChild(titleElement);
            detailsContainer.appendChild(infoElement);

            // Variants section
            const variantsContainer = document.createElement('div');
            variantsContainer.className = 'mt-auto';

            if (product.variants.length === 1) {
                // Single variant display
                const variant = product.variants[0];
                const variantElement = document.createElement('div');
                variantElement.className = 'variant-info';

                // Price section
                const priceSection = document.createElement('div');
                priceSection.className = 'price-section mb-1';
                priceSection.innerHTML = `<span class="price-tag fw-bold">$${variant.price}</span>`;

                // Stock section
                const stockSection = document.createElement('div');
                stockSection.className = 'stock-section mb-2';
                stockSection.innerHTML = `
                    <span class="stock-tag small">
                        <span class="stock-indicator ${variant.inventory_quantity > 10 ? 'stock-high' : variant.inventory_quantity > 3 ? 'stock-medium' : 'stock-low'}"></span>
                        ${variant.inventory_quantity || 0} in stock
                    </span>
                `;

                // Quantity input and add to cart button
                const actionSection = document.createElement('div');
                actionSection.innerHTML = `
                    <div class="input-group input-group-sm mb-1">
                        <span class="input-group-text py-0 px-1">Qty</span>
                        <input type="number" class="form-control qty-input py-0" value="1" min="1">
                    </div>
                    <button class="btn btn-primary btn-sm w-100 py-1 add-to-cart-btn" ${variant.inventory_quantity <= 0 ? 'disabled' : ''}>
                        ${variant.inventory_quantity <= 0 ? 'Out of Stock' : 'Add to Cart'}
                    </button>
                `;

                // Append all sections to the variant element
                variantElement.appendChild(priceSection);
                variantElement.appendChild(stockSection);
                variantElement.appendChild(actionSection);

                // Add event listeners for add to cart button
                const qtyInput = actionSection.querySelector('.qty-input');
                actionSection.querySelector('.add-to-cart-btn').addEventListener('click', () => {
                    const qty = parseInt(qtyInput.value) || 1;
                    addToCart(product, variant, qty);
                });

                variantsContainer.appendChild(variantElement);
            } else {
                // Multiple variants display
                const variantSummary = document.createElement('div');
                variantSummary.className = 'variant-summary small mb-1';
                variantSummary.innerHTML = `<span class="badge bg-info">${product.variants.length} variants</span>`;

                // Price section
                const priceSection = document.createElement('div');
                priceSection.className = 'price-section mb-1';
                priceSection.innerHTML = `<span class="price-tag fw-bold">$Various</span>`;

                // Select variant button
                const selectButton = document.createElement('button');
                selectButton.className = 'btn btn-primary btn-sm w-100 py-1 select-variant-btn';
                selectButton.textContent = 'Select Variant';

                // Add event listener for select variant button
                selectButton.addEventListener('click', () => {
                    showVariantModal(product, 1);
                });

                variantsContainer.appendChild(variantSummary);
                variantsContainer.appendChild(priceSection);
                variantsContainer.appendChild(selectButton);
            }

            detailsContainer.appendChild(variantsContainer);

            // Assemble the product card
            productCard.appendChild(imageContainer);
            productCard.appendChild(detailsContainer);

            gridItem.appendChild(productCard);
            productGrid.appendChild(gridItem);
        });

        // Apply any existing filter
        if (categoryProductFilter.value.trim()) {
            filterCategoryProducts();
        }
    }

    function filterCategoryProducts() {
        const filterText = categoryProductFilter.value.trim().toLowerCase();
        const items = document.querySelectorAll('.category-product-grid .product-grid-item');

        let visibleCount = 0;
        items.forEach(item => {
            const title = item.dataset.title;
            if (title.includes(filterText)) {
                item.style.display = '';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        // Update the counter
        const counterItem = categoryProductList.querySelector('.list-group-item.bg-primary');
        if (counterItem) {
            counterItem.innerHTML = `<strong>${visibleCount} of ${items.length} products shown</strong>`;
        }
    }

    // Add custom item functionality
    document.getElementById('addCustomItemBtn').addEventListener('click', function() {
        customItemModal.style.display = 'block';
    });

    // Function to display products in an enhanced grid layout
    function displayProducts(products) {
        const productList = document.getElementById('productList');
        productList.innerHTML = '';

        if (products.length === 0) {
            productList.innerHTML = '<div class="text-center p-4 bg-dark rounded"><i class="fas fa-search me-2"></i>No products found</div>';
            return;
        }

        // Add an enhanced results counter at the top with sorting options
        const resultsCounter = document.createElement('div');
        resultsCounter.className = 'results-counter';
        resultsCounter.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="badge bg-primary">${products.length}</span>
                <span>products found</span>
            </div>
            <div class="results-actions">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-primary btn-sm sort-btn" data-sort="name">
                        <i class="fas fa-sort-alpha-down me-1"></i>Name
                    </button>
                    <button class="btn btn-outline-primary btn-sm sort-btn" data-sort="price">
                        <i class="fas fa-sort-numeric-down me-1"></i>Price
                    </button>
                    <button class="btn btn-outline-primary btn-sm sort-btn" data-sort="stock">
                        <i class="fas fa-sort-amount-down me-1"></i>Stock
                    </button>
                </div>
            </div>
        `;
        productList.appendChild(resultsCounter);

        // Add sort functionality
        const sortButtons = resultsCounter.querySelectorAll('.sort-btn');
        sortButtons.forEach(button => {
            button.addEventListener('click', function() {
                const sortType = this.dataset.sort;

                // Remove active class from all buttons
                sortButtons.forEach(btn => btn.classList.remove('btn-secondary', 'active'));

                // Add active class to clicked button
                this.classList.add('btn-secondary', 'active');

                // Sort products
                let sortedProducts = [...products];

                if (sortType === 'name') {
                    sortedProducts.sort((a, b) => a.title.localeCompare(b.title));
                } else if (sortType === 'price') {
                    sortedProducts.sort((a, b) => {
                        const priceA = a.variants[0]?.price || 0;
                        const priceB = b.variants[0]?.price || 0;
                        return priceA - priceB;
                    });
                } else if (sortType === 'stock') {
                    sortedProducts.sort((a, b) => {
                        const stockA = a.variants[0]?.inventory_quantity || 0;
                        const stockB = b.variants[0]?.inventory_quantity || 0;
                        return stockB - stockA; // Higher stock first
                    });
                }

                // Redisplay sorted products
                displayProductCards(sortedProducts);
            });
        });

        // Create a container for the grid
        const gridContainer = document.createElement('div');
        gridContainer.className = 'row g-3';
        gridContainer.id = 'productCardsContainer';
        gridContainer.style.width = '100%'; // Ensure full width
        gridContainer.style.overflow = 'visible'; // Ensure overflow is visible
        productList.appendChild(gridContainer);

        // Clear any previous results counter
        const existingCounter = document.querySelector('.results-counter');
        if (existingCounter) {
            existingCounter.remove();
        }

        // Display the product cards
        displayProductCards(products);
    }

    // Function to display product cards in the grid
    function displayProductCards(products) {
        const gridContainer = document.getElementById('productCardsContainer');
        gridContainer.innerHTML = '';

        // Log the number of products being displayed for debugging
        console.log(`Displaying ${products.length} products in grid view`);

        // Create a document fragment for better performance
        const fragment = document.createDocumentFragment();

        // Add each product as a card in the grid
        products.forEach((product, index) => {
            const imageUrl = product.image ? product.image : 'https://via.placeholder.com/150';

            // Create column for the grid (more items per row for better space utilization)
            const col = document.createElement('div');
            col.className = 'product-grid-item';
            col.setAttribute('data-product-index', index); // Add index for debugging

            // Create card
            const card = document.createElement('div');
            card.className = 'card h-100 product-card';

            // Card content - reorganized as requested: image, name, variant name, price, qty in stock, qty selector
            card.innerHTML = `
                <!-- Image with hover zoom effect -->
                <div class="card-img-container" style="--hover-image-url: url('${imageUrl}');">
                    <img src="${imageUrl}" class="card-img-top" alt="${product.title}" loading="lazy">
                </div>
                <div class="card-body d-flex flex-column p-2">
                    <!-- Name - with 2-line wrapping -->
                    <h6 class="card-title fs-6 mb-1" title="${product.title}">${product.title}</h6>

                    <!-- Variant name -->
                    ${product.variants.length > 1 ?
                        `<div class="variant-summary small mb-1">
                            <span class="badge bg-info">${product.variants.length} variants</span>
                        </div>` :
                        `<p class="variant-name small mb-1">${product.variants[0].title || product.expansionName || ''}</p>`
                    }

                    <!-- Price -->
                    <div class="price-section mb-1">
                        <span class="price-tag fw-bold">${currencySymbol}${product.variants.length === 1 ? product.variants[0].price : 'Various'}</span>
                    </div>

                    <!-- Stock quantity -->
                    <div class="stock-section mb-2">
                        <span class="stock-tag small">
                            ${product.variants.length === 1 ?
                                `<span class="stock-indicator ${product.variants[0].inventory_quantity > 10 ? 'stock-high' : product.variants[0].inventory_quantity > 3 ? 'stock-medium' : 'stock-low'}"></span>
                                ${product.variants[0].inventory_quantity || 0} in stock` :
                                'Multiple variants'
                            }
                        </span>
                    </div>

                    <!-- Quantity selector and add to cart -->
                    <div class="mt-auto">
                        <div class="input-group input-group-sm mb-1">
                            <span class="input-group-text py-0 px-1">Qty</span>
                            <input type="number" class="form-control qty-input py-0" value="1" min="1">
                        </div>
                        <button class="btn btn-primary btn-sm w-100 py-1 ${product.variants.length === 1 ? 'add-to-cart-btn' : 'select-variant-btn'} ${product.variants.length === 1 && product.variants[0].inventory_quantity <= 0 ? 'out-of-stock-btn' : ''}">
                            ${product.variants.length === 1 ?
                                (product.variants[0].inventory_quantity <= 0 ? 'Add to Cart (Out of Stock)' : 'Add to Cart') :
                                'Select Variant'}
                        </button>
                    </div>
                </div>
            `;

            // Add event listeners
            const qtyInput = card.querySelector('.qty-input');
            qtyInput.dataset.productId = product.id;

            // Add image hover enhancement
            const imgContainer = card.querySelector('.card-img-container');
            const img = imgContainer.querySelector('img');

            // Preload the image for smoother hover experience
            const preloadImg = new Image();
            preloadImg.src = imageUrl;

            // Add keyboard accessibility
            imgContainer.setAttribute('tabindex', '0');
            imgContainer.setAttribute('role', 'button');
            imgContainer.setAttribute('aria-label', 'View larger image of ' + product.title);

            // Add keyboard support for image zoom
            imgContainer.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    imgContainer.classList.add('force-hover');
                }
            });

            imgContainer.addEventListener('keyup', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    imgContainer.classList.remove('force-hover');
                }
            });

            if (product.variants.length === 1) {
                card.querySelector('.add-to-cart-btn').addEventListener('click', () => {
                    const qty = parseInt(qtyInput.value);
                    addToCart(product, product.variants[0], qty);
                });
            } else {
                card.querySelector('.select-variant-btn').addEventListener('click', () => {
                    showVariantModal(product, parseInt(qtyInput.value));
                });
            }

            // Add card to grid
            col.appendChild(card);
            fragment.appendChild(col);
        });

        // Append all cards at once for better performance
        gridContainer.appendChild(fragment);

        // Log the actual number of cards added to the DOM
        console.log(`Added ${gridContainer.children.length} product cards to the DOM`);

        // Ensure the container is scrollable and add to cart buttons are visible
        setTimeout(() => {
            // Adjust the container's max-height to ensure it's scrollable
            const productList = document.getElementById('productList');
            if (productList) {
                // Set a reasonable max-height that ensures the container is scrollable
                productList.style.maxHeight = 'calc(100vh - 200px)';
                productList.style.overflowY = 'auto';

                // Ensure the container is scrollable
                if (gridContainer.scrollHeight > productList.clientHeight) {
                    console.log('Product list is scrollable');
                }

                // Add a small bottom padding to ensure the last row of buttons is fully visible
                gridContainer.style.paddingBottom = '60px';
            }
        }, 100); // Small delay to ensure DOM is fully rendered
    }

    // Function to toggle visibility of saved searches and quick access sections
    function toggleSidebarSections(show) {
        // Find the saved searches section by looking for the card that contains the savedSearchCards element
        const savedSearchesCards = document.querySelectorAll('.card.mb-3');
        let savedSearchesCard = null;
        let quickAccessCard = null;

        // Find the correct cards by their titles
        savedSearchesCards.forEach(card => {
            const title = card.querySelector('.card-title');
            if (title && title.textContent === 'Saved Searches') {
                savedSearchesCard = card;
            } else if (title && title.textContent === 'Quick Access') {
                quickAccessCard = card;
            }
        });

        if (savedSearchesCard) {
            savedSearchesCard.style.display = show ? 'block' : 'none';
        }

        if (quickAccessCard) {
            quickAccessCard.style.display = show ? 'block' : 'none';
        }
    }

    function searchProducts() {
        const query = document.getElementById('searchInput').value.trim();
        if (query.length > 0) {
            // Hide saved searches and quick access sections when searching
            toggleSidebarSections(false);

            const isNumeric = !isNaN(query);
            const searchType = isNumeric ? 'barcode' : 'title';
            const regexQuery = query.replace(/\s+/g, '\\s*');
            const inStockOnly = document.getElementById('inStockOnly').checked;

            // Show loading indicator
            const productList = document.getElementById('productList');
            productList.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Searching products...</p><p class="small text-muted">Retrieving all matching results...</p></div>';

            console.log(`Searching for products with query: ${query}, search_type: ${searchType}, in_stock_only: ${inStockOnly}`);

            fetch(`/pos/search_products?query=${encodeURIComponent(regexQuery)}&search_type=${searchType}&in_stock_only=${inStockOnly}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server responded with status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log(`Search returned ${data.length} products`);

                    // If this is a barcode search and exactly one product is found, add it to cart automatically
                    if (isNumeric && data.length === 1) {
                        const product = data[0];
                        console.log('Barcode scan detected. Auto-adding product to cart:', product.title);

                        // If the product has multiple variants, show the variant selection modal
                        if (product.variants && product.variants.length > 1) {
                            showVariantModal(product, 1);
                        } else if (product.variants && product.variants.length === 1) {
                            // If there's only one variant, add it directly to cart
                            addToCart(product, product.variants[0], 1);

                            // Show a brief success message
                            const successMsg = document.createElement('div');
                            successMsg.className = 'alert alert-success fixed-top w-50 mx-auto mt-2 text-center';
                            successMsg.innerHTML = `<strong>Added to cart:</strong> ${product.title}`;
                            document.body.appendChild(successMsg);

                            // Remove the message after 2 seconds
                            setTimeout(() => {
                                successMsg.remove();
                            }, 2000);

                            // Clear the search input
                            document.getElementById('searchInput').value = '';

                            // Clear the product list
                            productList.innerHTML = '';

                            // Show saved searches and quick access sections again
                            toggleSidebarSections(true);

                            // Focus the search input for the next scan
                            focusSearchInput();

                            return;
                        }
                    }

                    // If not auto-added to cart, display the products as usual
                    displayProducts(data);
                    focusSearchInput();
                })
                .catch(error => {
                    console.error('Error searching products:', error);
                    productList.innerHTML = `<div class="text-center p-3 text-danger">Error searching products: ${error.message}</div>`;
                });
        } else {
            // Show saved searches and quick access sections when search is cleared
            toggleSidebarSections(true);
            document.getElementById('productList').innerHTML = '';
            focusSearchInput();
        }
    }

    quicklinkSearchBtn.addEventListener('click', function() {
        const query = quicklinkSearchInput.value.trim();
        if (query.length > 0) {
            const isNumeric = !isNaN(query);
            const searchType = isNumeric ? 'barcode' : 'title';
            const regexQuery = query.replace(/\s+/g, '\\s*');
            // Always include all products regardless of stock status when searching for quicklinks
            const inStockOnly = false;

            console.log('Searching for quicklink products with query:', query);

            fetch(`/pos/search_products?query=${encodeURIComponent(regexQuery)}&search_type=${searchType}&in_stock_only=${inStockOnly}`)
                .then(response => {
                    console.log('Search response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Search results:', data);
                    quicklinkProductList.innerHTML = '';

                    if (data.length === 0) {
                        quicklinkProductList.innerHTML = '<li class="list-group-item">No products found</li>';
                        return;
                    }

                    data.forEach(product => {
                        const li = document.createElement('li');
                        li.className = 'list-group-item';
                        li.innerHTML = `
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>${product.title}</strong><br>
                                    <small>${product.expansionName || 'Unknown'} - ${product.rarity || 'Unknown'}</small><br>
                                    <small>Price: $${product.variants[0].price}</small>
                                </div>
                                <button class="btn btn-primary btn-sm select-quicklink-product-btn">Select</button>
                            </div>
                        `;
                        li.querySelector('.select-quicklink-product-btn').addEventListener('click', () => {
                            selectedQuicklinkProductId.value = product.id;
                            selectedQuicklinkProductName.textContent = product.title;
                            console.log('Selected product ID:', product.id);
                        });
                        quicklinkProductList.appendChild(li);
                    });
                })
                .catch(error => {
                    console.error('Error searching for products:', error);
                    quicklinkProductList.innerHTML = '<li class="list-group-item">Error searching for products</li>';
                });
        } else {
            quicklinkProductList.innerHTML = '<li class="list-group-item">Please enter a search term</li>';
        }
    });

    // Also add event listener for Enter key in the quicklink search input
    quicklinkSearchInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            quicklinkSearchBtn.click();
        }
    });

    document.querySelectorAll('.select-quicklink-btn').forEach(button => {
        button.addEventListener('click', function() {
            const quicklinkNumber = this.closest('.product-type-card').dataset.quicklinkNumber;
            selectedQuicklinkNumber.value = quicklinkNumber;
            selectedQuicklinkProductId.value = '';
            selectedQuicklinkProductName.textContent = 'No product selected';
            quicklinkModal.style.display = 'block';
        });
    });

    saveQuicklinkBtn.addEventListener('click', function() {
        const quicklinkNumber = selectedQuicklinkNumber.value;
        const productId = selectedQuicklinkProductId.value;
        const tillId = document.getElementById('tillId').value;

        if (!productId || productId === 'undefined') {
            alert('Please select a product for the quicklink.');
            return;
        }

        console.log('Saving quicklink:', {
            till_id: tillId,
            quicklink_number: quicklinkNumber,
            product_id: productId
        });

        fetch('/pos/save_quicklink', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                till_id: tillId,
                quicklink_number: quicklinkNumber,
                product_id: productId
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                try {
                    const quicklinkCard = document.querySelector(`.product-type-card[data-quicklink-number="${quicklinkNumber}"]`);
                    if (!quicklinkCard) {
                        console.error(`Quicklink card not found for number: ${quicklinkNumber}`);
                        throw new Error('Quicklink card not found');
                    }

                    const productNameElement = quicklinkCard.querySelector('.quicklink-product-name');
                    if (!productNameElement) {
                        console.error('Product name element not found in quicklink card');
                        throw new Error('Product name element not found');
                    }
                    productNameElement.textContent = selectedQuicklinkProductName.textContent;

                    // Update the mt-auto div with the new UI
                    const mtAutoDiv = quicklinkCard.querySelector('.mt-auto');
                    if (!mtAutoDiv) {
                        console.error('mt-auto div not found in quicklink card');
                        throw new Error('mt-auto div not found');
                    }

                    // Replace the content with the new UI structure
                    mtAutoDiv.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="d-flex align-items-center">
                                <button class="btn btn-sm btn-outline-primary select-quicklink-btn" title="Edit product">
                                    <i class="fas fa-pencil-alt"></i>
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <label for="quicklink-qty-${quicklinkNumber}" class="me-2 text-white-50">Qty:</label>
                                <input type="number" id="quicklink-qty-${quicklinkNumber}" class="form-control form-control-sm quicklink-qty" value="1" min="1" style="width: 60px;">
                            </div>
                        </div>
                        <button class="btn btn-secondary w-100 add-quicklink-item-btn">${selectedQuicklinkProductName.textContent}</button>
                    `;

                    // Set the product ID on the add button
                    const addButton = quicklinkCard.querySelector('.add-quicklink-item-btn');
                    if (addButton) {
                        addButton.dataset.productId = productId;
                    }

                    quicklinkModal.style.display = 'none';
                    console.log('Quicklink UI updated successfully');
                    // Update the visibility of the Quick Access section
                    updateQuickAccessSectionVisibility();
                } catch (error) {
                    console.error('Error updating UI:', error);
                    alert('Quicklink saved, but there was an error updating the UI: ' + error.message);
                    quicklinkModal.style.display = 'none';
                    // Still update visibility even if there was an error
                    updateQuickAccessSectionVisibility();
                }
            } else {
                alert('Error saving quicklink: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error saving quicklink:', error);
            alert('An error occurred while saving the quicklink: ' + error.message);
        });
    });

    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('add-quicklink-item-btn')) {
            const quicklinkCard = event.target.closest('.product-type-card');
            const productId = event.target.dataset.productId;
            const quantityInput = quicklinkCard.querySelector('.quicklink-qty');
            const quantity = quantityInput ? parseInt(quantityInput.value) || 1 : 1;

            fetch(`/pos/get_product/${productId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const product = data.product;
                        const variant = product.variant;
                        addToCart(product, variant, quantity);
                    } else {
                        alert('Error adding product to cart: ' + data.message);
                    }
                })
                .catch(error => console.error('Error:', error));
        }
    });

    function generateVariantSummary(variants) {
        return `<div class="variant-summary">${variants.map(variant => `
            <div>${variant.title}: ${currencySymbol}${variant.price} (In Stock: ${variant.inventory_quantity || 0})</div>
        `).join('')}</div>`;
    }

    function showVariantModal(product, qty) {
        variantList.innerHTML = '';
        product.variants.forEach(variant => {
            const li = document.createElement('li');
            li.className = 'list-group-item';
            const isOutOfStock = variant.inventory_quantity <= 0;
            li.innerHTML = `
                <div class="d-flex justify-content-between">
                    <div>
                        <strong>${variant.title}</strong><br>
                        <small>Price: ${currencySymbol}${variant.price}</small><br>
                        <small class="${isOutOfStock ? 'text-danger' : ''}">In Stock: ${variant.inventory_quantity || 0}${isOutOfStock ? ' (Out of Stock)' : ''}</small>
                    </div>
                    <div>
                        <input type="number" class="form-control qty-input-variant" value="${qty}" min="1" style="width: 70px;">
                        <button class="btn btn-primary btn-sm select-variant-confirm-btn mt-2 ${isOutOfStock ? 'out-of-stock-btn' : ''}">
                            ${isOutOfStock ? 'Add (Out of Stock)' : 'Add to Cart'}
                        </button>
                    </div>
                </div>
            `;
            li.querySelector('.select-variant-confirm-btn').addEventListener('click', () => {
                const selectedQty = parseInt(li.querySelector('.qty-input-variant').value);
                addToCart(product, variant, selectedQty);
                variantModal.style.display = "none";
                // Don't clear search results when adding to cart
                // clearResults();
                updateTotals();
            });
            variantList.appendChild(li);
        });
        variantModal.style.display = "block";
    }

    // Function to adjust Shopify inventory when items are added to or removed from the cart
    function adjustShopifyInventory(product, variant, quantity, action, forceAdjust = false) {
        // Skip for custom items
        if (product.is_custom || (variant.id && variant.id.toString().startsWith('custom_'))) {
            console.log('Skipping inventory adjustment for custom item');
            return;
        }

        // Skip inventory adjustment when adding to cart (unless forced)
        // Only adjust inventory for held sales, layaways, or when removing items from cart
        if (action === 'add' && !forceAdjust) {
            console.log('Skipping inventory adjustment when adding to cart');
            return;
        }

        console.log(`Adjusting inventory for ${action} action:`, product, variant, quantity);

        // Call the backend endpoint to adjust inventory
        fetch('/pos/adjust_cart_inventory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                item: {
                    variant_id: variant.id,
                    quantity: quantity,
                    is_custom: product.is_custom
                },
                action: action, // 'add' or 'remove'
                quantity: quantity,
                force_adjust: forceAdjust // Flag to force adjustment for held sales/layaways
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`Inventory ${action === 'add' ? 'deducted' : 'restored'} successfully:`, data.message);
            } else {
                console.error(`Failed to adjust inventory:`, data.message);
            }
        })
        .catch(error => {
            console.error(`Error adjusting inventory:`, error);
        });
    }

    // Define addToCart function and expose it to the global scope
    function addToCart(product, variant, qty, saveToStorage = false, fromHeldSale = false) {
        console.log('Adding to cart:', product, variant, qty, 'fromHeldSale:', fromHeldSale);
        if (!product || !variant) {
            console.error('Invalid product or variant');
            return;
        }
        const cartList = document.getElementById('cartList');
        if (cartList.querySelector('.list-group-item')?.textContent === 'No items in cart') {
            cartList.innerHTML = '';
        }

        const variantId = variant.id ? variant.id.toString() : 'custom_' + Date.now();
        const existingItem = [...cartList.children].find(item => item.dataset.id === variantId);
        if (existingItem) {
            console.log('Updating existing item');
            const quantity = existingItem.querySelector('.quantity');
            const oldQty = parseInt(quantity.value);
            const newQty = oldQty + qty;
            quantity.value = newQty;

            // No longer adjusting inventory when adding to cart

            // Scroll to the updated item and make sure totals are visible
            const cartListContainer = document.querySelector('.cart-list-container');
            if (cartListContainer) {
                // First scroll to the updated item
                existingItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

                // Then scroll up slightly to ensure totals section is visible
                setTimeout(() => {
                    // Calculate how much to scroll up to see totals
                    const totalsSectionHeight = 200; // Approximate height of totals section
                    const visibleHeight = cartListContainer.clientHeight;
                    const scrollPosition = cartListContainer.scrollHeight - visibleHeight + totalsSectionHeight;

                    // Only scroll up if we need to (if cart is very short, we don't need to)
                    if (cartListContainer.scrollHeight > visibleHeight + totalsSectionHeight) {
                        cartListContainer.scrollTop = scrollPosition;
                    }
                }, 300); // Longer timeout to allow smooth scrolling to complete
            }
        } else {
            console.log('Adding new item');

            // Mark the item as from a held sale if applicable
            if (fromHeldSale || product.from_held_sale) {
                console.log('Item is from a held sale, skipping inventory adjustment');
            }
            const li = document.createElement('li');
            li.className = 'list-group-item';
            li.dataset.id = variantId;

            // Check if this item is from a terminal order (no image should be shown)
            const showImage = !product.from_terminal_order;
            const imageUrl = showImage ? (product.image || variant.image || 'https://via.placeholder.com/50') : '';

            li.innerHTML = `
                <div class="cart-item position-relative">
                    <button class="cart-remove-btn" title="Remove item"><i class="fas fa-times"></i></button>

                    <div class="cart-item-header d-flex justify-content-between align-items-start mb-2">
                        ${showImage ? `
                        <div class="cart-item-image me-2" style="--enlarged-image: url('${imageUrl}');">
                            <img src="${imageUrl}" alt="${product.title}" class="cart-thumbnail" width="40" height="40">
                        </div>
                        ` : ''}
                        <div class="cart-item-details flex-grow-1">
                            <div class="item-title fw-bold">${product.title}</div>
                            <div class="item-variant text-muted small">${variant.title || 'Custom Item'}</div>
                        </div>
                    </div>

                    <div class="cart-item-controls">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="quantity-control-wrapper">
                                <div class="d-flex align-items-center">
                                    <div class="quantity-label me-2">
                                        <span class="small text-muted">Qty</span>
                                    </div>
                                    <div class="quantity-control d-flex align-items-center">
                                        <button class="btn btn-sm btn-outline-secondary quantity-btn quantity-decrease"><i class="fas fa-minus"></i></button>
                                        <input type="number" class="form-control form-control-sm quantity" value="${qty}" min="1" style="width: 45px; text-align: center;">
                                        <button class="btn btn-sm btn-outline-secondary quantity-btn quantity-increase"><i class="fas fa-plus"></i></button>
                                    </div>
                                </div>
                            </div>

                            <div class="tax-exempt-wrapper">
                                <div class="form-check">
                                    <input class="form-check-input tax-exempt-checkbox" type="checkbox" id="taxExempt-${variantId}">
                                    <label class="form-check-label small text-muted" for="taxExempt-${variantId}">
                                        Tax Exempt
                                    </label>
                                </div>
                            </div>

                            <div class="price-control-wrapper">
                                <div class="d-flex align-items-center">
                                    <div class="price-input-group">
                                        <span class="small text-muted me-2">Price</span>
                                        <div class="price-display">
                                            <span class="currency-symbol me-1">${currencySymbol}</span>
                                            <span class="price fw-bold">${parseFloat(variant.price).toFixed(2)}</span>
                                            <button class="btn btn-sm btn-primary edit-price-btn ms-2" title="Edit price"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-secondary add-discount-btn ms-2" title="Add discount"><i class="fas fa-tag"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="item-total mt-2 text-end">
                            <span class="small text-muted">Total: </span>
                            <span class="currency-symbol">${currencySymbol}</span>
                            <span class="item-total-price fw-bold">${(parseFloat(variant.price) * qty).toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            `;
li.querySelector('.cart-remove-btn').addEventListener('click', () => {
                // No need to restore inventory since we didn't adjust it when adding to cart
                li.remove();
                updateTotals();
                if (saveToStorage) {
                    const cartItems = getCartItems();
                    if (cartItems.length === 0) {
                        localStorage.removeItem('posCartItems');
                        cartList.innerHTML = '<li class="list-group-item empty-cart-message" style="background: transparent; border: 1px dashed rgba(255, 255, 255, 0.2); color: rgba(248, 250, 252, 0.7); text-align: center; padding: 1.5rem; border-radius: 0.5rem; margin: 0.5rem 0;">No items in cart</li>';
                    } else {
                        saveCartToLocalStorage();
                    }
                }
            });
            // Add event listeners for quantity buttons
            const quantityInput = li.querySelector('.quantity');
            const decreaseBtn = li.querySelector('.quantity-decrease');
            const increaseBtn = li.querySelector('.quantity-increase');
            const taxExemptCheckbox = li.querySelector('.tax-exempt-checkbox');

            // Add event listener for tax exempt checkbox
            if (taxExemptCheckbox) {
                taxExemptCheckbox.addEventListener('change', function() {
                    updateTotals();
                    if (saveToStorage) {
                        saveCartToLocalStorage();
                    }
                });
            }

            quantityInput.addEventListener('input', function() {
                updateTotals();
                if (saveToStorage) {
                    saveCartToLocalStorage();
                }
            });

            decreaseBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value) || 1;
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                    updateTotals();
                    if (saveToStorage) {
                        saveCartToLocalStorage();
                    }
                }
            });

            increaseBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value) || 1;
                quantityInput.value = currentValue + 1;
                updateTotals();
                if (saveToStorage) {
                    saveCartToLocalStorage();
                }
            });

            // Add discount button event listener
            if (window.addDiscountControlsToCartItem) {
                // Add the discount controls to the item
                window.addDiscountControlsToCartItem(li);
            }

            cartList.appendChild(li);

            // Scroll to the newly added item and make sure totals are visible
            const cartListContainer = document.querySelector('.cart-list-container');
            if (cartListContainer) {
                // First scroll to the bottom to see the new item
                cartListContainer.scrollTop = cartListContainer.scrollHeight;

                // Then scroll up slightly to ensure totals section is visible
                setTimeout(() => {
                    // Calculate how much to scroll up to see totals
                    const totalsSectionHeight = 200; // Approximate height of totals section
                    const visibleHeight = cartListContainer.clientHeight;
                    const scrollPosition = cartListContainer.scrollHeight - visibleHeight + totalsSectionHeight;

                    // Only scroll up if we need to (if cart is very short, we don't need to)
                    if (cartListContainer.scrollHeight > visibleHeight + totalsSectionHeight) {
                        cartListContainer.scrollTop = scrollPosition;
                    }
                }, 100);
            }
        }

        console.log('Cart updated');
        updateTotals();
        if (saveToStorage) {
            saveCartToLocalStorage();
        }
        clearResults();
        focusSearchInput();
    }

    function clearResults() {
        // Only clear the search input, not the product list
        // This allows users to add multiple items from search results
        document.getElementById('searchInput').value = '';
    }

    function focusSearchInput() {
        document.getElementById('searchInput').focus();
    }

    // Make updateTotals function globally accessible
    window.updateTotals = function updateTotals() {
        console.log('updateTotals called');
        const cartList = document.getElementById('cartList');

        // Update cart item count
        const cartItemCount = document.getElementById('cartItemCount');
        let totalItems = 0;

        if (cartList.children.length > 0 && cartList.children[0].textContent !== 'No items in cart') {
            // Count total items including quantities
            [...cartList.children].forEach(item => {
                const quantityElement = item.querySelector('.quantity');
                if (quantityElement) {
                    totalItems += parseInt(quantityElement.value) || 0;
                }
            });
        }

        // Update the cart count badge
        if (cartItemCount) {
            cartItemCount.textContent = totalItems;
            // Show/hide badge based on count
            if (totalItems > 0) {
                cartItemCount.style.display = 'inline-block';
            } else {
                cartItemCount.style.display = 'none';
            }
        }

        // Respect the "Use Store Credit" checkbox
        const useStoreCreditCheckbox = document.getElementById('useStoreCreditCheckbox');
        if (useStoreCreditCheckbox && !useStoreCreditCheckbox.checked) {
            const storeCreditApplied = document.getElementById('storeCreditApplied');
            if (storeCreditApplied) {
                storeCreditApplied.textContent = '0.00';
            }
            const storeCreditMsg = document.querySelector('.store-credit-message');
            if (storeCreditMsg) {
                storeCreditMsg.remove();
            }
            // Also reset hidden input value if exists
            const storeCreditAmountInput = document.getElementById('storeCreditAmount');
            if (storeCreditAmountInput) {
                storeCreditAmountInput.value = '0';
            }
        }

        let subtotal = 0;
        let totalTax = 0;

        if (cartList.children.length > 0 && cartList.children[0].textContent !== 'No items in cart') {
            [...cartList.children].forEach(item => {
                const priceElement = item.querySelector('.price');
                const quantityElement = item.querySelector('.quantity');
                const itemTotalElement = item.querySelector('.item-total-price');
                const taxExemptCheckbox = item.querySelector('.tax-exempt-checkbox');

                if (priceElement && quantityElement) {
                    // Get the current price (which is already discounted if a discount was applied)
                    const price = parseFloat(priceElement.textContent);
                    const qty = parseInt(quantityElement.value);
                    const isTaxExempt = taxExemptCheckbox ? taxExemptCheckbox.checked : false;

                    if (!isNaN(price) && !isNaN(qty)) {
                        // Use TaxCalculator to handle inclusive/exclusive logic
                        const result = window.TaxCalculator.calculateItemTax(price, qty, isTaxExempt);
                        subtotal += result.basePrice * qty;
                        totalTax += result.taxAmount;

                        // Store the tax amount as a data attribute on the item for later use
                        item.dataset.taxAmount = result.taxAmount.toFixed(2);

                        // Calculate the item total (price * quantity) WITHOUT tax
                        // Tax is calculated separately and added at the order level
                        const itemTotal = price * qty;

                        // Update the item total display
                        if (itemTotalElement) {
                            itemTotalElement.textContent = itemTotal.toFixed(2);
                        }
                    }
                }
            });
        }

        // Log the calculated subtotal for debugging
        console.log('Calculated subtotal:', subtotal.toFixed(2));

        // Handle global discount (not line item discounts)
        const discount = parseFloat(document.getElementById('discount').value) || 0;
        const discountType = document.getElementById('discountType').value;
        let discountAmount = 0;

        if (discountType === 'percent') {
            discountAmount = subtotal * (discount / 100);
        } else {
            discountAmount = discount;
        }

        // Update the discount display (only global discount)
        const discountAmountElement = document.getElementById('discountAmount');
        if (discountAmountElement) {
            discountAmountElement.textContent = discountAmount.toFixed(2);

            // Show/hide the discount row based on whether there are any discounts
            const discountRow = document.getElementById('discountRow');
            if (discountRow) {
                discountRow.style.display = discountAmount > 0 ? 'flex' : 'none';
            }
        }

        // Calculate the final total (subtotal + tax - global discount)
        // Line item discounts are already reflected in the subtotal
        const total = Math.max(0, subtotal + totalTax - discountAmount);

        // Update the displayed totals
        document.getElementById('subtotal').textContent = subtotal.toFixed(2);
        document.getElementById('tax').textContent = totalTax.toFixed(2);
        document.getElementById('total').textContent = total.toFixed(2);

        // Log the final total for debugging
        console.log('Final total:', total.toFixed(2));

        // Update the status to handle gift card calculations
        updateGiftCardStatus();

        // Save cart to localStorage if needed
        if (typeof saveCartToLocalStorage === 'function') {
            saveCartToLocalStorage();
        }
    }

    function updateGiftCardStatus() {
        const giftCardAmount = parseFloat(document.getElementById('giftCardAmount').value) || 0;
        const total = parseFloat(document.getElementById('total').textContent);

        if (giftCardAmount > 0) {
            // Limit gift card amount to the total
            const appliedAmount = Math.min(giftCardAmount, total);
            document.getElementById('giftCardApplied').textContent = appliedAmount.toFixed(2);

            // Update the gift card message if it exists
            const giftCardMsg = document.querySelector('.gift-card-message');
            if (giftCardMsg) {
                if (appliedAmount >= total) {
                    giftCardMsg.textContent = `Full amount will be charged to gift card`;
                } else {
                    giftCardMsg.textContent = `$${appliedAmount.toFixed(2)} from gift card will be applied at checkout`;
                }
            }
        }
    }

    // Toggle discount section
    document.getElementById('toggleDiscountBtn').addEventListener('click', function() {
        const discountContent = document.getElementById('discountContent');
        const isVisible = discountContent.style.display !== 'none';

        if (isVisible) {
            discountContent.style.display = 'none';
            this.classList.remove('active');
            // Clear discount when hiding
            document.getElementById('discount').value = '';
            updateTotals();
            saveCartToLocalStorage();
        } else {
            discountContent.style.display = 'block';
            this.classList.add('active');
            // Focus on the discount input
            document.getElementById('discount').focus();
        }
    });

    document.getElementById('discount').addEventListener('input', function() {
        updateTotals();
        saveCartToLocalStorage();
    });

    document.getElementById('discountType').addEventListener('change', function() {
        updateTotals();
        saveCartToLocalStorage();
    });

    document.getElementById('cashBtn').addEventListener('click', function() {
        const totalAmount = parseFloat(document.getElementById('total').textContent);
        document.getElementById('totalAmountDue').textContent = totalAmount.toFixed(2);
        document.getElementById('amountGiven').value = '';
        document.getElementById('changeDue').textContent = '0.00';
        const giftCardAmount = parseFloat(document.getElementById('giftCardAmount').value) || 0;
        document.getElementById('giftCardAmountUsed').textContent = giftCardAmount.toFixed(2);
        cashPaymentModal.style.display = 'block';
        document.getElementById('amountGiven').focus();
    });

    document.getElementById('cardBtn').addEventListener('click', function() {
        const totalAmount = parseFloat(document.getElementById('total').textContent);
        document.getElementById('totalCardAmountDue').textContent = totalAmount.toFixed(2);
        const giftCardAmount = parseFloat(document.getElementById('giftCardAmount').value) || 0;
        document.getElementById('cardGiftCardAmountUsed').textContent = giftCardAmount.toFixed(2);
        cardPaymentModal.style.display = 'block';
    });

    document.getElementById('amountGiven').addEventListener('input', function() {
        const amountGiven = parseFloat(document.getElementById('amountGiven').value) || 0;
        const totalAmountDue = parseFloat(document.getElementById('totalAmountDue').textContent);
        const changeDue = amountGiven - totalAmountDue;
        document.getElementById('changeDue').textContent = changeDue.toFixed(2);
    });

    document.getElementById('completeCashPaymentBtn').addEventListener('click', function() {
        completeTransaction('cash');
    });

    document.getElementById('completeCardPaymentBtn').addEventListener('click', function() {
        completeTransaction('card');
    });

    const selectCustomerBtn = document.getElementById('selectCustomerBtn');
    const searchCustomerBtn = document.getElementById('searchCustomerBtn');
    const customerEmailInput = document.getElementById('customerEmailInput');

    if (selectCustomerBtn) {
        selectCustomerBtn.addEventListener('click', function() {
            customerSearchModal.style.display = 'block';
        });
    }

    // Function to perform customer search
    function performCustomerSearch() {
        const searchTerm = customerSearchInput.value.trim();
        const searchType = document.getElementById('customerSearchType').value;
        if (searchTerm) {
            fetch(`/pos/search_customer?search=${encodeURIComponent(searchTerm)}&type=${searchType}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCustomerSearchResults(data.customers);
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => console.error('Error:', error));
        } else {
            alert('Please enter a search term.');
        }
    }

    // Search button click event
    if (searchCustomerBtn) {
        searchCustomerBtn.addEventListener('click', performCustomerSearch);
    }

    // Add Enter key press event to customer search input
    const customerSearchInput = document.getElementById('customerSearchInput');
    if (customerSearchInput) {
        customerSearchInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault(); // Prevent form submission
                performCustomerSearch();
            }
        });
    }

    function displayCustomerSearchResults(customers) {
        const customerList = document.getElementById('customerSearchResults');
        customerList.innerHTML = '';
        customers.forEach(customer => {
            const li = document.createElement('li');
            li.className = 'list-group-item customer-result-card';

            // Determine if customer has any credit
            const hasGiftCard = parseFloat(customer.gift_card_balance) > 0;
            const hasStoreCredit = parseFloat(customer.store_credit_balance) > 0;
            const hasTotalCredit = parseFloat(customer.total_credit_balance) > 0;

            li.innerHTML = `
                <div class="customer-info">
                    <div class="customer-header">
                        <div class="customer-name">${customer.first_name} ${customer.last_name}</div>
                        <button class="btn btn-success select-customer-btn">Select</button>
                    </div>
                    <div class="customer-email"><i class="fas fa-envelope"></i> ${customer.email}</div>
                    <div class="credit-balances ${hasTotalCredit ? 'has-credit' : ''}">
                        <div class="balance-row ${hasGiftCard ? 'positive-balance' : ''}">
                            <span class="balance-label">Gift Card:</span>
                            <span class="balance-amount">${currencySymbol}${customer.gift_card_balance}</span>
                        </div>
                        <div class="balance-row ${hasStoreCredit ? 'positive-balance' : ''}">
                            <span class="balance-label">Store Credit:</span>
                            <span class="balance-amount">${currencySymbol}${customer.store_credit_balance}</span>
                        </div>
                        <div class="balance-row total-balance ${hasTotalCredit ? 'positive-balance' : ''}">
                            <span class="balance-label">Total Credit:</span>
                            <span class="balance-amount">${currencySymbol}${customer.total_credit_balance}</span>
                        </div>
                    </div>
                </div>
            `;

            li.querySelector('.select-customer-btn').addEventListener('click', () => selectCustomer(customer));
            customerList.appendChild(li);
        });
    }

    // Define the original selectCustomer function
    function selectCustomer(customer) {
        // No need to reassign to window.selectCustomer here
        console.log("selectCustomer called with:", customer);

        // If customer is a DOM element (clicked from search results), extract data from attributes
        if (customer instanceof HTMLElement) {
            const customerElement = customer;
            customer = {
                id: customerElement.dataset.customerId,
                name: customerElement.dataset.customerName,
                email: customerElement.dataset.customerEmail,
                gift_card_balance: customerElement.dataset.giftCardBalance,
                store_credit_balance: customerElement.dataset.storeCreditBalance,
                total_credit_balance: customerElement.dataset.totalCreditBalance
            };
        }

        // Hide the customer search results dropdown if it's open
        const inlineCustomerSearchResults = document.getElementById('inlineCustomerSearchResults');
        if (inlineCustomerSearchResults) {
            inlineCustomerSearchResults.classList.add('d-none');
        }

        // Clear the search input
        const inlineCustomerSearch = document.getElementById('inlineCustomerSearch');
        if (inlineCustomerSearch) {
            inlineCustomerSearch.value = '';
        }

        // Close any open customer search modal
        const customerSearchModal = document.getElementById('customerSearchModal');
        if (customerSearchModal && customerSearchModal.style.display === 'block') {
            customerSearchModal.style.display = 'none';
        }

        // Display customer details
        document.getElementById('customerDetails').style.display = 'block';

        // Handle different customer object formats (from modal vs inline search)
        let firstName, lastName, email, giftCardBalance, storeCreditBalance, totalCreditBalance, customerId;

        if (customer.first_name !== undefined) {
            // Format from modal search
            firstName = customer.first_name || '';
            lastName = customer.last_name || '';
            email = customer.email || '';
            giftCardBalance = customer.gift_card_balance || '0.00';
            storeCreditBalance = customer.store_credit_balance || '0.00';
            totalCreditBalance = customer.total_credit_balance || '0.00';
            customerId = customer.shopify_id || customer.id;
        } else {
            // Format from inline search
            const nameParts = (customer.name || '').split(' ');
            firstName = nameParts[0] || '';
            lastName = nameParts.slice(1).join(' ') || '';
            email = customer.email || '';
            giftCardBalance = customer.gift_card_balance || '0.00';
            storeCreditBalance = customer.store_credit_balance || '0.00';
            totalCreditBalance = customer.total_credit_balance || '0.00';
            customerId = customer.id;
        }

        // Update the UI
        document.getElementById('customerName').innerText = `Name: ${firstName} ${lastName}`;
        document.getElementById('customerEmail').innerText = `Email: ${email}`;
        document.getElementById('giftCardBalance').innerText = `Gift Card Balance: $${giftCardBalance}`;
        document.getElementById('storeCreditBalance').innerText = `Store Credit Balance: $${storeCreditBalance}`;
        document.getElementById('totalCreditBalance').innerText = `Total Credit Balance: $${totalCreditBalance}`;
        document.getElementById('customerId').value = customerId;

        // Store the selected customer in the global object for other functions to use
        window.selectedCustomer = {
            id: customerId,
            name: `${firstName} ${lastName}`.trim(),
            email: email,
            giftCardBalance: giftCardBalance,
            storeCreditBalance: storeCreditBalance,
            totalCreditBalance: totalCreditBalance
        };
        document.getElementById('giftCardId').value = customer.gift_card_id;

        // Store credit account ID if available
        if (customer.store_credit_account_id) {
            // Add a hidden input for store credit account ID if it doesn't exist
            let storeCreditAccountIdInput = document.getElementById('storeCreditAccountId');
            if (!storeCreditAccountIdInput) {
                storeCreditAccountIdInput = document.createElement('input');
                storeCreditAccountIdInput.type = 'hidden';
                storeCreditAccountIdInput.id = 'storeCreditAccountId';
                document.body.appendChild(storeCreditAccountIdInput);
            }
            storeCreditAccountIdInput.value = customer.store_credit_account_id;
        }

        customerSearchModal.style.display = 'none';

        // Set up the credit section if customer has any balance
        const giftCardAmount = parseFloat(customer.gift_card_balance) || 0;
        const storeCreditAmount = parseFloat(customer.store_credit_balance) || 0;
        const totalCreditAmount = parseFloat(customer.total_credit_balance) || 0;

        // Update credit sections based on available balances
        if (giftCardAmount > 0 || storeCreditAmount > 0) {
            // Make credit section visible
            const creditSection = document.getElementById('creditSection');
            creditSection.style.display = 'block';

            // Store credit amounts for potential use
            document.getElementById('giftCardAmount').value = giftCardAmount;
            document.getElementById('giftCardApplied').textContent = '0.00';
            document.getElementById('storeCreditAmount').value = storeCreditAmount;
            document.getElementById('storeCreditApplied').textContent = '0.00';

            // Update available credit displays
            if (giftCardAmount > 0) {
                document.getElementById('giftCardAvailable').textContent = `Available: ${currencySymbol}${giftCardAmount.toFixed(2)}`;
            }

            if (storeCreditAmount > 0) {
                document.getElementById('storeCreditAvailable').textContent = `Available: ${currencySymbol}${storeCreditAmount.toFixed(2)}`;
            }

            // Add a message to indicate available credit
            const creditMsg = document.createElement('div');
            creditMsg.className = 'alert alert-info mt-2';
            if (giftCardAmount > 0 && storeCreditAmount > 0) {
                creditMsg.textContent = `Available Credit: $${totalCreditAmount.toFixed(2)} (Gift Card: $${giftCardAmount.toFixed(2)}, Store Credit: $${storeCreditAmount.toFixed(2)})`;
            } else if (giftCardAmount > 0) {
                creditMsg.textContent = `Gift Card Balance Available: $${giftCardAmount.toFixed(2)}`;
            } else if (storeCreditAmount > 0) {
                creditMsg.textContent = `Store Credit Balance Available: $${storeCreditAmount.toFixed(2)}`;
            }

            // Remove any existing message
            const existingMsg = document.querySelector('.gift-card-message');
            if (existingMsg) {
                existingMsg.remove();
            }

            // Add the new message
            creditMsg.classList.add('gift-card-message');
            document.getElementById('customerDetails').appendChild(creditMsg);

            // Conditionally apply store credit if checkbox is checked
            const useStoreCreditCheckbox = document.getElementById('useStoreCreditCheckbox');
            if (storeCreditAmount > 0 && useStoreCreditCheckbox && useStoreCreditCheckbox.checked && typeof window.autoApplyStoreCredit === 'function') {
                console.log("Auto-applying store credit from selectCustomer function");
                setTimeout(() => {
                    window.autoApplyStoreCredit();
                }, 500);
            }
        } else {
            // Hide credit section if no balance
            document.getElementById('creditSection').style.display = 'none';
        }

        // Update totals to trigger credit calculations
        updateTotals();
    }

    function applyGiftCardPayment(maxAmount) {
        const total = parseFloat(document.getElementById('total').textContent);
        const customerId = document.getElementById('customerId').value;

        if (!customerId || maxAmount <= 0 || total <= 0) {
            return; // Nothing to do
        }

        // Calculate amount to use (don't use more than the total)
        const amountToUse = Math.min(maxAmount, total);

        // Call server to apply gift card
        fetch('/pos/apply_gift_card', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                customer_id: customerId,
                amount: amountToUse
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Store gift card ID and amount for later use in transaction
                document.getElementById('giftCardId').value = data.gift_card_id;
                document.getElementById('giftCardAmount').value = data.amount;
                document.getElementById('giftCardApplied').textContent = data.amount.toFixed(2);

                // Add a message to indicate gift card was applied
                const giftCardMsg = document.createElement('div');
                giftCardMsg.className = 'alert alert-success mt-2';
                giftCardMsg.textContent = `$${data.amount.toFixed(2)} from gift card will be applied at checkout`;

                // Remove any existing message
                const existingMsg = document.querySelector('.gift-card-message');
                if (existingMsg) {
                    existingMsg.remove();
                }

                // Add the new message
                giftCardMsg.classList.add('gift-card-message');
                document.getElementById('customerDetails').appendChild(giftCardMsg);
            } else {
                console.error('Error applying gift card:', data.message);
            }
        })
        .catch(error => {
            console.error('Error applying gift card:', error);
        });
    }

    function completeTransaction(paymentMethod) {
        const cartList = document.getElementById('cartList');
        const items = getCartItems();
        const originalTotal = parseFloat(document.getElementById('total').textContent);
        const tillId = document.getElementById('tillId').value;
        const customerId = document.getElementById('customerId').value;
        const giftCardId = document.getElementById('giftCardId').value;
        const giftCardAmount = parseFloat(document.getElementById('giftCardAmount')?.value || 0);
        const storeCreditAccountId = document.getElementById('storeCreditAccountId')?.value;
        const storeCreditAmount = parseFloat(document.getElementById('storeCreditAmount')?.value || 0);
        const employeeName = document.getElementById('employeeName').value;
        const heldSaleId = document.getElementById('heldSaleId').value;
        const taxRate = parseFloat(document.getElementById('taxRate').value) / 100;

        // Apply store credit and gift card
        const appliedGiftCard = parseFloat(document.getElementById('giftCardApplied').textContent || 0);
        let appliedStoreCredit = parseFloat(document.getElementById('storeCreditApplied').textContent || 0);

        // Respect the "Use Store Credit" checkbox
        const useStoreCreditCheckbox = document.getElementById('useStoreCreditCheckbox');
        if (useStoreCreditCheckbox && !useStoreCreditCheckbox.checked) {
            appliedStoreCredit = 0;
        }

        // Calculate the remaining balance after applying credits
        const remainingBalance = Math.max(0, originalTotal - appliedGiftCard - appliedStoreCredit);

        // Determine if we're using gift card or store credit
        const isUsingGiftCard = giftCardId && appliedGiftCard > 0;
        const isUsingStoreCredit = storeCreditAccountId && appliedStoreCredit > 0;

        // If no payment is needed (fully covered by credits), use storecredit as the payment method
        const effectivePaymentMethod = remainingBalance <= 0 ? 'storecredit' : paymentMethod;

        console.log(`Transaction Summary:
          Original Total: ${originalTotal}
          Gift Card Applied: ${appliedGiftCard}
          Store Credit Applied: ${appliedStoreCredit}
          Remaining Balance: ${remainingBalance}
          Payment Method: ${effectivePaymentMethod}
        `);

        // Create line items with tax info from the stored data attributes
        const lineItems = items.map(item => {
            // Check if this item has the tax exempt checkbox checked
            const itemElement = document.querySelector(`.list-group-item[data-id="${item.variant_id}"]`);
            const isTaxExempt = itemElement ?
                itemElement.querySelector('.tax-exempt-checkbox')?.checked || false : false;

            // Get the tax amount from the data attribute we set in updateTotals()
            let itemTax = 0;
            if (itemElement && itemElement.dataset.taxAmount) {
                itemTax = parseFloat(itemElement.dataset.taxAmount);
            }

            // Add tax exempt flag and tax amount to each item
            return {
                ...item,
                tax_exempt: isTaxExempt,
                taxable: !isTaxExempt, // Explicitly set taxable property for Shopify API
                tax_amount: itemTax
            };
        });

        // Get the tax amount from the UI
        const taxAmount = parseFloat(document.getElementById('tax').textContent);

        // Check if there's a terminal order ID to include
        const terminalOrderIdInput = document.getElementById('terminalOrderId');
        const terminalOrderId = terminalOrderIdInput ? terminalOrderIdInput.value : null;

        // Create the transaction object
        const transaction = {
            items: lineItems,
            total: originalTotal, // Send the original total
            payment_method: effectivePaymentMethod,
            till_id: tillId,
            // Only include customer-related fields if customer_id exists
            ...(customerId && {
                customer_id: customerId,
                // Include gift card details only if using gift card
                ...(isUsingGiftCard && {
                    gift_card_id: giftCardId,
                    gift_card_amount: appliedGiftCard,
                    use_gift_card: true
                }),
                // Include store credit details only if using store credit
                ...(isUsingStoreCredit && {
                    store_credit_account_id: storeCreditAccountId,
                    store_credit_amount: appliedStoreCredit,
                    use_store_credit: true
                })
            }),
            // Add tax_amount to be used by the server but don't include root-level tax_lines
            // as Shopify doesn't allow tax_lines at both root and line item levels
            tax_amount: taxAmount,
            // Flag to indicate if any items are tax exempt
            has_tax_exempt_items: document.querySelectorAll('.tax-exempt-checkbox:checked').length > 0,
            // Other details
            employee_name: employeeName,
            remaining_balance: remainingBalance,
            // Don't set fulfillment_status directly, just indicate intent
            fulfill_order: true, // This will be handled by the server after order creation
            ...(document.getElementById('heldSaleId').value && { held_sale_id: heldSaleId }),
            // Include terminal order ID if present (for deletion after successful payment)
            ...(terminalOrderId && { terminal_order_id: terminalOrderId }),
            // Add note attributes for tracking payment method and customer type
            note_attributes: [
                { name: "payment_method", value: effectivePaymentMethod },
                { name: "customer_type", value: customerId ? "registered" : "guest" }
            ],
            // Add a flag to tell the server to omit the customer object entirely if no customer_id
            omit_customer_object: !customerId,
            // Add flag to notify customer of their order if one is selected
            notify_customer: customerId ? true : false,
            // Add additional logging for debugging notification issues
            send_receipt: customerId ? true : false,
            // Don't send fulfillment receipt for POS orders since the customer already has their items
            send_fulfillment_receipt: false
        };

        // Log the transaction object for debugging
        console.log("Sending order to Shopify:", JSON.stringify(transaction, null, 2));

        // Only proceed with payment if there's a remaining balance
        if (remainingBalance > 0) {
            // Show appropriate payment confirmation based on method
            if (paymentMethod === 'cash') {
                // Verify cash amount is entered for remaining balance
                const amountGiven = parseFloat(document.getElementById('amountGiven').value) || 0;
                if (amountGiven < remainingBalance) {
                    alert(`Please enter a cash amount that covers the remaining balance of ${currencySymbol}${remainingBalance.toFixed(2)}`);
                    return; // Stop transaction until proper amount is entered
                }
            } else if (paymentMethod === 'card') {
                // For card payments, just confirm the payment amount
                if (!confirm(`Process card payment for remaining balance of ${currencySymbol}${remainingBalance.toFixed(2)}?`)) {
                    return; // User cancelled the card payment
                }
            }
        }

        // IMMEDIATELY perform cleanup operations before sending to server
        // This allows the user to start a new sale without waiting for the server response

        // Close any open payment modals
        if (paymentMethod === 'cash') {
            cashPaymentModal.style.display = 'none';
        } else if (paymentMethod === 'card') {
            cardPaymentModal.style.display = 'none';
        }

        // No need to adjust inventory here - the backend will handle it
        // This prevents double inventory adjustment

        // Remove held sale if applicable
        if (heldSaleId && document.getElementById('heldSaleId').value) {
            // We don't need to adjust inventory here since the backend will handle it
            // This prevents double inventory adjustment
            removeHeldSale(heldSaleId);
        }

        // Clear the cart and reset customer details
        clearCart();
        resetCustomerDetails();

        // Clear terminal order ID if it exists
        if (terminalOrderIdInput) {
            terminalOrderIdInput.remove();
        }

        // Show success message
        const successMsg = document.createElement('div');
        successMsg.className = 'alert alert-success fixed-top w-50 mx-auto mt-2 text-center';
        successMsg.innerHTML = `<strong>Success!</strong> Transaction is being processed.`;
        document.body.appendChild(successMsg);

        // Set a guaranteed page refresh timeout - this will refresh the page even if the fetch fails
        const guaranteedRefreshTimeout = setTimeout(function() {
            console.log('Forcing page refresh after timeout');
            window.location.href = window.location.href; // Force a hard refresh
        }, 5000); // 5 seconds max wait time

        // Now process the transaction with the server asynchronously
        fetch('/pos/complete_transaction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(transaction)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Transaction response from server:', data);
            console.log('Transaction complete:', data);

            // Update success message
            successMsg.innerHTML = `<strong>Success!</strong> Transaction completed successfully.`;

            // Clear the guaranteed timeout since we got a response
            clearTimeout(guaranteedRefreshTimeout);

            // Refresh the POS page immediately
            console.log('Refreshing page after successful transaction');
            setTimeout(function() {
                window.location.href = window.location.href; // Force a hard refresh
            }, 800);
        })
        .catch(error => {
            console.error('Transaction error:', error);

            // Show error message but don't interrupt the user's workflow
            successMsg.className = 'alert alert-warning fixed-top w-50 mx-auto mt-2 text-center';
            successMsg.innerHTML = `<strong>Warning:</strong> Transaction sent but encountered an error: ${error.message || 'Unknown error'}. The order may still have been created.`;

            // Clear the guaranteed timeout since we're handling the error
            clearTimeout(guaranteedRefreshTimeout);

            // Refresh the POS page after a short delay
            console.log('Refreshing page after transaction error');
            setTimeout(function() {
                window.location.href = window.location.href; // Force a hard refresh
            }, 2000); // Shorter delay but still enough to see the error
        });
    }

    function removeHeldSale(saleId) {
        if (!saleId) return;

        fetch(`/pos/remove_held_sale/${saleId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(() => {
            // Silently continue regardless of response
            fetchHeldSales();
        })
        .catch(() => {
            // Silently ignore any errors
        });
    }

    function fetchHeldSales() {
        // Add a console log to debug
        console.log('Fetching held sales...');

        fetch('/pos/get_held_sales')
        .then(response => {
            console.log('Held sales response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Held sales data:', data);
            try {
                const heldSalesList = document.getElementById('heldSalesList');
                heldSalesList.innerHTML = '';

                if (data.success && data.held_sales && data.held_sales.length > 0) {
                    data.held_sales.forEach(sale => {
                        const li = document.createElement('li');
                        li.className = 'list-group-item held-sale-item';

                        // Calculate total amount
                        const totalAmount = sale.total || 0;
                        const formattedTotal = totalAmount.toFixed(2);

                        // Create the main sale header with item count and collapse toggle
                        li.innerHTML = `
                            <div class="held-sale-header">
                                <div class="held-sale-info">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="mb-1"><strong>Customer:</strong> ${sale.customer_name || 'No Customer'}</p>
                                            <p class="mb-1"><strong>Items:</strong> <span class="badge bg-info">${sale.items_count}</span> | <strong>Total:</strong> <span class="badge bg-success">${currencySymbol}${formattedTotal}</span></p>
                                            <p class="mb-0 text-muted small">ID: ${sale.id}</p>
                                        </div>
                                        <div>
                                            <button class="btn btn-sm btn-outline-secondary toggle-items-btn" title="Show/Hide Items">
                                                <i class="fas fa-chevron-down"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="held-sale-actions mt-2">
                                    <button class="btn btn-primary btn-sm retrieve-sale-btn" data-sale-id="${sale.id}">Retrieve Sale</button>
                                    <button class="btn btn-danger btn-sm delete-sale-btn" data-sale-id="${sale.id}">Delete Sale</button>
                                </div>
                            </div>
                            <div class="held-sale-items collapse">
                                <div class="mt-3">
                                    <h6>Items in this sale:</h6>
                                    <ul class="list-group list-group-flush sale-items-list">
                                        ${sale.items.map(item => `
                                            <li class="list-group-item py-2 px-3 bg-light">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span>${item.title}</span>
                                                        <span class="text-muted ms-2">x${item.quantity}</span>
                                                    </div>
                                                    <span>${currencySymbol}${(parseFloat(item.price) * parseInt(item.quantity)).toFixed(2)}</span>
                                                </div>
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                            </div>
                        `;

                        // Add event listeners for buttons
                        li.querySelector('.retrieve-sale-btn').addEventListener('click', function() {
                            const saleId = this.dataset.saleId;
                            retrieveHeldSale(saleId);
                        });

                        li.querySelector('.delete-sale-btn').addEventListener('click', function() {
                            const saleId = this.dataset.saleId;
                            removeHeldSale(saleId);
                        });

                        // Add event listener for toggle button
                        li.querySelector('.toggle-items-btn').addEventListener('click', function() {
                            const itemsContainer = this.closest('.held-sale-item').querySelector('.held-sale-items');
                            const icon = this.querySelector('i');

                            if (itemsContainer.classList.contains('show')) {
                                itemsContainer.classList.remove('show');
                                icon.classList.remove('fa-chevron-up');
                                icon.classList.add('fa-chevron-down');
                            } else {
                                itemsContainer.classList.add('show');
                                icon.classList.remove('fa-chevron-down');
                                icon.classList.add('fa-chevron-up');
                            }
                        });

                        heldSalesList.appendChild(li);
                    });
                } else {
                    heldSalesList.innerHTML = '<li class="list-group-item text-center">No held sales found</li>';
                }
            } catch (e) {
                console.error('Error displaying held sales:', e);
                heldSalesList.innerHTML = '<li class="list-group-item text-center">Error loading held sales</li>';
            }
        })
        .catch((error) => {
            console.error('Error fetching held sales:', error);
            const heldSalesList = document.getElementById('heldSalesList');
            heldSalesList.innerHTML = '<li class="list-group-item text-center">Error loading held sales</li>';
        });
    }

    window.getCartItemsFromGlobal = function() {
        const cartList = document.getElementById('cartList');
        return [...cartList.children].map(item => {
            if (item.classList.contains('list-group-item')) {
                const titleElement = item.querySelector('.item-title');
                const title = titleElement ? titleElement.textContent.trim() : '';
                const priceElement = item.querySelector('.price');
                const imageElement = item.querySelector('.cart-thumbnail');
                const quantityElement = item.querySelector('.quantity');
                return {
                    variant_id: item.dataset.id,
                    quantity: parseInt(quantityElement ? quantityElement.value : '0'),
                    title: title,
                    price: priceElement ? parseFloat(priceElement.textContent) : 0,
                    image: imageElement ? imageElement.src : null
                };
            }
            return null;
        }).filter(item => item !== null && !isNaN(item.quantity) && item.quantity > 0);
    }

    function getCartItems() {
        // Check if the global function exists
        if (typeof window.getCartItemsFromGlobal === 'function') {
            console.log('Using global getCartItemsFromGlobal function');
            return window.getCartItemsFromGlobal();
        }

        // Fallback implementation if the global function doesn't exist
        console.log('Using fallback getCartItems implementation');
        const cartList = document.getElementById('cartList');
        if (!cartList) return [];

        return [...cartList.children].map(item => {
            if (item.classList.contains('list-group-item')) {
                const titleElement = item.querySelector('.item-title');
                const title = titleElement ? titleElement.textContent.trim() : '';
                const priceElement = item.querySelector('.price');
                const imageElement = item.querySelector('.cart-thumbnail');
                return {
                    variant_id: item.dataset.id,
                    quantity: parseInt(item.querySelector('.quantity')?.value || '0'),
                    title: title,
                    price: priceElement ? parseFloat(priceElement.textContent) : 0,
                    image: imageElement ? imageElement.src : null
                };
            }
            return null;
        }).filter(item => item !== null && !isNaN(item.quantity) && item.quantity > 0);
    }

    document.getElementById('layawayBtn').addEventListener('click', function() {
        layawayModal.style.display = 'block';
        const total = parseFloat(document.getElementById('total').textContent);
        const tillId = document.getElementById('tillId').value;
        const customerId = document.getElementById('customerId').value;

        if (!customerId) {
            alert('Please select a customer before creating a layaway.');
            layawayModal.style.display = 'none';
            return;
        }

        fetch('/pos/initiate_layaway', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                till_id: tillId,
                customer_id: customerId,
                items: getCartItems(),
                total: total
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('layawayDeposit').value = data.default_deposit;
            } else {
                alert(data.message);
                layawayModal.style.display = 'none';
            }
        })
        .catch(error => console.error('Error:', error));
    });

    document.getElementById('createLayawayBtn').addEventListener('click', function() {
        const tillId = document.getElementById('tillId').value;
        const customerId = document.getElementById('customerId').value;
        const deposit = parseFloat(document.getElementById('layawayDeposit').value);
        const total = parseFloat(document.getElementById('total').textContent);

        // For layaways, we need to adjust inventory
        // First, adjust inventory for all items in the cart
        const cartList = document.getElementById('cartList');
        const cartItems = [...cartList.children].filter(item => item.classList.contains('list-group-item') && item.dataset.id);
        cartItems.forEach(item => {
            // Try to extract product/variant info from DOM
            const title = item.querySelector('.cart-item-details')?.textContent?.split(' - ')[0]?.trim() || '';
            const price = parseFloat(item.querySelector('.price')?.textContent) || 0;
            const quantity = parseInt(item.querySelector('.quantity')?.value) || 1;
            const variantId = item.dataset.id;
            // Compose minimal product/variant objects for adjustShopifyInventory
            const product = { title: title, is_custom: variantId.startsWith('custom_') };
            const variant = { id: variantId, title: title, price: price };
            // Force inventory adjustment for layaways
            adjustShopifyInventory(product, variant, quantity, 'add', true);
        });

        fetch('/pos/create_layaway', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                till_id: tillId,
                customer_id: customerId,
                items: getCartItems(),
                total: total,
                deposit: deposit
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                layawayModal.style.display = 'none';
                clearCart();
                updateTotals();
            } else {
                alert(data.message);
            }
        })
        .catch(error => console.error('Error:', error));
    });

    document.getElementById('holdSaleBtn').addEventListener('click', function() {
        customerNameModal.style.display = 'block';
    });

    document.getElementById('saveCustomerNameBtn').addEventListener('click', function() {
        const customerName = document.getElementById('customerNameInput').value.trim();
        if (!customerName) {
            alert('Please enter the customer name.');
            return;
        }
        customerNameModal.style.display = 'none';

        const tillId = document.getElementById('tillId').value;
        const customerId = document.getElementById('customerId').value;
        const employeeName = document.getElementById('employeeName').value;
        const items = getCartItems();

        if (items.length === 0) {
            alert('Cannot hold an empty sale. Please add items to the cart.');
            return;
        }

        // For held sales, we need to adjust inventory
        // First, adjust inventory for all items in the cart
        const cartList = document.getElementById('cartList');
        const cartItems = [...cartList.children].filter(item => item.classList.contains('list-group-item') && item.dataset.id);
        cartItems.forEach(item => {
            // Try to extract product/variant info from DOM
            const title = item.querySelector('.cart-item-details')?.textContent?.split(' - ')[0]?.trim() || '';
            const price = parseFloat(item.querySelector('.price')?.textContent) || 0;
            const quantity = parseInt(item.querySelector('.quantity')?.value) || 1;
            const variantId = item.dataset.id;
            // Compose minimal product/variant objects for adjustShopifyInventory
            const product = { title: title, is_custom: variantId.startsWith('custom_') };
            const variant = { id: variantId, title: title, price: price };
            // Force inventory adjustment for held sales
            adjustShopifyInventory(product, variant, quantity, 'add', true);
        });

        fetch('/pos/hold_sale', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                till_id: tillId,
                customer_id: customerId,
                customer_name: customerName,
                employee_name: employeeName,
                items: items
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Sale held successfully.');
                clearCart();
                updateTotals();
            } else {
                alert(data.message);
            }
        })
        .catch(error => console.error('Error:', error));
    });

    // Add event listener for Retrieve Sales button
    const retrieveSalesBtn = document.getElementById('retrieveSalesBtn');
    if (retrieveSalesBtn) {
        retrieveSalesBtn.addEventListener('click', function() {
            heldSalesModal.style.display = 'block';
            fetchHeldSales();
        });
    }

    // View Layaways functionality
    const viewLayawaysModal = document.getElementById('viewLayawaysModal');
    const layawaysList = document.getElementById('layawaysList');
    const layawaySearchInput = document.getElementById('layawaySearchInput');
    const searchLayawayBtn = document.getElementById('searchLayawayBtn');

    // Add event listener for the View Layaways button
    const viewLayawaysBtn = document.getElementById('viewLayawaysBtn');
    if (viewLayawaysBtn) {
        viewLayawaysBtn.addEventListener('click', function() {
            viewLayawaysModal.style.display = 'block';
            fetchLayaways();
        });
    }

    // Add event listener for the layaway search button
    if (searchLayawayBtn) {
        searchLayawayBtn.addEventListener('click', function() {
            const searchTerm = layawaySearchInput.value.trim();
            if (searchTerm) {
                searchLayaways(searchTerm);
            } else {
                fetchLayaways(); // If search is empty, show all layaways
            }
        });
    }

    // Add event listener for Enter key in the layaway search input
    if (layawaySearchInput) {
        layawaySearchInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                if (searchLayawayBtn) {
                    searchLayawayBtn.click();
                }
            }
        });
    }

    // Function to fetch all layaways or for the current customer
    function fetchLayaways() {
        // Show loading state
        layawaysList.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading layaways...</p>
            </div>
        `;

        // Get customer ID if a customer is selected
        const customerId = document.getElementById('customerId').value;

        // Build the URL with optional customer filter
        let url = '/pos/get_layaways';
        if (customerId) {
            url += `?customer_id=${customerId}`;
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayLayaways(data.layaways);
                } else {
                    layawaysList.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${data.message || 'Error loading layaways'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error fetching layaways:', error);
                layawaysList.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Failed to load layaways. Please try again.
                    </div>
                `;
            });
    }

    // Function to search layaways by ID
    function searchLayaways(searchTerm) {
        // Show loading state
        layawaysList.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Searching layaways...</p>
            </div>
        `;

        fetch(`/pos/search_layaways?search=${encodeURIComponent(searchTerm)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayLayaways(data.layaways);
                } else {
                    layawaysList.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${data.message || 'No layaways found matching your search'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error searching layaways:', error);
                layawaysList.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Failed to search layaways. Please try again.
                    </div>
                `;
            });
    }

    // Function to display layaways in the modal
    function displayLayaways(layaways) {
        if (!layaways || layaways.length === 0) {
            layawaysList.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No layaways found.
                </div>
            `;
            return;
        }

        // Sort layaways by created_at date in descending order (newest first)
        const sortedLayaways = [...layaways].sort((a, b) => {
            const dateA = new Date(a.created_at);
            const dateB = new Date(b.created_at);
            return dateB - dateA; // Descending order (newest first)
        });

        // Clear the list
        layawaysList.innerHTML = '';

        // Add each layaway to the list
        sortedLayaways.forEach(layaway => {
            const li = document.createElement('li');
            li.className = 'list-group-item layaway-item';

            // Calculate remaining balance
            const total = parseFloat(layaway.total) || 0;
            const depositPaid = parseFloat(layaway.deposit_paid) || 0;
            const remainingBalance = total - depositPaid;

            // Format dates
            const createdDate = new Date(layaway.created_at).toLocaleDateString();
            const dueDate = layaway.due_date ? new Date(layaway.due_date).toLocaleDateString() : 'Not set';

            // Create the layaway card
            li.innerHTML = `
                <div class="card layaway-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">Layaway #${layaway.id}</h6>
                            <small class="text-muted">Created: ${createdDate}</small>
                        </div>
                        <span class="badge ${remainingBalance > 0 ? 'bg-warning' : 'bg-success'}">
                            ${remainingBalance > 0 ? 'Active' : 'Paid'}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Customer:</strong> ${layaway.customer_name || 'Unknown'}</p>
                                <p class="mb-1"><strong>Items:</strong> ${layaway.items_count || 0}</p>
                                <p class="mb-0"><strong>Due Date:</strong> ${dueDate}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Total:</strong> ${currencySymbol}${total.toFixed(2)}</p>
                                <p class="mb-1"><strong>Deposit Paid:</strong> ${currencySymbol}${depositPaid.toFixed(2)}</p>
                                <p class="mb-0"><strong>Remaining:</strong> ${currencySymbol}${remainingBalance.toFixed(2)}</p>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-sm btn-outline-primary view-layaway-items-btn" data-layaway-id="${layaway.id}">
                                <i class="fas fa-eye me-1"></i> View Items
                            </button>
                            ${remainingBalance > 0 ? `
                                <button class="btn btn-sm btn-success process-payment-btn" data-layaway-id="${layaway.id}" data-remaining="${remainingBalance.toFixed(2)}">
                                    <i class="fas fa-money-bill-wave me-1"></i> Process Payment
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            // Add event listeners for the buttons
            const viewItemsBtn = li.querySelector('.view-layaway-items-btn');
            if (viewItemsBtn) {
                viewItemsBtn.addEventListener('click', function() {
                    const layawayId = this.dataset.layawayId;
                    viewLayawayItems(layawayId);
                });
            }

            const processPaymentBtn = li.querySelector('.process-payment-btn');
            if (processPaymentBtn) {
                processPaymentBtn.addEventListener('click', function() {
                    const layawayId = this.dataset.layawayId;
                    const remainingBalance = parseFloat(this.dataset.remaining);
                    processLayawayPayment(layawayId, remainingBalance);
                });
            }

            layawaysList.appendChild(li);
        });
    }

    // Function to view layaway items
    function viewLayawayItems(layawayId) {
        // Show loading state in the current layaway card
        const layawayCard = document.querySelector(`.layaway-item [data-layaway-id="${layawayId}"]`).closest('.layaway-card');
        const cardBody = layawayCard.querySelector('.card-body');
        const originalContent = cardBody.innerHTML;

        cardBody.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Loading items...</p>
            </div>
        `;

        fetch(`/pos/get_layaway_items/${layawayId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.items) {
                    // Create items list
                    let itemsHtml = `
                        <div class="layaway-items-container">
                            <h6 class="mb-3">Layaway Items</h6>
                            <ul class="list-group list-group-flush mb-3">
                    `;

                    data.items.forEach(item => {
                        const itemTotal = parseFloat(item.price) * parseInt(item.quantity);
                        itemsHtml += `
                            <li class="list-group-item py-2 px-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span>${item.title}</span>
                                        <span class="text-muted ms-2">x${item.quantity}</span>
                                    </div>
                                    <span>${currencySymbol}${itemTotal.toFixed(2)}</span>
                                </div>
                            </li>
                        `;
                    });

                    itemsHtml += `
                            </ul>
                            <button class="btn btn-sm btn-secondary back-to-layaway-btn">
                                <i class="fas fa-arrow-left me-1"></i> Back
                            </button>
                        </div>
                    `;

                    // Update the card body with items
                    cardBody.innerHTML = itemsHtml;

                    // Add event listener for back button
                    cardBody.querySelector('.back-to-layaway-btn').addEventListener('click', function() {
                        cardBody.innerHTML = originalContent;

                        // Re-add event listeners to the original content buttons
                        const viewItemsBtn = cardBody.querySelector('.view-layaway-items-btn');
                        if (viewItemsBtn) {
                            viewItemsBtn.addEventListener('click', function() {
                                const layawayId = this.dataset.layawayId;
                                viewLayawayItems(layawayId);
                            });
                        }

                        const processPaymentBtn = cardBody.querySelector('.process-payment-btn');
                        if (processPaymentBtn) {
                            processPaymentBtn.addEventListener('click', function() {
                                const layawayId = this.dataset.layawayId;
                                const remainingBalance = parseFloat(this.dataset.remaining);
                                processLayawayPayment(layawayId, remainingBalance);
                            });
                        }
                    });
                } else {
                    // Show error message
                    cardBody.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${data.message || 'Error loading layaway items'}
                        </div>
                        <button class="btn btn-sm btn-secondary back-to-layaway-btn">
                            <i class="fas fa-arrow-left me-1"></i> Back
                        </button>
                    `;

                    // Add event listener for back button
                    cardBody.querySelector('.back-to-layaway-btn').addEventListener('click', function() {
                        cardBody.innerHTML = originalContent;

                        // Re-add event listeners to the original content buttons
                        const viewItemsBtn = cardBody.querySelector('.view-layaway-items-btn');
                        if (viewItemsBtn) {
                            viewItemsBtn.addEventListener('click', function() {
                                const layawayId = this.dataset.layawayId;
                                viewLayawayItems(layawayId);
                            });
                        }

                        const processPaymentBtn = cardBody.querySelector('.process-payment-btn');
                        if (processPaymentBtn) {
                            processPaymentBtn.addEventListener('click', function() {
                                const layawayId = this.dataset.layawayId;
                                const remainingBalance = parseFloat(this.dataset.remaining);
                                processLayawayPayment(layawayId, remainingBalance);
                            });
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching layaway items:', error);

                // Show error message
                cardBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Failed to load layaway items. Please try again.
                    </div>
                    <button class="btn btn-sm btn-secondary back-to-layaway-btn">
                        <i class="fas fa-arrow-left me-1"></i> Back
                    </button>
                `;

                // Add event listener for back button
                cardBody.querySelector('.back-to-layaway-btn').addEventListener('click', function() {
                    cardBody.innerHTML = originalContent;

                    // Re-add event listeners to the original content buttons
                    const viewItemsBtn = cardBody.querySelector('.view-layaway-items-btn');
                    if (viewItemsBtn) {
                        viewItemsBtn.addEventListener('click', function() {
                            const layawayId = this.dataset.layawayId;
                            viewLayawayItems(layawayId);
                        });
                    }

                    const processPaymentBtn = cardBody.querySelector('.process-payment-btn');
                    if (processPaymentBtn) {
                        processPaymentBtn.addEventListener('click', function() {
                            const layawayId = this.dataset.layawayId;
                            const remainingBalance = parseFloat(this.dataset.remaining);
                            processLayawayPayment(layawayId, remainingBalance);
                        });
                    }
                });
            });
    }

    // Function to process layaway payment
    function processLayawayPayment(layawayId, remainingBalance) {
        // Show payment form in the current layaway card
        const layawayCard = document.querySelector(`.layaway-item [data-layaway-id="${layawayId}"]`).closest('.layaway-card');
        const cardBody = layawayCard.querySelector('.card-body');
        const originalContent = cardBody.innerHTML;

        // Create payment form
        cardBody.innerHTML = `
            <div class="layaway-payment-form">
                <h6 class="mb-3">Process Payment</h6>
                <div class="mb-3">
                    <label for="layaway-payment-amount" class="form-label">Payment Amount:</label>
                    <div class="input-group">
                        <span class="input-group-text">${currencySymbol}</span>
                        <input type="number" class="form-control" id="layaway-payment-amount" value="${remainingBalance.toFixed(2)}" min="0.01" max="${remainingBalance.toFixed(2)}" step="0.01">
                    </div>
                    <div class="form-text">Remaining balance: ${currencySymbol}${remainingBalance.toFixed(2)}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Payment Method:</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-success layaway-cash-btn">
                            <i class="fas fa-money-bill-wave me-1"></i> Cash
                        </button>
                        <button class="btn btn-primary layaway-card-btn">
                            <i class="fas fa-credit-card me-1"></i> Card
                        </button>
                    </div>
                </div>
                <button class="btn btn-sm btn-secondary layaway-cancel-btn mt-3">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
            </div>
        `;

        // Add event listeners for payment buttons
        cardBody.querySelector('.layaway-cash-btn').addEventListener('click', function() {
            const paymentAmount = parseFloat(cardBody.querySelector('#layaway-payment-amount').value);
            if (isNaN(paymentAmount) || paymentAmount <= 0 || paymentAmount > remainingBalance) {
                alert('Please enter a valid payment amount.');
                return;
            }

            submitLayawayPayment(layawayId, paymentAmount, 'cash', cardBody, originalContent);
        });

        cardBody.querySelector('.layaway-card-btn').addEventListener('click', function() {
            const paymentAmount = parseFloat(cardBody.querySelector('#layaway-payment-amount').value);
            if (isNaN(paymentAmount) || paymentAmount <= 0 || paymentAmount > remainingBalance) {
                alert('Please enter a valid payment amount.');
                return;
            }

            submitLayawayPayment(layawayId, paymentAmount, 'card', cardBody, originalContent);
        });

        // Add event listener for cancel button
        cardBody.querySelector('.layaway-cancel-btn').addEventListener('click', function() {
            cardBody.innerHTML = originalContent;

            // Re-add event listeners to the original content buttons
            const viewItemsBtn = cardBody.querySelector('.view-layaway-items-btn');
            if (viewItemsBtn) {
                viewItemsBtn.addEventListener('click', function() {
                    const layawayId = this.dataset.layawayId;
                    viewLayawayItems(layawayId);
                });
            }

            const processPaymentBtn = cardBody.querySelector('.process-payment-btn');
            if (processPaymentBtn) {
                processPaymentBtn.addEventListener('click', function() {
                    const layawayId = this.dataset.layawayId;
                    const remainingBalance = parseFloat(this.dataset.remaining);
                    processLayawayPayment(layawayId, remainingBalance);
                });
            }
        });
    }

    // Function to submit layaway payment
    function submitLayawayPayment(layawayId, amount, paymentMethod, cardBody, originalContent) {
        // Show loading state
        cardBody.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Processing payment...</p>
            </div>
        `;

        fetch('/pos/process_layaway_payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                layaway_id: layawayId,
                amount: amount,
                payment_method: paymentMethod
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                cardBody.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        ${data.message || 'Payment processed successfully!'}
                    </div>
                    <button class="btn btn-primary refresh-layaways-btn">
                        <i class="fas fa-sync me-1"></i> Refresh Layaways
                    </button>
                `;

                // Add event listener for refresh button
                cardBody.querySelector('.refresh-layaways-btn').addEventListener('click', function() {
                    fetchLayaways(); // Refresh the layaways list
                });
            } else {
                // Show error message
                cardBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${data.message || 'Error processing payment'}
                    </div>
                    <button class="btn btn-sm btn-secondary back-to-layaway-btn">
                        <i class="fas fa-arrow-left me-1"></i> Back
                    </button>
                `;

                // Add event listener for back button
                cardBody.querySelector('.back-to-layaway-btn').addEventListener('click', function() {
                    cardBody.innerHTML = originalContent;

                    // Re-add event listeners to the original content buttons
                    const viewItemsBtn = cardBody.querySelector('.view-layaway-items-btn');
                    if (viewItemsBtn) {
                        viewItemsBtn.addEventListener('click', function() {
                            const layawayId = this.dataset.layawayId;
                            viewLayawayItems(layawayId);
                        });
                    }

                    const processPaymentBtn = cardBody.querySelector('.process-payment-btn');
                    if (processPaymentBtn) {
                        processPaymentBtn.addEventListener('click', function() {
                            const layawayId = this.dataset.layawayId;
                            const remainingBalance = parseFloat(this.dataset.remaining);
                            processLayawayPayment(layawayId, remainingBalance);
                        });
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error processing layaway payment:', error);

            // Show error message
            cardBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Failed to process payment. Please try again.
                </div>
                <button class="btn btn-sm btn-secondary back-to-layaway-btn">
                    <i class="fas fa-arrow-left me-1"></i> Back
                </button>
            `;

            // Add event listener for back button
            cardBody.querySelector('.back-to-layaway-btn').addEventListener('click', function() {
                cardBody.innerHTML = originalContent;

                // Re-add event listeners to the original content buttons
                const viewItemsBtn = cardBody.querySelector('.view-layaway-items-btn');
                if (viewItemsBtn) {
                    viewItemsBtn.addEventListener('click', function() {
                        const layawayId = this.dataset.layawayId;
                        viewLayawayItems(layawayId);
                    });
                }

                const processPaymentBtn = cardBody.querySelector('.process-payment-btn');
                if (processPaymentBtn) {
                    processPaymentBtn.addEventListener('click', function() {
                        const layawayId = this.dataset.layawayId;
                        const remainingBalance = parseFloat(this.dataset.remaining);
                        processLayawayPayment(layawayId, remainingBalance);
                    });
                }
            });
        });
    }

    // Add event listener for the remove customer button
    document.getElementById('removeCustomerBtn').addEventListener('click', function() {
        // Hide the customer details section
        document.getElementById('customerDetails').style.display = 'none';

        // Reset customer-related fields
        document.getElementById('customerName').innerHTML = '<span class="detail-label">Name:</span> <span class="detail-value"></span>';
        document.getElementById('customerEmail').innerHTML = '<span class="detail-label">Email:</span> <span class="detail-value"></span>';
        document.getElementById('giftCardBalance').innerHTML = '<span class="detail-label">Gift Card Balance:</span> <span class="detail-value"><span class="currency-symbol">' + currencySymbol + '</span><span class="balance-amount">0.00</span></span>';
        document.getElementById('storeCreditBalance').innerHTML = '<span class="detail-label">Store Credit Balance:</span> <span class="detail-value"><span class="currency-symbol">' + currencySymbol + '</span><span class="balance-amount">0.00</span></span>';
        document.getElementById('totalCreditBalance').innerHTML = '<span class="detail-label">Total Credit Balance:</span> <span class="detail-value"><span class="currency-symbol">' + currencySymbol + '</span><span class="balance-amount">0.00</span></span>';

        // Clear customer ID and credit-related values
        document.getElementById('customerId').value = '';
        document.getElementById('giftCardId').value = '';
        document.getElementById('giftCardAmount').value = '';
        document.getElementById('storeCreditAmount').value = '';
        document.getElementById('storeCreditAccountId').value = '';

        // Reset applied credit amounts
        document.getElementById('giftCardApplied').textContent = '0.00';
        document.getElementById('storeCreditApplied').textContent = '0.00';

        // Hide credit section
        document.getElementById('creditSection').style.display = 'none';

        // Remove any credit messages
        const creditMessages = document.querySelectorAll('.gift-card-message, .store-credit-message');
        creditMessages.forEach(msg => msg.remove());

        // Update totals to reflect changes
        updateTotals();
    });

    function retrieveHeldSale(saleId) {
        fetch(`/pos/retrieve_held_sale/${saleId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                clearCart(); // Clear the cart before adding retrieved items
                data.held_sale.items.forEach(item => {
                    const product = {
                        title: item.title,
                        is_custom: item.variant_id && item.variant_id.toString().startsWith('custom_'),
                        from_held_sale: true, // Mark items as from held sale to prevent double inventory adjustment
                        variants: [{
                            id: item.variant_id,
                            title: item.title,
                            price: item.price
                        }]
                    };
                    const variant = product.variants[0];
                    // Pass from_held_sale flag to prevent inventory adjustment
                    addToCart(product, variant, item.quantity, true, true); // true for saveToStorage, true for fromHeldSale
                });

                // Use window.updateTotals if available, otherwise dispatch a custom event
                if (typeof window.updateTotals === 'function') {
                    window.updateTotals();
                } else {
                    console.log('window.updateTotals not available in retrieveHeldSale, dispatching cart:update event');
                    // Dispatch a custom event that will be caught by the event listener
                    document.dispatchEvent(new CustomEvent('cart:update'));
                }

                document.getElementById('heldSaleId').value = saleId;
                heldSalesModal.style.display = 'none'; // Close the modal

                // Show a success message
                const successMsg = document.createElement('div');
                successMsg.className = 'alert alert-success fixed-top w-50 mx-auto mt-2 text-center';
                successMsg.innerHTML = `<strong>Success!</strong> Held sale retrieved successfully.`;
                document.body.appendChild(successMsg);

                // Remove the message after 2 seconds
                setTimeout(() => {
                    successMsg.remove();
                }, 2000);
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while retrieving the sale.');
        });
    }

    function clearCart(saveToStorage = true) {
        const cartList = document.getElementById('cartList');
        cartList.innerHTML = '<li class="list-group-item empty-cart-message" style="background: transparent; border: 1px dashed rgba(255, 255, 255, 0.2); color: rgba(248, 250, 252, 0.7); text-align: center; padding: 1.5rem; border-radius: 0.5rem; margin: 0.5rem 0;">No items in cart</li>';

        // Use window.updateTotals if available, otherwise dispatch a custom event
        if (typeof window.updateTotals === 'function') {
            window.updateTotals();
        } else {
            console.log('window.updateTotals not available in clearCart, dispatching cart:update event');
            // Dispatch a custom event that will be caught by the event listener in initPOS
            document.dispatchEvent(new CustomEvent('cart:update'));
        }

        if (saveToStorage) {
            // Clear local storage instead of saving an empty cart
            localStorage.removeItem('posCartItems');
        }
    }

    function resetCustomerDetails() {
        document.getElementById('customerDetails').style.display = 'none';
        document.getElementById('customerName').innerText = 'Name: ';
        document.getElementById('customerEmail').innerText = 'Email: ';
        document.getElementById('giftCardBalance').innerText = 'Gift Card Balance: $';
        document.getElementById('customerId').value = '';
        document.getElementById('giftCardId').value = '';
        document.getElementById('giftCardAmount').value = '';
        document.getElementById('giftCardApplied').textContent = '0.00';
    }

    // Function to load saved searches
    function loadSavedSearches() {
        fetch('/pos/get_saved_category_searches')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySavedSearches(data.saved_searches);
                } else {
                    console.error('Error loading saved searches:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading saved searches:', error);
            });
    }

    // Function to display saved searches in the sidebar
    function displaySavedSearches(savedSearches) {
        const savedSearchCardsContainer = document.getElementById('savedSearchCards');
        const savedSearchesSection = document.getElementById('savedSearchesSection');

        // Clear existing saved searches
        savedSearchCardsContainer.innerHTML = '';

        if (savedSearches.length === 0) {
            // Hide the section if there are no saved searches
            savedSearchesSection.style.display = 'none';
            return;
        }

        // Show the section if there are saved searches
        savedSearchesSection.style.display = 'block';

        // Filter out duplicate searches
        const uniqueSearches = [];
        const searchKeys = new Set();

        savedSearches.forEach(search => {
            const key = `${search.vendor}|${search.product_type}`;
            if (!searchKeys.has(key)) {
                searchKeys.add(key);
                uniqueSearches.push(search);
            }
        });

        uniqueSearches.forEach(search => {
            // Create a container for each saved search
            const searchItem = document.createElement('div');
            searchItem.className = 'col-6 mb-2 saved-search-container';

            searchItem.innerHTML = `
                <div class="product-type-card" data-search-id="${search.id}">
                    <div class="remove-saved-search-btn" title="Remove Saved Search">
                         <i class="fas fa-times"></i>
                     </div>
                     <div class="card-body">
                         <p class="quicklink-product-name">${search.display_name}</p>
                         <div class="mt-auto">
                              <button class="btn btn-secondary w-100 load-saved-search-btn" data-search-id="${search.id}" data-vendor="${search.vendor}" data-product-type="${search.product_type}" data-in-stock-only="${search.in_stock_only}">
                                 ${search.display_name}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Add event listener for the load button
            const loadBtn = searchItem.querySelector('.load-saved-search-btn');
            loadBtn.addEventListener('click', function() {
                const vendor = this.dataset.vendor;
                const productType = this.dataset.productType;
                const inStockOnly = this.dataset.inStockOnly === 'true';

                // Set the form values
                vendorSelect.value = vendor;

                // Manually trigger the vendor change event to load product types
                const vendorChangeEvent = new Event('change');
                vendorSelect.dispatchEvent(vendorChangeEvent);

                // Then load product types with our callback
                loadProductTypes(vendor, function() {
                    productTypeSelect.value = productType;
                    productTypeSelect.disabled = false;

                    // Manually trigger the product type change event
                    const productTypeChangeEvent = new Event('change');
                    productTypeSelect.dispatchEvent(productTypeChangeEvent);

                    categoryInStockOnly.checked = inStockOnly;
                    loadCategoryProducts(vendor, productType);
                    document.getElementById('saveSearchContainer').style.display = 'block';

                    // Expand the category filter section if it's collapsed
                    const collapseVendorFilter = document.getElementById('collapseVendorFilter');
                    if (collapseVendorFilter && !collapseVendorFilter.classList.contains('show')) {
                        collapseVendorFilter.classList.add('show');
                    }
                });
            });

            // Add event listener for the remove button
            const removeBtn = searchItem.querySelector('.remove-saved-search-btn');
            removeBtn.addEventListener('click', function(event) {
                event.stopPropagation();
                const card = this.closest('.product-type-card');
                const searchId = card.dataset.searchId;

                if (confirm('Are you sure you want to remove this saved search?')) {
                    fetch(`/pos/remove_saved_category_search/${searchId}`, {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            searchItem.remove();
                            // Check if there are any saved searches left
                            if (savedSearchCardsContainer.children.length === 0) {
                                savedSearchCardsContainer.innerHTML = '<div class="col-12 text-center text-muted">No saved searches</div>';
                            }
                        } else {
                            alert('Error removing saved search: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error removing saved search:', error);
                        alert('An error occurred while removing the saved search.');
                    });
                }
            });

            // Add the saved search to the container
            savedSearchCardsContainer.appendChild(searchItem);
        });
    }

    // Modified loadProductTypes function to accept a callback
    function loadProductTypes(vendor, callback) {
        fetch(`/pos/get_product_types?vendor=${encodeURIComponent(vendor)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    productTypeSelect.innerHTML = '<option value="">Select a product type</option>';
                    data.product_types.forEach(productType => {
                        const option = document.createElement('option');
                        option.value = productType;
                        option.textContent = productType;
                        productTypeSelect.appendChild(option);
                    });

                    // Call the callback if provided
                    if (typeof callback === 'function') {
                        callback();
                    }
                } else {
                    console.error('Error loading product types:', data.message);
                    productTypeSelect.innerHTML = '<option value="">Error loading product types</option>';
                }
            })
            .catch(error => {
                console.error('Error loading product types:', error);
                productTypeSelect.innerHTML = '<option value="">Error loading product types</option>';
            });
    }

    // Function to set cart width
    function setCartWidth(widthPercent, cartWidthButtons, productColumn, cartColumn) {
        if (!productColumn || !cartColumn) {
            console.error('Product or cart column not found');
            return;
        }

        // Convert percentage to Bootstrap's 12-column grid system
        const cartColumns = Math.round((widthPercent / 100) * 12);
        const productColumns = 12 - cartColumns;

        console.log(`Setting cart width: ${widthPercent}% (${cartColumns} columns)`);

        // Update column classes - remove all col-* classes first
        productColumn.className = productColumn.className.replace(/col-\w+-\d+/g, '');
        cartColumn.className = cartColumn.className.replace(/col-\w+-\d+/g, '');

        // Add the new column classes
        productColumn.classList.add(`col-md-${productColumns}`);
        cartColumn.classList.add(`col-md-${cartColumns}`);

        // Save preference to localStorage
        localStorage.setItem('cartWidthPreference', widthPercent);

        // Update active button state
        cartWidthButtons.forEach(btn => {
            if (parseInt(btn.dataset.width) === widthPercent) {
                btn.classList.remove('btn-outline-secondary');
                btn.classList.add('btn-secondary');
            } else {
                btn.classList.remove('btn-secondary');
                btn.classList.add('btn-outline-secondary');
            }
        });
    }

    // Initialize the POS system
    // Function to save cart items to localStorage
    function saveCartToLocalStorage() {
        const cartItems = getCartItems();
        localStorage.setItem('posCartItems', JSON.stringify(cartItems));
        console.log('Cart saved to localStorage:', cartItems);
    }

// Function to load cart items from localStorage
function loadCartFromLocalStorage() {
    const savedCart = localStorage.getItem('posCartItems');
    if (savedCart) {
        try {
            const cartItems = JSON.parse(savedCart);
            if (cartItems && cartItems.length > 0) {
                console.log('Loading cart from localStorage:', cartItems);

                // Clear any existing "No items in cart" message
                const cartList = document.getElementById('cartList');
                if (cartList.children.length === 1 && cartList.children[0].textContent === 'No items in cart') {
                    cartList.innerHTML = '';
                }

                // Use the tax init helper to ensure tax functions are available
                ensureTaxHandlerInitialized(() => {
                    // Add each item to the cart
                    cartItems.forEach(item => {
                        const product = {
                            title: item.title,
                            variants: [{
                                id: item.variant_id,
                                title: item.title,
                                price: item.price
                            }]
                        };
                        const variant = product.variants[0];
                        addToCart(product, variant, item.quantity, false); // false = don't save to localStorage again
                    });

                    // Update totals
                    if (typeof window.updateTotals === 'function') {
                        window.updateTotals();
                    } else {
                        console.log('window.updateTotals not available in loadCartFromLocalStorage, dispatching cart:update event');
                        // Dispatch a custom event that will be caught by the event listener
                        document.dispatchEvent(new CustomEvent('cart:update'));
                    }
                });
            }
        } catch (error) {
            console.error('Error loading cart from localStorage:', error);
        }
    }
}

    function initPOS() {
        // Add event listener for cart:update custom event
        document.addEventListener('cart:update', function() {
            console.log('Received cart:update event');
            if (typeof window.updateTotals === 'function') {
                window.updateTotals();
            }
        });

        clearCart(false); // false = don't save to localStorage
        resetCustomerDetails();
        focusSearchInput();
        loadSavedSearches(); // Load saved searches when initializing
        // Disabled loading cart from localStorage to prevent issues with refreshes
        // loadCartFromLocalStorage();

        // Explicitly update totals after loading cart items
        if (typeof window.updateTotals === 'function') {
            window.updateTotals();
        } else {
            console.log('window.updateTotals not available in initPOS, dispatching cart:update event');
            // Dispatch a custom event that will be caught by the event listener
            document.dispatchEvent(new CustomEvent('cart:update'));
        }

    // Initialize cart width adjustment functionality
    const cartWidthButtons = document.querySelectorAll('.cart-width-btn');

    // Wait for DOM to be fully loaded before accessing columns
    setTimeout(() => {
        const productColumn = document.querySelector('.row > .col-md-8');
        const cartColumn = document.querySelector('.row > .col-md-4');

        if (productColumn && cartColumn) {
            // Add event listeners to width buttons
            cartWidthButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const width = parseInt(this.dataset.width);
                    setCartWidth(width, cartWidthButtons, productColumn, cartColumn);
                });
            });

            // Load saved cart width preference
            const savedWidth = localStorage.getItem('cartWidthPreference');
            if (savedWidth) {
                setCartWidth(parseInt(savedWidth), cartWidthButtons, productColumn, cartColumn);
            } else {
                // Default is 35% (4 columns in bootstrap's 12-column grid)
                setCartWidth(35, cartWidthButtons, productColumn, cartColumn);
            }
        } else {
            console.warn('Product or cart column not found. Cart width adjustment disabled.');
        }
    }, 100); // Short delay to ensure DOM is ready
    }

    // Add event listener for the Reset Cart button
    document.getElementById('emptyCartBtn').addEventListener('click', function() {
        if (confirm('Are you sure you want to reset the cart? All items will be put back into inventory.')) {
            // Restore inventory for all items currently in the cart
            const cartList = document.getElementById('cartList');
            const cartItems = [...cartList.children].filter(item => item.classList.contains('list-group-item') && item.dataset.id);
            cartItems.forEach(item => {
                // Try to extract product/variant info from DOM
                const title = item.querySelector('.cart-item-details')?.textContent?.split(' - ')[0]?.trim() || '';
                const price = parseFloat(item.querySelector('.price')?.textContent) || 0;
                const quantity = parseInt(item.querySelector('.quantity')?.value) || 1;
                const variantId = item.dataset.id;
                // Compose minimal product/variant objects for adjustShopifyInventory
                const product = { title: title, is_custom: variantId.startsWith('custom_') };
                const variant = { id: variantId, title: title, price: price };
                adjustShopifyInventory(product, variant, quantity, 'remove');
            });

            // Clear the cart
            clearCart();

            // Clear localStorage
            localStorage.removeItem('posCartItems');

            // Clear any other relevant cache
            localStorage.removeItem('lastSearchQuery');

            // Show success message
            const successMsg = document.createElement('div');
            successMsg.className = 'alert alert-success fixed-top w-50 mx-auto mt-2 text-center';
            successMsg.innerHTML = `<strong>Success!</strong> Cart reset and inventory restored.`;
            document.body.appendChild(successMsg);

            // Remove the message after 2 seconds
            setTimeout(function() {
                successMsg.remove();
            }, 2000);
        }
    });

    // Instore Orders functionality
    function initInstoreOrders() {
        const instoreOrdersDropdown = document.getElementById('instoreOrdersDropdown');
        const instoreOrdersList = document.getElementById('instoreOrdersList');

        if (instoreOrdersDropdown && instoreOrdersList) {
            // Load orders when dropdown is shown
            instoreOrdersDropdown.addEventListener('click', function() {
                loadInstoreOrders();
            });
        }
    }

    function loadInstoreOrders() {
        const instoreOrdersList = document.getElementById('instoreOrdersList');

        // Show loading state
        instoreOrdersList.innerHTML = '<li><a class="dropdown-item text-muted">Loading orders...</a></li>';

        fetch('/pos/get_terminal_orders')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.orders && data.orders.length > 0) {
                    // Clear loading message
                    instoreOrdersList.innerHTML = '';

                    // Add orders to dropdown
                    data.orders.forEach(order => {
                        const li = document.createElement('li');
                        const a = document.createElement('a');
                        a.className = 'dropdown-item';
                        a.href = '#';
                        a.textContent = `${order.customerName} - $${order.total.toFixed(2)}`;
                        a.dataset.orderId = order._id;

                        // Add click handler to load order items
                        a.addEventListener('click', function(e) {
                            e.preventDefault();
                            loadTerminalOrder(order._id, order.customerName);
                        });

                        li.appendChild(a);
                        instoreOrdersList.appendChild(li);
                    });
                } else {
                    instoreOrdersList.innerHTML = '<li><a class="dropdown-item text-muted">No orders found</a></li>';
                }
            })
            .catch(error => {
                console.error('Error loading instore orders:', error);
                instoreOrdersList.innerHTML = '<li><a class="dropdown-item text-danger">Error loading orders</a></li>';
            });
    }

    function loadTerminalOrder(orderId, customerName) {
        fetch(`/pos/load_terminal_order/${orderId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.order && data.order.items) {
                    // Clear cart first
                    clearCart(false);

                    // Add each item to cart
                    data.order.items.forEach(item => {
                        const product = {
                            title: item.title,
                            from_terminal_order: true, // Flag to indicate this is from a terminal order
                            variants: [{
                                id: item.variant_id,
                                title: item.title,
                                price: item.price
                            }]
                        };
                        const variant = product.variants[0];
                        addToCart(product, variant, item.quantity, true);
                    });

                    // Update totals
                    if (typeof window.updateTotals === 'function') {
                        window.updateTotals();
                    } else {
                        document.dispatchEvent(new CustomEvent('cart:update'));
                    }

                    // Store the terminal order ID for deletion when payment is completed
                    const terminalOrderIdInput = document.getElementById('terminalOrderId') || document.createElement('input');
                    terminalOrderIdInput.type = 'hidden';
                    terminalOrderIdInput.id = 'terminalOrderId';
                    terminalOrderIdInput.value = data.order._id;
                    if (!document.getElementById('terminalOrderId')) {
                        document.body.appendChild(terminalOrderIdInput);
                    }

                    // Show success message
                    const successMsg = document.createElement('div');
                    successMsg.className = 'alert alert-success fixed-top w-50 mx-auto mt-2 text-center';
                    successMsg.innerHTML = `<strong>Success!</strong> Order for ${customerName} loaded to cart.`;
                    document.body.appendChild(successMsg);

                    // Remove the message after 3 seconds
                    setTimeout(() => {
                        successMsg.remove();
                    }, 3000);

                } else {
                    alert(data.message || 'Error loading order');
                }
            })
            .catch(error => {
                console.error('Error loading terminal order:', error);
                alert('An error occurred while loading the order.');
            });
    }

    // Call initPOS when the page loads
    initPOS();

    // Initialize instore orders dropdown
    initInstoreOrders();

    // Add event listeners for credit buttons
    const applyGiftCardBtn = document.getElementById('applyGiftCardBtn');
    if (applyGiftCardBtn) {
        applyGiftCardBtn.addEventListener('click', function() {
            const giftCardAmount = parseFloat(document.getElementById('giftCardAmount').value) || 0;
            const customerId = document.getElementById('customerId').value;
            const total = parseFloat(document.getElementById('total').textContent);

            if (giftCardAmount <= 0) {
                alert('No gift card balance available');
                return;
            }

            if (total <= 0) {
                alert('Please add items to the cart first');
                return;
            }

            if (!customerId) {
                alert('Customer information is missing');
                return;
            }

        // Calculate amount to apply
        const amountToApply = Math.min(giftCardAmount, total);

        // Update the display
        document.getElementById('giftCardApplied').textContent = amountToApply.toFixed(2);

        // Update message
        const giftCardMsg = document.querySelector('.gift-card-message');
        if (giftCardMsg) {
            if (amountToApply >= total) {
                giftCardMsg.textContent = `Full amount will be charged to gift card`;
                giftCardMsg.className = 'alert alert-success mt-2 gift-card-message';
            } else {
                giftCardMsg.textContent = `$${amountToApply.toFixed(2)} from gift card will be applied at checkout`;
                giftCardMsg.className = 'alert alert-success mt-2 gift-card-message';
            }
        }

        // Update payment modal displays
        document.getElementById('giftCardAmountUsed').textContent = amountToApply.toFixed(2);
        document.getElementById('cardGiftCardAmountUsed').textContent = amountToApply.toFixed(2);

        // Flash effect on the gift card section to indicate success
        const giftCardSection = document.getElementById('giftCardSection');
        giftCardSection.style.backgroundColor = '#28a745';
        setTimeout(() => {
            giftCardSection.style.backgroundColor = '#343a40';
        }, 500);
        });
    }

    const applyStoreCreditBtn = document.getElementById('applyStoreCreditBtn');
    if (applyStoreCreditBtn) {
        applyStoreCreditBtn.addEventListener('click', function() {
            const storeCreditAmount = parseFloat(document.getElementById('storeCreditAmount').value) || 0;
            const customerId = document.getElementById('customerId').value;
            const total = parseFloat(document.getElementById('total').textContent);

            if (storeCreditAmount <= 0) {
                alert('No store credit balance available');
                return;
            }

            if (total <= 0) {
                alert('Please add items to the cart first');
                return;
            }

            if (!customerId) {
                alert('Customer information is missing');
                return;
            }

        // Call server to apply store credit
        fetch('/pos/apply_store_credit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                customer_id: customerId,
                amount: Math.min(storeCreditAmount, total)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Store store credit ID and amount for later use in transaction
                let storeCreditAccountIdInput = document.getElementById('storeCreditAccountId');
                if (!storeCreditAccountIdInput) {
                    storeCreditAccountIdInput = document.createElement('input');
                    storeCreditAccountIdInput.type = 'hidden';
                    storeCreditAccountIdInput.id = 'storeCreditAccountId';
                    document.body.appendChild(storeCreditAccountIdInput);
                }
                storeCreditAccountIdInput.value = data.store_credit_account_id;

                // Update display
                document.getElementById('storeCreditApplied').textContent = data.amount.toFixed(2);

                // Add a message to indicate store credit was applied
                const creditMsg = document.createElement('div');
                creditMsg.className = 'alert alert-success mt-2';
                creditMsg.textContent = `$${data.amount.toFixed(2)} from store credit will be applied at checkout`;

                // Remove any existing store credit message
                const existingMsg = document.querySelector('.store-credit-message');
                if (existingMsg) {
                    existingMsg.remove();
                }

                // Add the new message
                creditMsg.classList.add('store-credit-message');
                document.getElementById('customerDetails').appendChild(creditMsg);

                // Flash effect on the store credit section to indicate success
                const storeCreditSection = document.getElementById('storeCreditSection');
                storeCreditSection.style.backgroundColor = '#28a745';
                setTimeout(() => {
                    storeCreditSection.style.backgroundColor = '#343a40';
                }, 500);
            } else {
                alert(data.message || 'Error applying store credit');
            }
        })
        .catch(error => {
            console.error('Error applying store credit:', error);
            alert('An error occurred while applying store credit');
        });
        });
    }

    // Edit Price Modal
    const editPriceModal = document.getElementById('editPriceModal');
    const editItemName = document.getElementById('editItemName');
    const editItemPrice = document.getElementById('editItemPrice');
    const editItemId = document.getElementById('editItemId');
    const saveItemPriceBtn = document.getElementById('saveItemPriceBtn');

    // Add event delegation for edit price buttons
    document.addEventListener('click', function(event) {
        if (event.target.closest('.edit-price-btn')) {
            const cartItem = event.target.closest('.list-group-item');
            const itemId = cartItem.dataset.id;
            const itemDetails = cartItem.querySelector('.cart-item-details').textContent;
            const priceElement = cartItem.querySelector('.price');
            const currentPrice = parseFloat(priceElement.textContent);

            // Set values in the edit price modal
            editItemName.textContent = itemDetails.split(' - $')[0];
            editItemPrice.value = currentPrice.toFixed(2);
            editItemId.value = itemId;

            // Show the modal
            editPriceModal.style.display = 'block';
            editItemPrice.focus();
        }
    });

    // Save price button click event
    if (saveItemPriceBtn) {
        saveItemPriceBtn.addEventListener('click', function() {
            const itemId = editItemId.value;
            const newPrice = parseFloat(editItemPrice.value);

            if (isNaN(newPrice) || newPrice <= 0) {
                alert('Please enter a valid price greater than zero.');
                return;
            }

        // Find the cart item and update its price
        const cartItem = document.querySelector(`.list-group-item[data-id="${itemId}"]`);
        if (cartItem) {
            const priceElement = cartItem.querySelector('.price');
            const quantityElement = cartItem.querySelector('.quantity');
            const itemTotalElement = cartItem.querySelector('.item-total-price');

            if (priceElement) {
                // Update the price display
                priceElement.textContent = newPrice.toFixed(2);

                // Update the item total if quantity and item total elements exist
                if (quantityElement && itemTotalElement) {
                    const quantity = parseInt(quantityElement.value) || 1;
                    const itemTotal = newPrice * quantity;
                    itemTotalElement.textContent = itemTotal.toFixed(2);
                }

                // Update the cart totals
                updateTotals();

                // Save to localStorage
                saveCartToLocalStorage();

                // Add a visual feedback - briefly highlight the price
                const priceDisplay = cartItem.querySelector('.price-display');
                if (priceDisplay) {
                    priceDisplay.style.backgroundColor = 'rgba(46, 204, 113, 0.3)'; // Light green background
                    setTimeout(() => {
                        priceDisplay.style.backgroundColor = 'rgba(0, 0, 0, 0.1)'; // Restore original background
                    }, 1000);
                }

                // Close the modal
                editPriceModal.style.display = 'none';
            }
        }
        });
    }

    // Add event listener for Enter key in the edit price input
    if (editItemPrice) {
        editItemPrice.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                if (saveItemPriceBtn) {
                    saveItemPriceBtn.click();
                }
            }
        });
    }

    // Custom Item Modal
    const addCustomItemBtn = document.getElementById('addCustomItemBtn');
    const saveCustomItemBtn = document.getElementById('saveCustomItemBtn');

    // Receipt Modal is already defined above
    const printReceiptBtnMain = document.getElementById('printReceiptBtn');

    if (addCustomItemBtn) {
        addCustomItemBtn.addEventListener('click', function() {
            customItemModal.style.display = 'block';
        });
    }

    if (saveCustomItemBtn) {
        saveCustomItemBtn.addEventListener('click', function() {
        const itemName = document.getElementById('customItemName').value.trim();
        const itemPrice = parseFloat(document.getElementById('customItemPrice').value);
        const itemQuantity = parseInt(document.getElementById('customItemQuantity').value);

        if (!itemName || isNaN(itemPrice) || isNaN(itemQuantity) || itemQuantity < 1) {
            alert('Please enter valid item details.');
            return;
        }

        const customItem = {
            title: itemName,
            variants: [{
                id: 'custom_' + Date.now(),
                title: itemName,
                price: itemPrice
            }],
            is_custom: true
        };

        addToCart(customItem, customItem.variants[0], itemQuantity);

        // Clear the input fields
        document.getElementById('customItemName').value = '';
        document.getElementById('customItemPrice').value = '';
        document.getElementById('customItemQuantity').value = '1';

        customItemModal.style.display = 'none';
        });
    }

    // Function to generate receipt content
    async function generateReceipt() {
        const items = getCartItems();
        const subtotal = parseFloat(document.getElementById('subtotal').textContent);
        const tax = parseFloat(document.getElementById('tax').textContent);
        const total = parseFloat(document.getElementById('total').textContent);
        const discount = parseFloat(document.getElementById('discount').value) || 0;
        const discountType = document.getElementById('discountType').value;
        const customerName = document.getElementById('customerName').textContent.includes(':') ?
            document.getElementById('customerName').textContent.split(':')[1].trim() : '';
        const customerEmail = document.getElementById('customerEmail').textContent.includes(':') ?
            document.getElementById('customerEmail').textContent.split(':')[1].trim() : '';

        // Fetch business details from the server
        let businessDetails = {
            business_name: 'Business Name',
            address: '',
            city: '',
            state: '',
            postal_code: '',
            country: '',
            phone: '',
            email: '',
            website: '',
            vat_number: '',
            receipt_footer: 'Thank you for your purchase!'
        };

        try {
            // Get the username from the URL if available (for public POS access)
            let username = '';
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('username')) {
                username = urlParams.get('username');
            }

            // Fetch business details with username parameter if available
            const url = username ? `/pos/receipt/business-details?username=${username}` : '/pos/receipt/business-details';
            console.log('Fetching business details from:', url);

            const response = await fetch(url);
            const data = await response.json();
            if (data.success) {
                businessDetails = data.business_details;
                console.log('Received business details:', businessDetails);
            } else {
                console.error('Error in business details response:', data.message);
            }
        } catch (error) {
            console.error('Error fetching business details:', error);
        }

        // Format address for receipt
        const addressLines = [];
        if (businessDetails.address) addressLines.push(businessDetails.address);

        const cityStatePostal = [
            businessDetails.city,
            businessDetails.state,
            businessDetails.postal_code
        ].filter(Boolean).join(', ');

        if (cityStatePostal) addressLines.push(cityStatePostal);
        if (businessDetails.country) addressLines.push(businessDetails.country);

        // Contact information
        const contactLines = [];
        // Remove phone number as requested
        if (businessDetails.email) contactLines.push(`Email: ${businessDetails.email}`);
        if (businessDetails.website) contactLines.push(`Web: ${businessDetails.website}`);
        if (businessDetails.vat_number) contactLines.push(`VAT: ${businessDetails.vat_number}`);

        // Generate a transaction ID (timestamp-based)
        const transactionId = 'TXN-' + Date.now().toString().slice(-8);

        // Format the current date and time
        const now = new Date();
        const dateTimeStr = now.toLocaleDateString() + ' ' + now.toLocaleTimeString();

        // Start building the receipt HTML with improved styling
        let receiptHTML = `
            <div style="text-align: center; margin-bottom: 15px; border-bottom: 1px dashed #ccc; padding-bottom: 10px;">
                <h2 style="margin: 0; font-size: 1.4em;">${businessDetails.business_name}</h2>
                ${addressLines.length > 0 ? `<p style="margin: 5px 0; font-size: 0.9em;">${addressLines.join('<br>')}</p>` : ''}
                ${contactLines.length > 0 ? `<p style="margin: 5px 0; font-size: 0.8em;">${contactLines.join(' | ')}</p>` : ''}
            </div>

            <div style="margin-bottom: 15px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                <table style="width: 100%; font-size: 0.9em;">
                    <tr>
                        <td><strong>Receipt:</strong> ${transactionId}</td>
                        <td style="text-align: right;"><strong>Date:</strong> ${dateTimeStr}</td>
                    </tr>
                    ${customerName ? `<tr><td colspan="2"><strong>Customer:</strong> ${customerName}</td></tr>` : ''}
                    ${customerEmail ? `<tr><td colspan="2"><strong>Email:</strong> ${customerEmail}</td></tr>` : ''}
                </table>
            </div>

            <div style="margin-bottom: 15px;">
                <table style="width: 100%; border-collapse: collapse; font-size: 0.9em;">
                    <tr style="border-bottom: 2px solid #000; font-weight: bold;">
                        <th style="text-align: left; padding: 5px;">Item</th>
                        <th style="text-align: right; padding: 5px;">Qty</th>
                        <th style="text-align: right; padding: 5px;">Price</th>
                        <th style="text-align: right; padding: 5px;">Total</th>
                    </tr>
        `;

        // Add each item to the receipt with improved styling
        items.forEach(item => {
            const itemTotal = item.price * item.quantity;

            // Check if this item has discount information
            const hasDiscount = item.original_price && item.discount_amount;

            // Create the price display with original price strikethrough if discounted
            let priceDisplay = '';
            if (hasDiscount) {
                priceDisplay = `<span style="text-decoration: line-through; color: #777;">${currencySymbol}${item.original_price.toFixed(2)}</span> ${currencySymbol}${item.price.toFixed(2)}`;
            } else {
                priceDisplay = `${currencySymbol}${item.price.toFixed(2)}`;
            }

            receiptHTML += `
                <tr style="border-bottom: 1px solid #eee;">
                    <td style="text-align: left; padding: 5px; font-size: 0.85em;">
                        ${item.title}
                        ${hasDiscount ? `<br><span style="font-size: 0.8em; color: #28a745;">Discounted</span>` : ''}
                    </td>
                    <td style="text-align: right; padding: 5px;">${item.quantity}</td>
                    <td style="text-align: right; padding: 5px;">${priceDisplay}</td>
                    <td style="text-align: right; padding: 5px; font-weight: 500;">${currencySymbol}${itemTotal.toFixed(2)}</td>
                </tr>
            `;
        });

        // Add discount if applicable
        let discountDisplay = '';
        if (discount > 0) {
            if (discountType === 'percent') {
                discountDisplay = `${discount}%`;
            } else {
                discountDisplay = `${currencySymbol}${discount.toFixed(2)}`;
            }
        }

        // Complete the receipt with totals in a more professional layout
        receiptHTML += `
                </table>
            </div>

            <div style="margin-top: 10px; border-top: 1px dashed #ccc; padding-top: 10px;">
                <table style="width: 100%; font-size: 0.9em;">
                    <tr>
                        <td style="text-align: right; padding: 3px;"><strong>Subtotal:</strong></td>
                        <td style="text-align: right; width: 80px; padding: 3px;">${currencySymbol}${subtotal.toFixed(2)}</td>
                    </tr>
                    ${discount > 0 ? `
                    <tr>
                        <td style="text-align: right; padding: 3px;"><strong>Discount:</strong></td>
                        <td style="text-align: right; padding: 3px;">${discountDisplay}</td>
                    </tr>` : ''}
                    <tr>
                        <td style="text-align: right; padding: 3px;"><strong>Tax:</strong></td>
                        <td style="text-align: right; padding: 3px;">${currencySymbol}${tax.toFixed(2)}</td>
                    </tr>
                    <tr style="font-size: 1.1em; font-weight: bold;">
                        <td style="text-align: right; padding: 5px; border-top: 1px solid #000;"><strong>TOTAL:</strong></td>
                        <td style="text-align: right; padding: 5px; border-top: 1px solid #000;">${currencySymbol}${total.toFixed(2)}</td>
                    </tr>
                </table>
            </div>

            <div style="margin-top: 20px; text-align: center; border-top: 1px dashed #ccc; padding-top: 15px; font-size: 0.9em;">
                <p style="margin: 5px 0;">${businessDetails.receipt_footer || 'Thank you for your purchase!'}</p>
                <p style="margin: 5px 0; font-size: 0.8em;">Please keep this receipt for your records</p>
                <p style="margin: 10px 0 5px; font-size: 0.75em;">${dateTimeStr}</p>
            </div>
        `;

        return receiptHTML;
    }

    // Set up modal close buttons
    document.querySelectorAll('.modal .close, .modal .btn-close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Close modals when clicking outside the modal content
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });

    // Event listener for the main Print Receipt button (in the POS interface)
    const showReceiptBtn = document.getElementById('showReceiptBtn');
    if (showReceiptBtn) {
        showReceiptBtn.addEventListener('click', async function() {
            // Check if there are items in the cart
            const cartItems = getCartItems();
            if (cartItems.length === 0) {
                alert('There are no items in the cart to print a receipt for.');
                return;
            }

            // Show loading indicator
            receiptContent.innerHTML = '<div style="text-align: center; padding: 20px;"><p>Generating receipt...</p><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
            receiptModal.style.display = 'block';

            try {
                // Generate and display the receipt
                const receiptHTML = await generateReceipt();
                receiptContent.innerHTML = receiptHTML;
            } catch (error) {
                console.error('Error generating receipt:', error);
                receiptContent.innerHTML = '<div class="alert alert-danger">Error generating receipt. Please try again.</div>';
            }
        });
    }

    // Event listener for the Print button in the receipt modal
    const modalPrintBtn = document.getElementById('modalPrintBtn');
    if (modalPrintBtn) {
        modalPrintBtn.addEventListener('click', function() {
            // Direct print approach - print the current receipt content directly
            try {
                // Create a hidden iframe for printing
                const printFrame = document.createElement('iframe');
                printFrame.style.position = 'fixed';
                printFrame.style.right = '0';
                printFrame.style.bottom = '0';
                printFrame.style.width = '0';
                printFrame.style.height = '0';
                printFrame.style.border = '0';

                document.body.appendChild(printFrame);

                // Get the current receipt content
                const content = receiptContent.innerHTML;

                // Write the receipt content to the iframe with print-optimized styling
                const frameDoc = printFrame.contentWindow.document;

                frameDoc.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Receipt</title>
                        <style>
                            @page {
                                size: 80mm auto;
                                margin: 0;
                            }
                            body {
                                font-family: Arial, sans-serif;
                                margin: 0;
                                padding: 5mm;
                                width: 80mm;
                                background-color: white;
                                color: black;
                            }
                        </style>
                    </head>
                    <body>
                        ${content}
                    </body>
                    </html>
                `);

                frameDoc.close();

                // Wait a moment for content to render, then print
                setTimeout(function() {
                    try {
                        printFrame.contentWindow.focus();
                        printFrame.contentWindow.print();

                        // Remove the iframe after printing
                        setTimeout(function() {
                            document.body.removeChild(printFrame);
                        }, 1000);
                    } catch (e) {
                        console.error('Print error:', e);
                        document.body.removeChild(printFrame);
                    }
                }, 500);
            } catch (e) {
                console.error('Error setting up print:', e);
                alert('Printing failed. Please try again.');
            }
        });
    }

    // Expose addToCart function to global scope for draft orders functionality
    window.addToCart = addToCart;

    // JavaScript syntax fixed - all event listeners now have proper null checks
});  // End of DOMContentLoaded event listener
</script>

<!-- Payment Option Modal for Insufficient Store Credit -->
<div id="paymentOptionModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Insufficient Store Credit</h5>
        <div class="alert alert-warning">
            <p id="creditAvailableText"></p>
            <p id="creditRequiredText"></p>
            <p id="remainingBalanceText" class="mt-2 mb-0 fw-bold"></p>
        </div>
        <p class="text-center mt-3">How would you like to pay the remaining balance?</p>
        <div class="d-flex justify-content-center gap-3 mt-3">
            <button id="payWithCardBtn" class="btn btn-primary btn-lg">
                <i class="fas fa-credit-card me-2"></i>Pay with Card
            </button>
            <button id="payWithCashBtn" class="btn btn-success btn-lg">
                <i class="fas fa-money-bill-wave me-2"></i>Pay with Cash
            </button>
        </div>
    </div>
</div>

<!-- Custom Item Modal -->
<div id="customItemModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h5>Add Custom Item</h5>
        <div class="form-group">
            <input type="text" id="customItemName" class="form-control mb-2" placeholder="Item Name">
            <input type="number" id="customItemPrice" class="form-control mb-2" placeholder="Price">
            <input type="number" id="customItemQuantity" class="form-control mb-2" placeholder="Quantity" value="1" min="1">
            <button id="saveCustomItemBtn" class="btn btn-primary">Add to Cart</button>
        </div>
    </div>
</div>

<!-- Customer modals moved inside the Buy tab content -->


<!-- Buylist Tab Content -->
<div class="pos-tab-pane" id="buylist-tab" style="position: absolute; top: 60px; left: 0; right: 0; bottom: 0; height: calc(100vh - 60px);">
    <div class="buylist-iframe-container">
        <iframe id="buylistIframe" data-src="https://kiosk.tcgsync.com/{{ current_user.username }}" frameborder="0" allowfullscreen allow="fullscreen; camera; microphone; geolocation" referrerpolicy="origin"></iframe>
        <div id="iframe-error" class="iframe-error" style="display: none;">
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Unable to load Buylist Builder</h3>
                <p>There was an issue loading the Buylist Builder. This could be due to:</p>
                <ul>
                    <li>Network connectivity issues</li>
                    <li>The Buylist Builder service may be temporarily unavailable</li>
                    <li>Your browser may be blocking the content</li>
                </ul>
                <p>Please try the following:</p>
                <ul>
                    <li>Click the button below to try again</li>
                    <li>Open in a new tab instead</li>
                    <li>Check your internet connection</li>
                </ul>
                <div class="error-actions">
                    <button id="reload-iframe" class="btn btn-primary">Try Again</button>
                    <a href="https://kiosk.tcgsync.com/{{ current_user.username }}" target="_blank" class="btn btn-secondary">Open in New Tab</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- PSA Checker Tab Content -->
<div class="pos-tab-pane" id="psa-tab" style="position: absolute; top: 60px; left: 0; right: 0; bottom: 0; height: calc(100vh - 60px); overflow-y: auto;">
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header">
                        <h2>PSA Certificate Checker</h2>
                    </div>
                    <div class="card-body">
                        <form id="psaForm">
                            <div class="form-group mb-3">
                                <label for="certNumber">PSA Certificate Number</label>
                                <input type="text" class="form-control" id="certNumber" name="certNumber" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Check Certificate</button>
                        </form>

                        <div id="loading" style="display: none;" class="text-center mt-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Fetching certificate data...</p>
                        </div>

                        <div id="error" style="display: none;" class="alert alert-danger mt-4"></div>

                        <div id="result" style="display: none;" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Card Grader Tab Content -->
<div class="pos-tab-pane" id="card-grader-tab" style="position: absolute; top: 60px; left: 0; right: 0; bottom: 0; height: calc(100vh - 60px); overflow-y: auto;">
    <div class="container mt-4">
        <div class="card-grader-container">
            <div class="card-grader-header">
                <i class="fas fa-star star-icon"></i>
                <h1>Card Grader</h1>
            </div>
            <p>Get professional grading for your trading cards</p>

            <div class="d-flex justify-content-start align-items-center mt-3">
                <div class="scan-counter">
                    <span class="badge bg-info p-2">
                        <i class="fas fa-camera me-1"></i>
                        <span id="remainingScansCount">Loading...</span>
                    </span>
                    <span id="scanStatusBadge" class="badge ms-2" style="display: none;"></span>
                </div>
            </div>

            <div class="header-divider"></div>

            <div class="upload-section">
                <h3 class="mb-4">Upload Cards for Grading</h3>
                <div class="mb-3">
                    <label for="graderFileInput" class="form-label">Select one or more card images</label>
                    <div class="file-input-wrapper">
                        <div class="choose-files-btn">
                            <i class="fas fa-images me-2"></i> Choose Files
                        </div>
                        <input type="file" id="graderFileInput" accept="image/*" multiple>
                    </div>
                    <small class="text-muted mt-2 d-block">Supported formats: JPG, PNG, WEBP (max 10MB)</small>
                </div>
                <div id="uploadedFilesList" class="mt-3">
                    <!-- Selected files will be listed here -->
                </div>

                <!-- Grade button will be shown after files are selected -->
                <div id="gradeButtonContainer" style="display: none; margin-top: 1.5rem;">
                    <button id="processGradeCardBtn" class="btn btn-primary">
                        <i class="fas fa-star"></i> Grade Cards
                    </button>
                </div>
            </div>

            <div id="graderResult" class="results-section">
                <!-- Grading results will be displayed here -->
            </div>
        </div>
    </div>
</div>

<!-- Warehouse Tab Content -->
<div class="pos-tab-pane" id="warehouse-tab" style="position: absolute; top: 60px; left: 0; right: 0; bottom: 0; height: calc(100vh - 60px); overflow-y: auto; color: white;">
    <div class="container-fluid" style="padding-top: 15px;">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4" style="background-color: #1e293b; border: 1px solid rgba(255, 255, 255, 0.1);">
                    <div class="card-body" style="color: white;">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4 class="card-title mb-0" style="color: white;">
                                <i class="fas fa-warehouse me-2"></i>Warehouse Inventory
                            </h4>
                            <div class="d-flex gap-2">
                                <button id="refreshWarehouseBtn" class="btn btn-primary">
                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                </button>
                                <button id="processAllBtn" class="btn btn-success">
                                    <i class="fas fa-check-circle me-1"></i>Process All
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This page shows items purchased through the Buy tab that need to be added to your Shopify inventory.
                        </div>

                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0" style="color: white;">Purchase History</h5>
                                <div class="d-flex gap-2 align-items-center">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="showProcessedToggle">
                                        <label class="form-check-label" for="showProcessedToggle" style="color: white;">Show Processed</label>
                                    </div>
                                    <select id="sortOrderSelect" class="form-select form-select-sm" style="background-color: #2d3748; color: white; border: 1px solid rgba(255, 255, 255, 0.1);">
                                        <option value="newest">Newest First</option>
                                        <option value="oldest">Oldest First</option>
                                    </select>
                                </div>
                            </div>

                            <div id="purchasesContainer">
                                <div class="text-center py-5" id="loadingPurchases">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2" style="color: white;">Loading purchases...</p>
                                </div>
                                <div id="noPurchasesMessage" style="display: none;">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        No purchases found. Use the Buy tab to purchase items from customers.
                                    </div>
                                    <div class="text-center mt-3">
                                        <button id="goBuyTabBtn" class="btn btn-primary">
                                            <i class="fas fa-shopping-basket me-1"></i>Go to Buy Tab
                                        </button>
                                    </div>
                                </div>
                                <div id="purchasesList"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Buy Tab Content -->
<div class="pos-tab-pane" id="buy-tab" style="position: absolute; top: 60px; left: 0; right: 0; bottom: 0; height: calc(100vh - 60px); overflow-y: auto;">
    <div class="container-fluid" style="padding-top: 15px;">
        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex gap-2 mb-3">
                            <div class="input-group" style="width: 70%;">
                                <input type="text" id="buySearchInput" class="form-control" placeholder="Search by name..." autocomplete="off">
                                <button class="btn btn-primary" id="buySearchButton">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                            <!-- Custom item button removed -->
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="buyVendorSelect" class="form-label">Vendor</label>
                                <select id="buyVendorSelect" class="form-select">
                                    <option value="">Select Vendor</option>
                                    <!-- Vendors will be populated dynamically -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="buyProductTypeSelect" class="form-label">Product Type</label>
                                <select id="buyProductTypeSelect" class="form-select">
                                    <option value="">Select Product Type</option>
                                    <!-- Product types will be populated dynamically -->
                                </select>
                            </div>
                        </div>

                        <!-- Results Counter -->
                        <div id="buyResultsCounter" class="results-counter mb-3" style="display: none;">
                            <div>
                                <span class="badge bg-primary" id="buyResultsCount">0</span>
                                <span>products found</span>
                            </div>
                        </div>

                        <!-- Product List -->
                        <div id="buyProductList" class="mb-3">
                            <div id="buyProductCardsContainer" class="category-product-grid">
                                <!-- Products will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cart Section -->
            <div class="col-md-4">
                <div class="card sticky-cart">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-shopping-basket me-2"></i>Buy Cart
                            <span class="badge bg-primary ms-2" id="buyCartItemCount" style="display: none;">0</span>
                        </h5>

                        <!-- Custom Item Form -->
                        <div class="custom-item-form mb-3 p-3 rounded" style="background-color: #1e293b; border: 1px solid rgba(255, 255, 255, 0.1);">
                            <div class="d-flex align-items-center mb-2">
                                <button class="btn btn-sm btn-primary rounded-circle me-2" style="width: 30px; height: 30px; padding: 0; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <input type="text" id="inlineCustomItemDescription" class="form-control"
                                    placeholder="Item Description" autocomplete="off"
                                    title="Enter item description (Press Enter to move to Price)"
                                    style="background-color: transparent; border: none; border-bottom: 1px solid rgba(255, 255, 255, 0.2); color: #f8fafc; padding: 0.5rem 0;">
                            </div>
                            <div class="d-flex gap-2 mb-3">
                                <div class="input-group" style="flex: 2;">
                                    <span class="input-group-text" style="background-color: transparent; border: none; color: #f8fafc;">$</span>
                                    <input type="number" id="inlineCustomItemPrice" class="form-control"
                                        placeholder="Price" min="0.01" step="0.01" autocomplete="off"
                                        title="Enter retail price (Press Enter to move to Quantity)"
                                        style="background-color: transparent; border: none; border-bottom: 1px solid rgba(255, 255, 255, 0.2); color: #f8fafc; padding: 0.5rem 0;">
                                </div>
                                <div class="input-group" style="flex: 1;">
                                    <input type="number" id="inlineCustomItemQuantity" class="form-control text-center"
                                        value="1" min="1" autocomplete="off"
                                        title="Enter quantity (Press Enter to add as Cash)"
                                        style="background-color: transparent; border: none; border-bottom: 1px solid rgba(255, 255, 255, 0.2); color: #f8fafc; padding: 0.5rem 0;">
                                    <span class="input-group-text" style="background-color: transparent; border: none; color: #f8fafc;">Qty</span>
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button id="inlineCustomItemCashBtn" class="btn btn-primary flex-grow-1"
                                    title="Add as Cash (Alt+C)"
                                    style="background-color: #6366f1; border: none; padding: 0.75rem 0;">
                                    <i class="fas fa-money-bill-wave me-1"></i> Cash (40%)
                                </button>
                                <button id="inlineCustomItemCreditBtn" class="btn btn-success flex-grow-1"
                                    title="Add as Credit (Alt+R)"
                                    style="background-color: #10b981; border: none; padding: 0.75rem 0;">
                                    <i class="fas fa-credit-card me-1"></i> Credit (60%)
                                </button>
                            </div>
                        </div>

                        <div class="cart-list-container mb-3" style="max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: rgba(255, 255, 255, 0.2) transparent;">
                            <ul class="list-group" id="buyCartList" style="background-color: transparent; border: none;">
                                <li class="list-group-item empty-cart-message" style="background: transparent; border: 1px dashed rgba(255, 255, 255, 0.2); color: rgba(248, 250, 252, 0.7); text-align: center; padding: 1.5rem; border-radius: 0.5rem; margin: 0.5rem 0;">No items in cart</li>
                            </ul>
                        </div>

                        <div class="totals-section p-3 rounded" style="background-color: #1e293b; border: 1px solid rgba(255, 255, 255, 0.1);">
                            <div class="d-flex justify-content-between mb-2" style="color: rgba(248, 250, 252, 0.7);">
                                <span>Items:</span>
                                <span id="buyItemsCount">0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2" style="color: rgba(248, 250, 252, 0.7);">
                                <span>Cash Total (40%):</span>
                                <span id="buyCashTotal" style="color: #6366f1;">$0.00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2" style="color: rgba(248, 250, 252, 0.7);">
                                <span>Credit Total (60%):</span>
                                <span id="buyCreditTotal" style="color: #10b981;">$0.00</span>
                            </div>
                            <div class="d-flex justify-content-between fw-bold mt-2" style="color: #f8fafc; padding-top: 0.5rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                                <span>Total Value:</span>
                                <span id="buyTotalValue">$0.00</span>
                            </div>
                        </div>

                        <div class="customer-section mb-3" id="buyCustomerSection" style="display: none;" data-customer-id="">
                            <div class="p-3 rounded" style="background-color: #1e293b; border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="m-0" style="color: #f8fafc;">Customer</h6>
                                    <button id="buyChangeCustomerBtn" class="btn btn-sm btn-outline-light" onclick="showBuyCustomerSearchModal()">
                                        <i class="fas fa-exchange-alt me-1"></i>Change
                                    </button>
                                </div>
                                <div class="customer-info">
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="fas fa-user me-2" style="color: rgba(248, 250, 252, 0.7);"></i>
                                        <span id="buyCustomerName" style="color: #f8fafc; font-weight: 500;">No customer selected</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-envelope me-2" style="color: rgba(248, 250, 252, 0.7);"></i>
                                        <span id="buyCustomerEmail" style="color: rgba(248, 250, 252, 0.7);">Select a customer to complete purchase</span>
                                    </div>
                                </div>
                                <div id="buyCustomerCreditSection" class="mt-2" style="display: none;">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <i class="fas fa-credit-card me-2" style="color: rgba(248, 250, 252, 0.7);"></i>
                                            <span style="color: rgba(248, 250, 252, 0.7);">Store Credit: </span>
                                            <span id="buyCustomerCreditAmount" class="ms-1" style="color: #10b981; font-weight: 500;">$0.00</span>
                                        </div>
                                        <button id="buyApplyCreditBtn" class="btn btn-sm btn-success" onclick="applyBuyStoreCredit()">
                                            <i class="fas fa-plus-circle me-1"></i>Apply Credit
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="payment-section mt-3">
                            <button id="buyCompleteBtn" class="btn w-100 py-3" disabled
                                style="background-color: #10b981; border: none; color: white; font-weight: 500; border-radius: 0.375rem;"
                                onclick="handleCompletePurchase()">
                                <i class="fas fa-user-check me-2"></i>Select Customer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Buy Customer Search Modal -->
    <div id="buyCustomerSearchModal" class="modal" style="display: none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0, 0, 0, 0.8); padding-top: 60px;">
        <div class="modal-content" style="background-color: #1e293b; margin: 5% auto; padding: 30px; border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; width: 60%; max-width: 700px; color: #f8fafc; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5); position: relative;">
            <span class="close" style="position: absolute; right: 20px; top: 15px; color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer;" onclick="document.getElementById('buyCustomerSearchModal').style.display='none';">&times;</span>
            <h5 class="mb-4">Select Customer</h5>

            <div class="search-section mb-4">
                <div class="input-group">
                    <input type="text" id="buyCustomerSearchInput" class="form-control" placeholder="Search by name or email"
                        style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); color: #f8fafc; padding: 12px;"
                        onkeypress="if(event.key === 'Enter') { performBuyCustomerSearch(); return false; }">
                    <button id="buyCustomerSearchBtn" class="btn btn-primary" style="background-color: #6366f1; border: none;"
                        onclick="performBuyCustomerSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <div id="buyCustomerSearchResults" class="search-results mb-4" style="max-height: 300px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: rgba(255, 255, 255, 0.2) transparent;">
                <div class="text-center p-4 text-muted">
                    <i class="fas fa-search fa-2x mb-3"></i>
                    <p>Search for a customer by name or email</p>
                </div>
            </div>

            <div class="text-center">
                <button id="buyCreateCustomerBtn" class="btn btn-success" style="background-color: #10b981; border: none;"
                    onclick="showBuyCreateCustomerModal()">
                    <i class="fas fa-plus-circle me-2"></i>Create New Customer
                </button>
            </div>
        </div>
    </div>

    <!-- Buy Create Customer Modal -->
    <div id="buyCreateCustomerModal" class="modal" style="display: none; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0, 0, 0, 0.8); padding-top: 60px;">
        <div class="modal-content" style="background-color: #1e293b; margin: 5% auto; padding: 30px; border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; width: 50%; max-width: 500px; color: #f8fafc; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5); position: relative;">
            <span class="close" style="position: absolute; right: 20px; top: 15px; color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer;" onclick="document.getElementById('buyCreateCustomerModal').style.display='none';">&times;</span>
            <h5 class="mb-4">Create New Customer</h5>

            <form id="buyCreateCustomerForm" onsubmit="event.preventDefault(); createBuyCustomer();">
                <div class="mb-3">
                    <label for="buyNewCustomerFirstName" class="form-label">First Name</label>
                    <input type="text" id="buyNewCustomerFirstName" class="form-control" required
                        style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); color: #f8fafc; padding: 12px;">
                </div>

                <div class="mb-3">
                    <label for="buyNewCustomerLastName" class="form-label">Last Name</label>
                    <input type="text" id="buyNewCustomerLastName" class="form-control" required
                        style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); color: #f8fafc; padding: 12px;">
                </div>

                <div class="mb-3">
                    <label for="buyNewCustomerEmail" class="form-label">Email</label>
                    <input type="email" id="buyNewCustomerEmail" class="form-control" required
                        style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); color: #f8fafc; padding: 12px;">
                </div>

                <div class="mb-3">
                    <label for="buyNewCustomerInitialCredit" class="form-label">Initial Store Credit</label>
                    <div class="input-group">
                        <span class="input-group-text" style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); color: #f8fafc;">$</span>
                        <input type="number" id="buyNewCustomerInitialCredit" class="form-control" min="0" step="0.01" value="0"
                            style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); color: #f8fafc; padding: 12px;">
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-success" style="background-color: #10b981; border: none; padding: 10px 20px;">
                        <i class="fas fa-user-plus me-2"></i>Create Customer
                    </button>
                </div>
            </form>

            <div id="buyCreateCustomerMessage" class="mt-3"></div>
        </div>
    </div>
</div>

    <!-- Purchase Scans Modal -->
    <div class="modal" id="purchaseScansModal" tabindex="-1" aria-labelledby="purchaseScansModalLabel" aria-hidden="true" style="z-index: 9999;">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 800px; width: 75%;">
            <div class="modal-content bg-dark text-light">
                <div class="modal-header border-secondary">
                    <h3 class="modal-title" id="purchaseScansModalLabel">
                        <i class="fas fa-star me-2 text-warning"></i> Purchase Card Grader Scans
                    </h3>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <!-- Header Section with Gradient Background -->
                    <div class="p-4 mb-4" style="background: linear-gradient(135deg, #1a2433 0%, #0f172a 100%);">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h3 class="fs-2 mb-2">Card Grader Scans</h3>
                                <p class="text-light opacity-75 mb-0">Get professional grading for your trading cards with our advanced AI-powered system</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-primary p-3 rounded-circle mb-2" style="width: 70px; height: 70px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-camera fs-2 text-white"></i>
                                </div>
                                <span class="badge bg-info px-3 py-2 fs-6">£0.03 per scan</span>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="px-4 pb-4">
                        <div class="row">
                            <!-- Left Column - Purchase Input -->
                            <div class="col-md-7 pe-md-4">
                                <div class="card bg-dark mb-4" style="border: 1px solid rgba(255, 255, 255, 0.1);">
                                    <div class="card-body p-4">
                                        <h4 class="mb-3">Purchase Details</h4>

                                        <div class="form-group mb-4">
                                            <label for="purchaseAmount" class="form-label">Amount (£)</label>
                                            <div class="input-group input-group-lg">
                                                <span class="input-group-text bg-primary text-white">£</span>
                                                <input type="number" class="form-control" id="purchaseAmount" value="10" min="10" step="1">
                                            </div>
                                            <div class="d-flex justify-content-between mt-2">
                                                <small class="form-text text-muted">Minimum purchase: £10</small>
                                                <small class="form-text text-info">Recommended: £30 (1000 scans)</small>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center p-3 bg-secondary rounded mb-3">
                                            <div>
                                                <span class="d-block fs-5 fw-bold">You will receive:</span>
                                                <div class="d-flex align-items-center mt-1">
                                                    <i class="fas fa-camera me-2 text-info"></i>
                                                    <span id="summaryScans" class="fs-4">333 scans</span>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <span class="d-block text-muted">Total amount:</span>
                                                <span id="summaryAmount" class="fs-3 fw-bold">£10.00</span>
                                            </div>
                                        </div>

                                        <div id="purchaseStatus"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Information -->
                            <div class="col-md-5">
                                <div class="card bg-dark mb-3" style="border: 1px solid rgba(255, 255, 255, 0.1);">
                                    <div class="card-body p-4">
                                        <h5 class="card-title mb-3"><i class="fas fa-info-circle me-2 text-info"></i> About Card Grader</h5>
                                        <p>Our AI-powered card grading system provides professional analysis of your trading cards, including:</p>
                                        <ul class="mb-0">
                                            <li class="mb-2">Overall condition assessment</li>
                                            <li class="mb-2">Surface quality evaluation</li>
                                            <li class="mb-2">Edge and corner analysis</li>
                                            <li>Centering measurement</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="card border-success bg-dark mb-3" style="border-width: 1px;">
                                    <div class="card-body p-3">
                                        <h5 class="card-title text-success"><i class="fas fa-shield-alt me-2"></i> Secure Payment</h5>
                                        <p class="card-text mb-0">Your payment is processed securely through Stripe. Scans are added to your account immediately after payment.</p>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center bg-primary bg-opacity-25 p-3 rounded">
                                    <i class="fas fa-tag fs-4 text-primary me-3"></i>
                                    <div>
                                        <h6 class="mb-1">Special Offer</h6>
                                        <p class="mb-0">Purchase £50 or more and get 10% extra scans for free!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-secondary justify-content-between">
                    <button type="button" class="btn btn-outline-light btn-lg px-4" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i> Cancel
                    </button>
                    <button type="button" class="btn btn-success btn-lg px-5" id="confirmPurchaseBtn">
                        <i class="fas fa-credit-card me-2"></i> Proceed to Payment
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Receipt Modal -->
<div id="receiptModal" class="modal">
    <div class="modal-content" style="max-width: 400px; margin: 0 auto; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.3);">
        <div style="background-color: #f8f9fa; padding: 15px; border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center;">
            <h5 style="margin: 0; font-weight: 600; color: #212529;">Receipt Preview</h5>
            <button type="button" class="btn-close" aria-label="Close"
                    style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6c757d; padding: 0;"
                    onclick="document.getElementById('receiptModal').style.display='none';">
                &times;
            </button>
        </div>
        <div id="receiptContent" style="background-color: white; color: black; padding: 20px; font-family: Arial, sans-serif; max-height: 70vh; overflow-y: auto;">
            <!-- Receipt content will be generated dynamically -->
        </div>
        <div style="background-color: #f8f9fa; padding: 15px; border-top: 1px solid #dee2e6; text-align: center;">
            <button id="modalPrintBtn" class="btn btn-primary btn-lg" style="padding: 12px 30px; font-weight: 600; font-size: 1.1em;">
                <i class="fas fa-print me-2"></i>Print Receipt
            </button>
            <div style="margin-top: 10px; font-size: 0.8em; color: #6c757d;">
                <i class="fas fa-info-circle me-1"></i> Receipt will print directly to your default printer
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.10.2/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
<script src="{{ url_for('static', filename='js/trollaus_warehouse.js') }}"></script>

<script>
    // Store the username in the window object for easy access
    window.username = "{{ current_user.username }}";
    console.log("Current username:", window.username);

    // Custom item form functionality is now handled in pos_buy.js

    // Event listeners for the custom item form are now handled in pos_buy.js

    // Make the showBuyCustomerSearchModal function available globally
    window.showBuyCustomerSearchModal = function() {
        console.log('Global showBuyCustomerSearchModal called');

        // Get the modal element
        const modal = document.getElementById('buyCustomerSearchModal');
        console.log('Modal element:', modal);

        if (modal) {
            // Log current display style
            console.log('Current modal display style:', modal.style.display);

            // Force the modal to be visible with important styles
            modal.setAttribute('style', 'display: block !important; position: fixed !important; z-index: 99999 !important; left: 0 !important; top: 0 !important; width: 100% !important; height: 100% !important; overflow: auto !important; background-color: rgba(0, 0, 0, 0.8) !important; padding-top: 60px !important;');

            console.log('Modal style set to:', modal.getAttribute('style'));

            // Reset search input
            const searchInput = document.getElementById('buyCustomerSearchInput');
            console.log('Search input element:', searchInput);

            if (searchInput) {
                searchInput.value = '';
                setTimeout(() => searchInput.focus(), 100);
            }

            // Reset search results
            const resultsContainer = document.getElementById('buyCustomerSearchResults');
            console.log('Results container element:', resultsContainer);

            if (resultsContainer) {
                resultsContainer.innerHTML = `
                    <div class="text-center p-4 text-muted">
                        <i class="fas fa-search fa-2x mb-3"></i>
                        <p>Search for a customer by name or email</p>
                    </div>
                `;
            }

            // Check if modal content is visible
            const modalContent = modal.querySelector('.modal-content');
            console.log('Modal content element:', modalContent);

            if (modalContent) {
                modalContent.setAttribute('style', 'background-color: #1e293b !important; margin: 5% auto !important; padding: 30px !important; border: 1px solid rgba(255, 255, 255, 0.1) !important; border-radius: 12px !important; width: 60% !important; max-width: 700px !important; color: #f8fafc !important; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5) !important; position: relative !important;');
                console.log('Modal content style set');
            }
        } else {
            console.error('Customer search modal not found');

            // Check if the modal exists in the DOM at all
            const allModals = document.querySelectorAll('.modal');
            console.log('All modals in the DOM:', allModals);

            // Try to find any element with ID containing "customer" and "modal"
            const customerElements = document.querySelectorAll('[id*="customer"][id*="modal"]');
            console.log('Elements with ID containing "customer" and "modal":', customerElements);
        }
    };

    // Add a test function to directly show the modal
    window.testShowModal = function() {
        console.log('Test show modal called');

        // Create a test modal if it doesn't exist
        let testModal = document.getElementById('testModal');

        if (!testModal) {
            testModal = document.createElement('div');
            testModal.id = 'testModal';
            testModal.style.position = 'fixed';
            testModal.style.zIndex = '99999';
            testModal.style.left = '0';
            testModal.style.top = '0';
            testModal.style.width = '100%';
            testModal.style.height = '100%';
            testModal.style.overflow = 'auto';
            testModal.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            testModal.style.display = 'block';

            const modalContent = document.createElement('div');
            modalContent.style.backgroundColor = '#1e293b';
            modalContent.style.margin = '15% auto';
            modalContent.style.padding = '20px';
            modalContent.style.border = '1px solid #888';
            modalContent.style.width = '50%';
            modalContent.style.color = 'white';
            modalContent.style.textAlign = 'center';

            const closeBtn = document.createElement('span');
            closeBtn.style.color = '#aaa';
            closeBtn.style.float = 'right';
            closeBtn.style.fontSize = '28px';
            closeBtn.style.fontWeight = 'bold';
            closeBtn.style.cursor = 'pointer';
            closeBtn.innerHTML = '&times;';
            closeBtn.onclick = function() {
                testModal.style.display = 'none';
            };

            const heading = document.createElement('h2');
            heading.textContent = 'Test Modal';

            const message = document.createElement('p');
            message.textContent = 'This is a test modal to check if modals can be displayed.';

            const button = document.createElement('button');
            button.textContent = 'Try Customer Modal';
            button.style.padding = '10px 20px';
            button.style.margin = '20px 0';
            button.style.backgroundColor = '#4CAF50';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.cursor = 'pointer';
            button.onclick = function() {
                testModal.style.display = 'none';
                setTimeout(() => {
                    const customerModal = document.getElementById('buyCustomerSearchModal');
                    if (customerModal) {
                        customerModal.style.display = 'block';
                    } else {
                        alert('Customer modal not found!');
                    }
                }, 500);
            };

            modalContent.appendChild(closeBtn);
            modalContent.appendChild(heading);
            modalContent.appendChild(message);
            modalContent.appendChild(button);
            testModal.appendChild(modalContent);

            document.body.appendChild(testModal);
        } else {
            testModal.style.display = 'block';
        }
    };

    // Make the selectBuyCustomer function available globally
    window.selectBuyCustomer = function(customerId, customerName, customerEmail, storeCredit = 0) {
        console.log('Global selectBuyCustomer called:', customerId, customerName, customerEmail, storeCredit);

        // Store the selected customer
        window.selectedCustomer = {
            id: customerId,
            name: customerName,
            email: customerEmail,
            storeCredit: storeCredit
        };

        // Update the customer section UI
        const customerSection = document.getElementById('buyCustomerSection');
        const customerNameEl = document.getElementById('buyCustomerName');
        const customerEmailEl = document.getElementById('buyCustomerEmail');
        const customerCreditSection = document.getElementById('buyCustomerCreditSection');
        const customerCreditAmount = document.getElementById('buyCustomerCreditAmount');

        if (customerSection && customerNameEl && customerEmailEl) {
            customerNameEl.textContent = customerName;
            customerEmailEl.textContent = customerEmail;
            customerSection.style.display = 'block';

            // Set the customer ID in the data attribute
            customerSection.setAttribute('data-customer-id', customerId);

            // Update store credit display
            if (customerCreditSection && customerCreditAmount) {
                customerCreditAmount.textContent = `$${storeCredit.toFixed(2)}`;
                customerCreditSection.style.display = 'block';
            }
        }

        // Close the customer search modal
        const modal = document.getElementById('buyCustomerSearchModal');
        if (modal) {
            modal.style.display = 'none';
        }

        // Enable the complete button and change its text to "Complete Purchase"
        const buyCompleteBtn = document.getElementById('buyCompleteBtn');
        if (buyCompleteBtn) {
            buyCompleteBtn.disabled = false;
            buyCompleteBtn.innerHTML = '<i class="fas fa-check-circle me-2"></i>Complete Purchase';
        }
    };

    // Make the create customer modal function available globally
    window.showBuyCreateCustomerModal = function() {
        console.log('Global showBuyCreateCustomerModal called');

        const modal = document.getElementById('buyCreateCustomerModal');
        if (modal) {
            // Reset form fields
            const form = document.getElementById('buyCreateCustomerForm');
            if (form) {
                form.reset();
            }

            // Clear any previous messages
            const messageDiv = document.getElementById('buyCreateCustomerMessage');
            if (messageDiv) {
                messageDiv.innerHTML = '';
            }

            // Hide the customer search modal
            const searchModal = document.getElementById('buyCustomerSearchModal');
            if (searchModal) {
                searchModal.style.display = 'none';
            }

            // Show the create customer modal
            modal.style.display = 'block';

            // Focus on the first name input
            const firstNameInput = document.getElementById('buyNewCustomerFirstName');
            if (firstNameInput) {
                setTimeout(() => firstNameInput.focus(), 100);
            }
        } else {
            console.error('Create customer modal not found');
        }
    };

    // Make the create customer function available globally
    window.createBuyCustomer = function() {
        console.log('Global createBuyCustomer called');

        const firstNameInput = document.getElementById('buyNewCustomerFirstName');
        const lastNameInput = document.getElementById('buyNewCustomerLastName');
        const emailInput = document.getElementById('buyNewCustomerEmail');
        const initialCreditInput = document.getElementById('buyNewCustomerInitialCredit');
        const messageDiv = document.getElementById('buyCreateCustomerMessage');

        if (!firstNameInput || !lastNameInput || !emailInput || !initialCreditInput || !messageDiv) {
            console.error('One or more form elements not found');
            return;
        }

        const firstName = firstNameInput.value.trim();
        const lastName = lastNameInput.value.trim();
        const email = emailInput.value.trim();
        const initialCredit = parseFloat(initialCreditInput.value) || 0;

        if (!firstName || !lastName || !email) {
            messageDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>Please fill in all required fields
                </div>
            `;
            return;
        }

        // Show loading state
        messageDiv.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">Creating customer...</p>
            </div>
        `;

        // Prepare customer data
        const customerData = {
            firstName: firstName,
            lastName: lastName,
            email: email
        };

        // Add initial credit if provided
        if (initialCredit > 0) {
            customerData.initialCredit = initialCredit;
        }

        // Create customer on the server
        fetch('/shopify/customers/api/customer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(customerData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                messageDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>${data.error}
                    </div>
                `;
                return;
            }

            if (data.success) {
                messageDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>Customer created successfully
                    </div>
                `;

                // Select the newly created customer
                setTimeout(() => {
                    // Close the create customer modal
                    const modal = document.getElementById('buyCreateCustomerModal');
                    if (modal) {
                        modal.style.display = 'none';
                    }

                    // Select the customer
                    selectBuyCustomer(
                        data.customer.id,
                        `${data.customer.first_name} ${data.customer.last_name}`,
                        data.customer.email
                    );
                }, 1000);
            } else {
                messageDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>${data.message || 'Failed to create customer'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error creating customer:', error);
            messageDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>Error creating customer: ${error.message}
                </div>
            `;
        });
    };

    // New handler function that bypasses the recursion issue
    window.handleCompletePurchase = function() {
        console.log('handleCompletePurchase called');

        // Prevent multiple clicks
        if (window.isProcessingPurchase) {
            console.log('Purchase already in progress, ignoring duplicate call');
            return;
        }

        window.isProcessingPurchase = true;

        // Set a timeout to reset the flag after 2 seconds (in case of errors)
        setTimeout(function() {
            window.isProcessingPurchase = false;
        }, 2000);

        // Skip trying to call the external function and use our implementation directly
        console.log('Using direct implementation for purchase completion');

        // Check for customer in multiple ways
        let hasCustomer = false;
        let customerName = '';

        // Check window.selectedCustomer
        if (window.selectedCustomer && window.selectedCustomer.id) {
            hasCustomer = true;
            customerName = window.selectedCustomer.name || 'Selected Customer';
        }

        // If not found, check DOM
        if (!hasCustomer) {
            const customerSection = document.getElementById('buyCustomerSection');
            const customerNameEl = document.getElementById('buyCustomerName');

            if (customerSection &&
                customerSection.style.display !== 'none' &&
                customerNameEl &&
                customerNameEl.textContent !== 'No customer selected' &&
                customerSection.getAttribute('data-customer-id')) {

                hasCustomer = true;
                customerName = customerNameEl.textContent;

                // Recreate selectedCustomer object from DOM
                const customerEmailEl = document.getElementById('buyCustomerEmail');
                window.selectedCustomer = {
                    id: customerSection.getAttribute('data-customer-id'),
                    name: customerNameEl.textContent,
                    email: customerEmailEl ? customerEmailEl.textContent : ''
                };

                console.log('Recreated selectedCustomer from DOM:', window.selectedCustomer);
            }
        }

        if (!hasCustomer) {
            TCGDialog.alert({
                title: 'Customer Required',
                message: 'Please select a customer before completing the purchase.',
                type: 'warning',
                onClose: function() {
                    if (typeof showBuyCustomerSearchModal === 'function') {
                        showBuyCustomerSearchModal();
                    }
                }
            });
            window.isProcessingPurchase = false;
            return;
        }

        if (!window.buyCart || window.buyCart.length === 0) {
            TCGDialog.alert({
                title: 'Empty Cart',
                message: 'There are no items in the cart to complete the purchase.',
                type: 'warning'
            });
            window.isProcessingPurchase = false;
            return;
        }

        // Calculate totals
        let cashTotal = 0;
        let creditTotal = 0;

        window.buyCart.forEach(item => {
            if (item.paymentType === 'cash') {
                cashTotal += item.buyPrice * item.quantity;
            } else {
                creditTotal += item.buyPrice * item.quantity;
            }
        });

        TCGDialog.confirm({
            title: 'Complete Cash Purchase',
            message: `Complete cash purchase for ${customerName}?`,
            detail: `<div class="mb-2">
                <strong>Cash Total:</strong> <span class="tcg-dialog-highlight">$${cashTotal.toFixed(2)}</span>
            </div>
            <div class="text-muted">
                <i class="fas fa-info-circle me-1"></i> Store credit will be applied separately.
            </div>`,
            confirmText: 'Complete Purchase',
            cancelText: 'Cancel',
            confirmButtonClass: 'tcg-btn-success',
            onConfirm: function() {
                TCGDialog.toast({
                    title: 'Purchase Completed',
                    message: 'Cash purchase has been completed successfully.',
                    type: 'success'
                });

                // Remove cash items from cart
                window.buyCart = window.buyCart.filter(item => item.paymentType !== 'cash');

                // Update UI
                if (typeof updateBuyCartItems === 'function') {
                    updateBuyCartItems();
                }

                if (typeof updateBuyCartTotals === 'function') {
                    updateBuyCartTotals();
                }

                // Reset the processing flag
                window.isProcessingPurchase = false;
            }
        });

        // Reset the processing flag if the user cancels
        TCGDialog.confirm.onCancel = function() {
            window.isProcessingPurchase = false;
        };
    };

    // Keep the original function for compatibility
    window.completeBuyPurchase = function() {
        console.log('completeBuyPurchase called - redirecting to handleCompletePurchase');
        window.handleCompletePurchase();
    };

    // Make the apply store credit function available globally
    window.applyBuyStoreCredit = function() {
        console.log('Global applyBuyStoreCredit called');

        // Prevent multiple clicks
        if (window.isProcessingCredit) {
            console.log('Credit application already in progress, ignoring duplicate call');
            return;
        }

        window.isProcessingCredit = true;

        // Set a timeout to reset the flag after 2 seconds (in case of errors)
        setTimeout(function() {
            window.isProcessingCredit = false;
        }, 2000);

        // Get customer ID from the DOM or selectedCustomer object
        let customerId = null;

        // Try to get customer ID from the DOM first
        const customerNameEl = document.getElementById('buyCustomerName');
        const customerSection = document.getElementById('buyCustomerSection');

        if (customerSection && customerSection.style.display !== 'none' && customerNameEl && customerNameEl.textContent !== 'No customer selected') {
            // Customer is displayed in the UI, try to get ID from data attribute
            customerId = customerSection.getAttribute('data-customer-id');
        }

        // If not found in DOM, try from selectedCustomer object
        if (!customerId && window.selectedCustomer && window.selectedCustomer.id) {
            customerId = window.selectedCustomer.id;
        }

        // If still no customer ID, show error
        if (!customerId) {
            TCGDialog.alert({
                title: 'Customer Required',
                message: 'Please select a customer before applying store credit.',
                type: 'warning',
                onClose: function() {
                    showBuyCustomerSearchModal();
                }
            });
            return;
        }

        // Get the credit total from the UI
        const creditTotalElement = document.getElementById('buyCreditTotal');
        let creditTotal = 0;

        if (creditTotalElement) {
            // Extract the numeric value from the text (remove $ and convert to number)
            const creditTotalText = creditTotalElement.textContent.replace('$', '');
            creditTotal = parseFloat(creditTotalText) || 0;
        } else {
            // Fallback to calculating from cart if element not found
            if (!window.buyCart || window.buyCart.length === 0) {
                alert('There are no items in the cart to apply store credit to.');
                return;
            }

            window.buyCart.forEach(item => {
                if (item.paymentType === 'credit') {
                    creditTotal += item.buyPrice * item.quantity;
                }
            });
        }

        if (creditTotal <= 0) {
            TCGDialog.alert({
                title: 'No Credit to Apply',
                message: 'There are no credit items to apply. The credit total is $0.00.',
                type: 'warning'
            });
            return;
        }

        // Show confirmation dialog using TCGDialog
        TCGDialog.confirm({
            title: 'Confirm Store Credit',
            message: `Apply <span class="tcg-dialog-highlight">$${creditTotal.toFixed(2)}</span> store credit to this customer?`,
            confirmText: 'Apply Credit',
            cancelText: 'Cancel',
            confirmButtonClass: 'tcg-btn-success',
            onConfirm: function() {
                // Show processing dialog
                const processingDialog = TCGDialog.processing({
                    message: 'Applying store credit...'
                });

            // Add store credit to the customer's account
            fetch(`/shopify/customers/api/customer/${customerId}/store-credit/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    amount: creditTotal,
                    note: `Store credit from buy purchase - ${new Date().toLocaleString()}`,
                    staff_name: 'POS System'
                })
            })
            .then(response => response.json())
            .then(data => {
                // Close processing dialog
                processingDialog.close();

                if (data.error) {
                    console.error('Error adding store credit:', data.error);
                    TCGDialog.alert({
                        title: 'Error',
                        message: `Error adding store credit: ${data.error}`,
                        type: 'error'
                    });
                    return;
                }

                // Get the updated store credit balance
                fetch(`/shopify/customers/api/customer/${customerId}/store-credit`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(creditData => {
                    // Calculate total store credit from accounts
                    let updatedCredit = 0;

                    // Check if we have accounts data (same format as in customer search)
                    if (creditData.accounts && creditData.accounts.length > 0) {
                        creditData.accounts.forEach(account => {
                            updatedCredit += parseFloat(account.balance.amount) || 0;
                        });
                    } else if (creditData.store_credit) {
                        // Fallback to store_credit field if available
                        updatedCredit = parseFloat(creditData.store_credit) || 0;
                    }

                    console.log('Updated store credit data:', creditData);
                    console.log('Calculated updated credit:', updatedCredit);

                    // Update the store credit display in the UI
                    const customerCreditAmount = document.getElementById('buyCustomerCreditAmount');
                    if (customerCreditAmount) {
                        customerCreditAmount.textContent = `$${updatedCredit.toFixed(2)}`;
                    }

                    // Show success message with updated balance
                    TCGDialog.alert({
                        title: 'Store Credit Applied',
                        message: `<span class="tcg-dialog-highlight">$${creditTotal.toFixed(2)}</span> has been added to the customer's store credit.`,
                        detail: `<div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>Updated Balance:</strong> $${updatedCredit.toFixed(2)}
                            </div>
                        </div>`,
                        type: 'success',
                        buttonText: 'Done'
                    });

                    // Also show a toast notification
                    TCGDialog.toast({
                        title: 'Store Credit Updated',
                        message: `New balance: $${updatedCredit.toFixed(2)}`,
                        type: 'success',
                        duration: 5000
                    });

                    // Items are now saved to TrollAus collection when added to the cart
                    // No need to save them again here

                    // Reset the processing flag
                    window.isProcessingCredit = false;
                })
                .catch(error => {
                    console.error('Error fetching updated store credit:', error);
                    // Show basic success message if we can't get the updated balance
                    TCGDialog.alert({
                        title: 'Store Credit Applied',
                        message: `$${creditTotal.toFixed(2)} has been added to the customer's store credit.`,
                        type: 'success',
                        onClose: function() {
                            // Reset the processing flag
                            window.isProcessingCredit = false;
                        }
                    });
                });

                // Clear the credit items from the cart
                window.buyCart = window.buyCart.filter(item => item.paymentType !== 'credit');

                // Update UI
                if (typeof updateBuyCartItems === 'function') {
                    updateBuyCartItems();
                } else {
                    const buyCartList = document.getElementById('buyCartList');
                    if (buyCartList) {
                        if (window.buyCart.length === 0) {
                            buyCartList.innerHTML = '<li class="list-group-item empty-cart-message" style="background: transparent; border: 1px dashed rgba(255, 255, 255, 0.2); color: rgba(248, 250, 252, 0.7); text-align: center; padding: 1.5rem; border-radius: 0.5rem; margin: 0.5rem 0;">No items in cart</li>';
                        } else {
                            // Refresh the cart items that are left
                            const items = buyCartList.querySelectorAll('li');
                            items.forEach(item => {
                                const index = parseInt(item.dataset.index);
                                if (!isNaN(index) && window.buyCart[index] && window.buyCart[index].paymentType === 'credit') {
                                    item.remove();
                                }
                            });
                        }
                    }
                }

                // Update totals
                if (typeof updateBuyCartTotals === 'function') {
                    updateBuyCartTotals();
                } else {
                    // Reset the credit total in the UI directly
                    const buyCreditTotal = document.getElementById('buyCreditTotal');
                    if (buyCreditTotal) {
                        buyCreditTotal.textContent = '$0.00';
                    }

                    // Update the total value
                    const buyTotalValue = document.getElementById('buyTotalValue');
                    const buyCashTotal = document.getElementById('buyCashTotal');
                    if (buyTotalValue && buyCashTotal) {
                        const cashTotal = parseFloat(buyCashTotal.textContent.replace('$', '')) || 0;
                        buyTotalValue.textContent = '$' + cashTotal.toFixed(2);
                    }
                }

                // Clear the selected customer
                window.selectedCustomer = null;

                // Hide customer section
                const customerSection = document.getElementById('buyCustomerSection');
                if (customerSection) {
                    customerSection.style.display = 'none';
                }

                // Disable the complete button
                const buyCompleteBtn = document.getElementById('buyCompleteBtn');
                if (buyCompleteBtn) {
                    buyCompleteBtn.disabled = true;
                }
            })
            .catch(error => {
                console.error('Error applying store credit:', error);

                // Close processing dialog
                processingDialog.close();

                // Reset the processing flag
                window.isProcessingCredit = false;

                // Show error message using TCGDialog
                TCGDialog.alert({
                    title: 'Error',
                    message: `Error applying store credit: ${error.message}`,
                    type: 'error'
                });
            });
        }});

        // Reset the processing flag if the user cancels
        TCGDialog.confirm.onCancel = function() {
            window.isProcessingCredit = false;
        };
    };

    // Make the customer search function available globally
    window.performBuyCustomerSearch = function() {
        console.log('Global performBuyCustomerSearch called');

        const searchInput = document.getElementById('buyCustomerSearchInput');
        const resultsContainer = document.getElementById('buyCustomerSearchResults');

        if (!searchInput || !resultsContainer) {
            console.error('Search input or results container not found');
            return;
        }

        const searchTerm = searchInput.value.trim();
        if (!searchTerm) {
            TCGDialog.alert({
                title: 'Search Required',
                message: 'Please enter a search term to find customers.',
                type: 'warning'
            });
            return;
        }

        // Show loading state
        resultsContainer.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2 text-light">Searching for "${searchTerm}"...</p>
            </div>
        `;

        // Fetch customers from the server
        fetch(`/pos/search_customer?search=${encodeURIComponent(searchTerm)}`)
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    resultsContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>${data.message || 'Error searching customers'}
                        </div>
                    `;
                    return;
                }

                const customers = data.customers || [];

                if (customers.length === 0) {
                    resultsContainer.innerHTML = `
                        <div class="text-center p-4">
                            <p class="text-light">No customers found matching "${searchTerm}"</p>
                            <button class="btn btn-outline-light mt-2" onclick="showBuyCreateCustomerModal()">
                                <i class="fas fa-plus-circle me-2"></i>Create New Customer
                            </button>
                        </div>
                    `;
                    return;
                }

                // Display customers
                let resultsHTML = '<div class="list-group">';

                // Process each customer
                const customerPromises = customers.map(customer => {
                    return new Promise((resolve) => {
                        const fullName = `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || 'No Name';
                        const email = customer.email || 'No Email';

                        // Fetch store credit for this customer
                        fetch(`/shopify/customers/api/customer/${customer.id}/store-credit`)
                            .then(response => response.json())
                            .then(creditData => {
                                // Calculate total store credit
                                let totalCredit = 0;
                                if (creditData.accounts && creditData.accounts.length > 0) {
                                    creditData.accounts.forEach(account => {
                                        totalCredit += parseFloat(account.balance.amount) || 0;
                                    });
                                }

                                // Create customer result HTML with store credit
                                const customerHTML = `
                                    <button class="list-group-item list-group-item-action customer-result"
                                        onclick="selectBuyCustomer('${customer.id}', '${fullName}', '${email}', ${totalCredit})"
                                        style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); color: #f8fafc; margin-bottom: 0.5rem; border-radius: 0.375rem;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-bold">${fullName}</div>
                                                <div class="small text-muted">${email}</div>
                                                <div class="mt-1">
                                                    <span class="badge" style="background-color: #10b981; color: white;">
                                                        <i class="fas fa-credit-card me-1"></i>Store Credit: $${totalCredit.toFixed(2)}
                                                    </span>
                                                </div>
                                            </div>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-check me-1"></i>Select
                                            </button>
                                        </div>
                                    </button>
                                `;
                                resolve(customerHTML);
                            })
                            .catch(error => {
                                console.error('Error fetching store credit:', error);
                                // Create customer result HTML without store credit
                                const customerHTML = `
                                    <button class="list-group-item list-group-item-action customer-result"
                                        onclick="selectBuyCustomer('${customer.id}', '${fullName}', '${email}', 0)"
                                        style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1); color: #f8fafc; margin-bottom: 0.5rem; border-radius: 0.375rem;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-bold">${fullName}</div>
                                                <div class="small text-muted">${email}</div>
                                                <div class="mt-1">
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-credit-card me-1"></i>Store Credit: $0.00
                                                    </span>
                                                </div>
                                            </div>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-check me-1"></i>Select
                                            </button>
                                        </div>
                                    </button>
                                `;
                                resolve(customerHTML);
                            });
                    });
                });

                // Wait for all customer credit checks to complete
                Promise.all(customerPromises)
                    .then(customerHTMLs => {
                        resultsHTML += customerHTMLs.join('');
                        resultsHTML += '</div>';
                        resultsContainer.innerHTML = resultsHTML;
                    })
                    .catch(error => {
                        console.error('Error processing customers:', error);
                        resultsContainer.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>Error processing customers: ${error.message}
                            </div>
                        `;
                    });
            })
            .catch(error => {
                console.error('Error searching customers:', error);
                resultsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Error searching customers: ${error.message}
                    </div>
                `;
            });
    };

    // Tab switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const tabItems = document.querySelectorAll('.pos-tab-item');
        const tabPanes = document.querySelectorAll('.pos-tab-pane');
        const buylistIframe = document.getElementById('buylistIframe');
        const iframeError = document.getElementById('iframe-error');
        const reloadIframeBtn = document.getElementById('reload-iframe');
        let iframeLoaded = false;
        let loadAttempts = 0;
        let psaInitialized = false;

        // Function to save active tab to localStorage
        function saveActiveTab(tabId) {
            localStorage.setItem('activeTab', tabId);
        }

        // Function to load and activate the saved tab
        function loadSavedTab() {
            const savedTab = localStorage.getItem('activeTab');
            if (savedTab) {
                // Find the tab item with the saved ID
                const tabToActivate = document.querySelector(`.pos-tab-item[data-tab="${savedTab}"]`);
                if (tabToActivate) {
                    // Simulate a click on the saved tab
                    tabToActivate.click();
                    return true;
                }
            }
            return false;
        }

        // Function to load the buylist iframe
        function loadBuylistIframe() {
            // Hide any previous error message
            iframeError.style.display = 'none';

            // Show loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'iframe-loading';
            loadingDiv.innerHTML = '<div class="spinner"></div><p>Loading Buylist Builder...</p>';
            buylistIframe.parentNode.appendChild(loadingDiv);

            // Get the iframe source URL
            const iframeSrc = buylistIframe.getAttribute('data-src');

            // Set a timeout to detect if iframe fails to load
            const timeoutId = setTimeout(() => {
                if (loadingDiv.parentNode) {
                    loadingDiv.remove();
                }
                iframeError.style.display = 'flex';
                console.error('Iframe load timeout');
            }, 15000); // 15 seconds timeout

            // Set up iframe load event
            buylistIframe.onload = function() {
                clearTimeout(timeoutId);
                iframeLoaded = true;
                loadAttempts++;

                // Remove loading indicator
                const loadingElement = document.querySelector('.iframe-loading');
                if (loadingElement) {
                    loadingElement.remove();
                }

                // Iframe loaded event - do not attempt to access cross-origin content
                console.log('Iframe loaded (onload event fired)');
            };

            // Set up iframe error event
            buylistIframe.onerror = function() {
                clearTimeout(timeoutId);
                console.error('Iframe failed to load');

                // Remove loading indicator
                const loadingElement = document.querySelector('.iframe-loading');
                if (loadingElement) {
                    loadingElement.remove();
                }

                iframeError.style.display = 'flex';
            };

            // Set the iframe src to load the content
            buylistIframe.setAttribute('src', iframeSrc);
        }

        // Add event listener for reload button
        if (reloadIframeBtn) {
            reloadIframeBtn.addEventListener('click', function() {
                loadBuylistIframe();
            });
        }

        // Function to activate a tab
        function activateTab(tabId) {
            // Find the tab element
            const tabElement = document.querySelector(`.pos-tab-item[data-tab="${tabId}"]`);
            if (!tabElement) return false;

            // Remove active class from all tabs and panes
            tabItems.forEach(tab => tab.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to current tab and pane
            tabElement.classList.add('active');
            document.getElementById(tabId).classList.add('active');

            // Save the active tab to localStorage
            saveActiveTab(tabId);

            // Load iframe content when Buylist tab is clicked
            if (tabId === 'buylist-tab') {
                if (!iframeLoaded) {
                    loadBuylistIframe();
                } else {
                    // If already loaded, try to refresh the iframe content
                    try {
                        buylistIframe.contentWindow.location.reload();
                    } catch (e) {
                        console.log('Could not reload iframe content:', e);
                    }
                }
            }

            // Initialize PSA Checker when PSA tab is clicked
            if (tabId === 'psa-tab' && !psaInitialized) {
                initPSAChecker();
                psaInitialized = true;
            }

            // Initialize Card Grader when Card Grader tab is clicked
            if (tabId === 'card-grader-tab') {
                initCardGrader();
            }

            return true;
        }

        // Add click event listeners to tabs
        tabItems.forEach(item => {
            item.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');
                activateTab(tabId);
            });
        });

        // Try to load the saved tab, or default to the first tab
        if (!loadSavedTab()) {
            // If no saved tab or it couldn't be loaded, activate the first tab
            const firstTabId = tabItems[0].getAttribute('data-tab');
            activateTab(firstTabId);
        }
    });

    // Function to initialize Card Grader
    function initCardGrader() {
        // Check if already initialized
        if (window.cardGraderInitialized) return;
        window.cardGraderInitialized = true;

        console.log('Initializing Card Grader');

        // Check remaining scans
        fetch('/card_scanning/check_remaining_scans')
            .then(response => response.json())
            .then(data => {
                const remainingScansCount = document.getElementById('remainingScansCount');
                const scanStatusBadge = document.getElementById('scanStatusBadge');

                if (remainingScansCount) {
                    remainingScansCount.textContent = `${data.remaining_scans} Scans Remaining`;

                    if (data.remaining_scans <= 0) {
                        remainingScansCount.textContent = 'No Scans Remaining';

                        if (scanStatusBadge) {
                            scanStatusBadge.className = 'badge bg-danger ms-2';
                            scanStatusBadge.textContent = 'Purchase More Scans';
                            scanStatusBadge.style.display = 'inline-block';
                        }
                    } else if (data.remaining_scans <= 3) {
                        if (scanStatusBadge) {
                            scanStatusBadge.className = 'badge bg-warning ms-2';
                            scanStatusBadge.textContent = 'Low Balance';
                            scanStatusBadge.style.display = 'inline-block';
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error checking remaining scans:', error);
                const remainingScansCount = document.getElementById('remainingScansCount');
                if (remainingScansCount) {
                    remainingScansCount.textContent = 'Error checking scans';
                }
            });

        // Initialize the purchase modal elements (but don't show the modal)
        const purchaseScansModalEl = document.getElementById('purchaseScansModal');
        const purchaseAmount = document.getElementById('purchaseAmount');
        const summaryAmount = document.getElementById('summaryAmount');
        const summaryScans = document.getElementById('summaryScans');
        const confirmPurchaseBtn = document.getElementById('confirmPurchaseBtn');
        const purchaseStatus = document.getElementById('purchaseStatus');

        if (purchaseAmount) {
            purchaseAmount.addEventListener('input', function() {
                const amount = parseFloat(this.value) || 10;
                const scanPrice = 0.03;
                let numScans = Math.floor(amount / scanPrice);

                // Apply bonus for purchases of £50 or more (10% extra scans)
                if (amount >= 50) {
                    numScans = Math.floor(numScans * 1.1); // 10% bonus
                }

                if (summaryAmount) summaryAmount.textContent = `£${amount.toFixed(2)}`;
                if (summaryScans) summaryScans.textContent = `${numScans.toLocaleString()} scans`;
            });

            // Trigger the input event to initialize values correctly
            purchaseAmount.dispatchEvent(new Event('input'));
        }

        if (confirmPurchaseBtn) {
            confirmPurchaseBtn.addEventListener('click', function() {
                const amount = parseFloat(purchaseAmount.value) || 10;
                if (amount < 10) {
                    purchaseStatus.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> Minimum purchase amount is £10.
                        </div>
                    `;
                    return;
                }

                // Show loading status
                purchaseStatus.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i> Creating checkout session...
                    </div>
                `;

                // Disable the button to prevent multiple clicks
                confirmPurchaseBtn.disabled = true;

                // Create a checkout session
                fetch('/card_scanning/purchase_scans', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: amount
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.checkout_url) {
                        window.location.href = data.checkout_url;
                    } else {
                        purchaseStatus.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i> ${data.error || 'An error occurred'}
                            </div>
                        `;
                        confirmPurchaseBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error creating checkout session:', error);
                    purchaseStatus.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> An error occurred. Please try again.
                        </div>
                    `;
                    confirmPurchaseBtn.disabled = false;
                });
            });
        }

        // Add event listener to the file input to show selected files
        const fileInput = document.getElementById('graderFileInput');
        const uploadedFilesList = document.getElementById('uploadedFilesList');
        const gradeButtonContainer = document.getElementById('gradeButtonContainer');
        const processGradeCardBtn = document.getElementById('processGradeCardBtn');
        const graderResult = document.getElementById('graderResult');

        // Function to format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        if (fileInput) {
            fileInput.addEventListener('change', function() {
                // Clear the list first
                uploadedFilesList.innerHTML = '';

                // Check if files are selected
                if (this.files.length > 0) {
                    // Show the grade button
                    gradeButtonContainer.style.display = 'block';

                    // Create a list of selected files
                    Array.from(this.files).forEach((file, index) => {
                        // Format file size
                        const fileSize = formatFileSize(file.size);

                        // Create list item
                        const listItem = document.createElement('div');
                        listItem.className = 'file-list-item';
                        listItem.innerHTML = `
                            <div>
                                <i class="fas fa-image"></i> ${file.name}
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="file-size me-2">${fileSize}</span>
                                <span class="badge bg-secondary" id="fileStatusBadge-${index}">Ready</span>
                            </div>
                        `;
                        uploadedFilesList.appendChild(listItem);
                    });
                } else {
                    // Hide the grade button if no files are selected
                    gradeButtonContainer.style.display = 'none';
                }
            });
        }

        // Add event listener to the grade button
        if (processGradeCardBtn) {
            processGradeCardBtn.addEventListener('click', function() {
                // Get selected files
                const files = fileInput.files;
                if (!files || files.length === 0) {
                    alert('Please select at least one file to grade.');
                    return;
                }

                // Disable the button while processing
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

                // Clear previous results
                graderResult.innerHTML = '';

                // Array to store graded cards
                const gradedCards = [];

                // Process each file
                let processedCount = 0;
                let errorCount = 0;

                Array.from(files).forEach((file, index) => {
                    // Create FormData object to send the file
                    const formData = new FormData();
                    formData.append('image', file);

                    // Update status to processing
                    const statusBadge = document.getElementById(`fileStatusBadge-${index}`);
                    if (statusBadge) {
                        statusBadge.className = 'badge bg-info';
                        statusBadge.textContent = 'Processing';
                    }

                    // Process the file
                    fetch('/card_scanning/grade_card', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            // Check if it's an insufficient scans error
                            if (response.status === 403) {
                                return response.json().then(data => {
                                    if (data.error === 'Insufficient scans') {
                                        throw new Error(data.message || 'You have used all your free card grader scans. Please purchase more scans to continue.');
                                    } else {
                                        throw new Error(`HTTP error! status: ${response.status}`);
                                    }
                                });
                            }
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Update status to success
                        if (statusBadge) {
                            statusBadge.className = 'badge bg-success';
                            statusBadge.textContent = 'Graded';
                        }

                        // Process the grading result
                        if (data.records && data.records.length > 0) {
                            const record = data.records[0];

                            // Extract the grades and other information
                            const grades = record.grades;
                            const corners = grades.corners || 'N/A';
                            const edges = grades.edges || 'N/A';
                            const surface = grades.surface || 'N/A';
                            const centering = grades.centering || 'N/A';
                            const finalGrade = grades.final || 'N/A';
                            const condition = grades.condition || 'N/A';

                            // Get image URLs
                            const cardImageUrl = record._full_url_card || '';

                            // Add to graded cards array
                            gradedCards.push({
                                imageUrl: cardImageUrl,
                                finalGrade,
                                condition,
                                corners,
                                edges,
                                surface,
                                centering,
                                timestamp: new Date().toLocaleString()
                            });
                        }

                        // Update processed count
                        processedCount++;

                        // Check if all files have been processed
                        if (processedCount + errorCount === files.length) {
                            // Display results
                            displayGradedCardsTable(gradedCards);

                            // Re-enable the button
                            processGradeCardBtn.disabled = false;
                            processGradeCardBtn.innerHTML = '<i class="fas fa-star"></i> Grade Cards';

                            // Update remaining scans
                            fetch('/card_scanning/check_remaining_scans')
                                .then(response => response.json())
                                .then(data => {
                                    const remainingScansCount = document.getElementById('remainingScansCount');
                                    if (remainingScansCount) {
                                        remainingScansCount.textContent = `${data.remaining_scans} Scans Remaining`;

                                        if (data.remaining_scans <= 0) {
                                            remainingScansCount.textContent = 'No Scans Remaining';
                                        }
                                    }
                                });
                        }
                    })
                    .catch(error => {
                        console.error(`Error grading file ${index}:`, error);

                        // Update status to error
                        if (statusBadge) {
                            statusBadge.className = 'badge bg-danger';
                            statusBadge.textContent = 'Error';
                        }

                        // Show error message
                        graderResult.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i> ${error.message}
                            </div>
                        `;

                        // Update error count
                        errorCount++;

                        // Check if all files have been processed
                        if (processedCount + errorCount === files.length) {
                            // Display results if any cards were graded
                            if (gradedCards.length > 0) {
                                displayGradedCardsTable(gradedCards);
                            }

                            // Re-enable the button
                            processGradeCardBtn.disabled = false;
                            processGradeCardBtn.innerHTML = '<i class="fas fa-star"></i> Grade Cards';
                        }
                    });
                });
            });
        }

        // Function to display graded cards in a table
        function displayGradedCardsTable(gradedCards) {
            if (!gradedCards || gradedCards.length === 0) return;

            let resultHtml = `
                <h3 class="mb-4">Grading Results</h3>
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>Card</th>
                                <th>Grade</th>
                                <th>Condition</th>
                                <th>Corners</th>
                                <th>Edges</th>
                                <th>Surface</th>
                                <th>Centering</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Add rows for each graded card
            gradedCards.forEach(card => {
                // Determine grade class based on final grade value
                let gradeClass = 'medium';
                const numericGrade = parseFloat(card.finalGrade);
                if (!isNaN(numericGrade)) {
                    if (numericGrade >= 9) gradeClass = 'high';
                    else if (numericGrade < 7) gradeClass = 'low';
                }

                resultHtml += `
                    <tr>
                        <td><img src="${card.imageUrl}" alt="Graded Card" style="height: 60px;"></td>
                        <td><span class="grade-badge ${gradeClass}">${card.finalGrade}</span></td>
                        <td>${card.condition}</td>
                        <td>${card.corners}</td>
                        <td>${card.edges}</td>
                        <td>${card.surface}</td>
                        <td>${card.centering}</td>
                        <td>${card.timestamp}</td>
                    </tr>
                `;
            });

            resultHtml += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i> Print Results
                    </button>
                </div>
            `;

            graderResult.innerHTML = resultHtml;
        }
    }

    // Function to initialize PSA Checker
    function initPSAChecker() {
        // Add event listener to the PSA form
        document.getElementById('psaForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const certNumber = document.getElementById('certNumber').value;
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            const error = document.getElementById('error');

            // Show loading, hide other elements
            loading.style.display = 'block';
            result.style.display = 'none';
            error.style.display = 'none';

            // Call our backend endpoint
            fetch(`/psa-checker/check/${certNumber}`)
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';

                    if (data.error) {
                        error.textContent = data.error;
                        error.style.display = 'block';
                        return;
                    }

                    // Get the PSA cert data
                    const certData = data.PSACert || data;

                    // Create HTML for the fields
                    let fieldsHtml = '<div class="row">';
                    let column1 = '<div class="col-md-6">';
                    let column2 = '<div class="col-md-6">';

                    // Add images if available
                    if (certData.FrontImage || certData.BackImage) {
                        fieldsHtml += `
                            <div class="col-12 mb-4 text-center">
                                ${certData.FrontImage ? `
                                <div class="mb-2">
                                    <img src="${certData.FrontImage}" alt="Front" class="img-thumbnail" style="height: 150px;">
                                </div>
                                <small class="text-muted">${certData.FrontImage}</small>
                                ` : ''}

                                ${certData.BackImage ? `
                                <div class="mt-3 mb-2">
                                    <img src="${certData.BackImage}" alt="Back" class="img-thumbnail" style="height: 150px;">
                                </div>
                                <small class="text-muted">${certData.BackImage}</small>
                                ` : ''}
                            </div>
                        `;
                    }

                    // Helper function to format field names
                    function formatFieldName(key) {
                        return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                    }

                    // Helper function to format field values
                    function formatFieldValue(value) {
                        if (value === null || value === undefined) return 'N/A';
                        if (typeof value === 'boolean') return value ? 'Yes' : 'No';
                        return value;
                    }

                    // Helper function to determine if a field should be displayed
                    function shouldDisplayField(key, value) {
                        return value !== null && value !== undefined &&
                               typeof value !== 'object' &&
                               key !== 'PSACert';
                    }

                    // Sort fields alphabetically
                    const sortedFields = Object.entries(certData)
                        .filter(([key, value]) => shouldDisplayField(key, value) && !['ImagesFetched', 'ImageSource', 'FrontImage', 'BackImage'].includes(key))
                        .sort((a, b) => a[0].localeCompare(b[0]));

                    // Split fields between columns
                    sortedFields.forEach(([key, value], index) => {
                        const fieldHtml = `
                            <div class="mb-2">
                                <strong>${formatFieldName(key)}:</strong>
                                <span class="ms-1">${formatFieldValue(value)}</span>
                            </div>
                        `;

                        if (index < sortedFields.length / 2) {
                            column1 += fieldHtml;
                        } else {
                            column2 += fieldHtml;
                        }
                    });

                    column1 += '</div>';
                    column2 += '</div>';
                    fieldsHtml += column1 + column2 + '</div>';

                    // Create eBay search URL
                    let ebaySearchTerms = [];
                    if (certData.Subject) ebaySearchTerms.push(certData.Subject);
                    if (certData.Brand) ebaySearchTerms.push(certData.Brand);
                    if (certData.Year) ebaySearchTerms.push(certData.Year);
                    if (certData.CardGrade) ebaySearchTerms.push(`PSA ${certData.CardGrade}`);

                    const ebayUrl = `https://www.ebay.com/sch/i.html?_nkw=${encodeURIComponent(ebaySearchTerms.join(' '))}&_sacat=0&LH_Complete=1&LH_Sold=1`;

                    // Create the full result HTML
                    result.innerHTML = `
                        <div class="card">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <span>Certificate Details</span>
                                <div>
                                    <a href="${ebayUrl}" target="_blank" class="btn btn-light btn-sm">
                                        <i class="fas fa-search"></i> Check eBay Sold Prices
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                ${fieldsHtml}
                            </div>
                        </div>
                    `;
                    result.style.display = 'block';
                })
                .catch(err => {
                    loading.style.display = 'none';
                    error.textContent = `Error: ${err.message}`;
                    error.style.display = 'block';
                });
        });
    }

    // Function to initialize Customers tab
    function initCustomersTab() {
        // Check if already initialized
        if (window.customersTabInitialized) return;
        window.customersTabInitialized = true;

        console.log('Initializing Customers Tab');

        // Load the iframe content
        const customersIframe = document.getElementById('customersIframe');
        const iframeError = document.getElementById('customers-iframe-error');

        if (customersIframe) {
            // Use the enterprise URL from the memory
            const src = "https://enterprise.tcgsync.com/shopify/customers";
            customersIframe.src = src;

            // Handle iframe load events
            customersIframe.onload = function() {
                console.log('Customers iframe loaded successfully');
                if (iframeError) iframeError.style.display = 'none';
            };

            customersIframe.onerror = function() {
                console.error('Failed to load customers iframe');
                if (iframeError) iframeError.style.display = 'block';
            };
        }
    }

    // Function to reload the customers iframe
    function reloadCustomersIframe() {
        const customersIframe = document.getElementById('customersIframe');
        const iframeError = document.getElementById('customers-iframe-error');

        if (customersIframe) {
            if (iframeError) iframeError.style.display = 'none';

            // Reload the iframe with the enterprise URL
            const src = "https://enterprise.tcgsync.com/shopify/customers";
            customersIframe.src = '';
            setTimeout(() => {
                customersIframe.src = src;
            }, 100);
        }
    }

    // Add any additional functions needed for the Customers tab here

    // Add event listener to initialize Customers tab when clicked
    document.addEventListener('DOMContentLoaded', function() {
        // Wait for the DOM to be fully loaded
        setTimeout(function() {
            // Find all tab items
            const tabItems = document.querySelectorAll('.pos-tab-item');

            // Add click event listener to the Customers tab
            tabItems.forEach(item => {
                if (item.getAttribute('data-tab') === 'customers-tab') {
                    item.addEventListener('click', function() {
                        // Initialize the Customers tab when clicked
                        initCustomersTab();
                    });
                }
            });

            // Also modify the existing activateTab function if it exists
            if (typeof window.activateTab === 'function') {
                const originalActivateTab = window.activateTab;
                window.activateTab = function(tabId) {
                    const result = originalActivateTab(tabId);

                    // Initialize Customers tab when activated
                    if (tabId === 'customers-tab') {
                        initCustomersTab();
                    }

                    return result;
                };
            }
        }, 500); // Small delay to ensure all scripts are loaded
    });
</script>

<script src="{{ url_for('static', filename='js/custom-dialogs.js') }}"></script>

<script>
// Override server error popup functionality
// This script runs after custom-dialogs.js is loaded to override its error display behavior
document.addEventListener('DOMContentLoaded', function() {
    // Check if TCGDialog exists (the likely implementation of server error popups)
    if (window.TCGDialog) {
        // Store the original alert method
        const originalAlert = window.TCGDialog.alert;

        // Override the alert method to filter out server errors
        window.TCGDialog.alert = function(options) {
            // Check if this is a server error alert
            if (options &&
                (options.type === 'error' ||
                 (options.message && typeof options.message === 'string' &&
                  (options.message.includes('server') ||
                   options.message.includes('error') ||
                   options.message.includes('failed') ||
                   options.message.includes('unable to'))))) {

                // Log the error to console instead of showing popup
                console.log('Server error suppressed:', options.message);

                // Call the onClose callback if it exists
                if (typeof options.onClose === 'function') {
                    options.onClose();
                }

                // Don't show the popup
                return;
            }

            // For non-server errors, use the original alert method
            return originalAlert.call(window.TCGDialog, options);
        };

        console.log('Server error popups have been disabled');
    }
});

// Define a standalone inline customer selection function
function handleInlineCustomerSelection(customer) {
    console.log("Handling inline customer selection:", customer);

    // Basic functionality for selecting a customer
    const customerDetails = document.getElementById('customerDetails');
    if (customerDetails) {
        customerDetails.style.display = 'block';

        // Handle different customer object formats
        let firstName, lastName, email;
        if (customer.first_name !== undefined) {
            firstName = customer.first_name || '';
            lastName = customer.last_name || '';
            email = customer.email || '';
        } else {
            const nameParts = (customer.name || '').split(' ');
            firstName = nameParts[0] || '';
            lastName = nameParts.slice(1).join(' ') || '';
            email = customer.email || '';
        }

        // Update UI elements if they exist
        const customerNameEl = document.getElementById('customerName');
        const customerEmailEl = document.getElementById('customerEmail');
        const customerIdInput = document.getElementById('customerId');
        const giftCardBalanceEl = document.getElementById('giftCardBalance');
        const storeCreditBalanceEl = document.getElementById('storeCreditBalance');
        const totalCreditBalanceEl = document.getElementById('totalCreditBalance');

        if (customerNameEl) customerNameEl.innerText = `Name: ${firstName} ${lastName}`;
        if (customerEmailEl) customerEmailEl.innerText = `Email: ${email}`;
        if (customerIdInput) customerIdInput.value = customer.id;

        // Update credit balances if available
        const giftCardBalance = customer.gift_card_balance || '0.00';
        const storeCreditBalance = customer.store_credit_balance || '0.00';
        const totalCreditBalance = customer.total_credit_balance || '0.00';

        if (giftCardBalanceEl) giftCardBalanceEl.innerText = `Gift Card Balance: $${giftCardBalance}`;
        if (storeCreditBalanceEl) storeCreditBalanceEl.innerText = `Store Credit Balance: $${storeCreditBalance}`;
        if (totalCreditBalanceEl) totalCreditBalanceEl.innerText = `Total Credit Balance: $${totalCreditBalance}`;

        // Store customer data for other functions
        window.selectedCustomer = {
            id: customer.id,
            name: `${firstName} ${lastName}`.trim(),
            email: email,
            giftCardBalance: giftCardBalance,
            storeCreditBalance: storeCreditBalance,
            totalCreditBalance: totalCreditBalance
        };

        // Hide the customer search results dropdown if it's open
        const inlineCustomerSearchResults = document.getElementById('inlineCustomerSearchResults');
        if (inlineCustomerSearchResults) {
            inlineCustomerSearchResults.classList.add('d-none');
        }

        // Clear the search input
        const inlineCustomerSearch = document.getElementById('inlineCustomerSearch');
        if (inlineCustomerSearch) {
            inlineCustomerSearch.value = '';
        }
    }
}

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Inline customer search functionality
    const inlineCustomerSearch = document.getElementById('inlineCustomerSearch');
    const inlineCustomerSearchResults = document.getElementById('inlineCustomerSearchResults');
    const createCustomerBtn = document.getElementById('createCustomerBtn');

    if (inlineCustomerSearch && inlineCustomerSearchResults) {
        // Add event listener for input changes with debounce
        let searchTimeout;
        inlineCustomerSearch.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.trim();

            if (searchTerm.length < 2) {
                inlineCustomerSearchResults.classList.add('d-none');
                return;
            }

            searchTimeout = setTimeout(() => {
                performInlineCustomerSearch(searchTerm);
            }, 300); // 300ms debounce
        });

        // Hide search results when clicking outside
        document.addEventListener('click', function(event) {
            if (inlineCustomerSearchResults &&
                (!inlineCustomerSearch || !inlineCustomerSearch.contains(event.target)) &&
                (!inlineCustomerSearchResults || !inlineCustomerSearchResults.contains(event.target)) &&
                (!createCustomerBtn || !createCustomerBtn.contains(event.target))) {
                inlineCustomerSearchResults.classList.add('d-none');
            }
        });

        // Show results when focusing on the search input
        inlineCustomerSearch.addEventListener('focus', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length >= 2) {
                inlineCustomerSearchResults.classList.remove('d-none');
            }
        });
    }

    // Create customer button
    if (createCustomerBtn) {
        createCustomerBtn.addEventListener('click', function() {
            // Use the existing create customer modal
            const createCustomerModal = document.getElementById('createCustomerModal');
            if (createCustomerModal) {
                createCustomerModal.style.display = 'block';
            }
        });
    }
});

// Function to perform inline customer search
function performInlineCustomerSearch(searchTerm) {
    if (!searchTerm || searchTerm.length < 2) return;

    // Show loading state
    inlineCustomerSearchResults.innerHTML = `
        <div class="text-center p-3">
            <div class="spinner-border spinner-border-sm text-light" role="status"></div>
            <span class="ms-2 text-light">Searching...</span>
        </div>
    `;
    inlineCustomerSearchResults.classList.remove('d-none');

    // Fetch customers from the server
    fetch(`/pos/search_customer?search=${encodeURIComponent(searchTerm)}`)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                inlineCustomerSearchResults.innerHTML = `
                    <div class="p-3 text-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>${data.message || 'Error searching customers'}
                    </div>
                `;
                return;
            }

            const customers = data.customers || [];

            if (customers.length === 0) {
                inlineCustomerSearchResults.innerHTML = `
                    <div class="p-3 text-center">
                        <p class="text-light mb-2">No customers found</p>
                        <button class="btn btn-sm btn-success" onclick="document.getElementById('createCustomerModal').style.display='block';">
                            <i class="fas fa-user-plus me-1"></i>Create New Customer
                        </button>
                    </div>
                `;
                return;
            }

            // Build results HTML
            let resultsHTML = '';

            customers.forEach(customer => {
                const fullName = `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || 'No Name';
                const email = customer.email || 'No Email';
                const totalCredit = parseFloat(customer.total_credit_balance || 0);

                resultsHTML += `
                    <div class="customer-result p-2 border-bottom border-secondary"
                         data-customer-id="${customer.id}"
                         data-customer-name="${fullName}"
                         data-customer-email="${email}"
                         data-gift-card-balance="${customer.gift_card_balance || 0}"
                         data-store-credit-balance="${customer.store_credit_balance || 0}"
                         data-total-credit-balance="${customer.total_credit_balance || 0}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="fw-bold text-light">${fullName}</div>
                                <div class="small text-muted">${email}</div>
                                ${totalCredit > 0 ? `
                                <div class="mt-1">
                                    <span class="badge bg-success">
                                        <i class="fas fa-credit-card me-1"></i>Credit: $${totalCredit.toFixed(2)}
                                    </span>
                                </div>` : ''}
                            </div>
                            <button class="btn btn-sm btn-outline-light select-customer-btn">
                                <i class="fas fa-check me-1"></i>Select
                            </button>
                        </div>
                    </div>
                `;
            });

            inlineCustomerSearchResults.innerHTML = resultsHTML;

            // Add event listeners to the customer results
            const customerResults = inlineCustomerSearchResults.querySelectorAll('.customer-result');
            customerResults.forEach(result => {
                // Add click event to the entire result div
                result.addEventListener('click', function() {
                    const customerId = this.dataset.customerId;
                    const customerName = this.dataset.customerName;
                    const customerEmail = this.dataset.customerEmail;
                    const giftCardBalance = this.dataset.giftCardBalance;
                    const storeCreditBalance = this.dataset.storeCreditBalance;
                    const totalCreditBalance = this.dataset.totalCreditBalance;

                    const customerData = {
                        id: customerId,
                        name: customerName,
                        email: customerEmail,
                        gift_card_balance: giftCardBalance,
                        store_credit_balance: storeCreditBalance,
                        total_credit_balance: totalCreditBalance
                    };

                    // Call the handleInlineCustomerSelection function
                    handleInlineCustomerSelection(customerData);
                });

                // Add click event to the select button
                const selectBtn = result.querySelector('.select-customer-btn');
                if (selectBtn) {
                    selectBtn.addEventListener('click', function(e) {
                        e.stopPropagation(); // Prevent the parent div's click event

                        const parent = this.closest('.customer-result');
                        const customerId = parent.dataset.customerId;
                        const customerName = parent.dataset.customerName;
                        const customerEmail = parent.dataset.customerEmail;
                        const giftCardBalance = parent.dataset.giftCardBalance;
                        const storeCreditBalance = parent.dataset.storeCreditBalance;
                        const totalCreditBalance = parent.dataset.totalCreditBalance;

                        const customerData = {
                            id: customerId,
                            name: customerName,
                            email: customerEmail,
                            gift_card_balance: giftCardBalance,
                            store_credit_balance: storeCreditBalance,
                            total_credit_balance: totalCreditBalance
                        };

                        // Call the handleInlineCustomerSelection function
                        handleInlineCustomerSelection(customerData);
                    });
                }
            });
        })
        .catch(error => {
            console.error('Error searching customers:', error);
            inlineCustomerSearchResults.innerHTML = `
                <div class="p-3 text-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>Error searching customers
                </div>
            `;
        });
}
</script>

</body>
</html>
