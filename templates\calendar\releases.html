{% extends "base.html" %}

{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card" style="background: linear-gradient(135deg, #1a1a2e, #16213e); border: 1px solid rgba(255, 255, 255, 0.05); border-radius: 12px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);">
                <div class="card-header" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02)); border-bottom: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px 12px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" style="color: #ffffff; font-weight: 600;">
                            <i class="fas fa-calendar-alt me-2" style="color: #ff4081;"></i>Upcoming Expansion Releases
                        </h5>
                        <div class="d-flex align-items-center gap-3">
                            <!-- Game Filter -->
                            <div class="filter-container">
                                <select id="gameFilter" class="form-select form-select-sm" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff; min-width: 200px;">
                                    <option value="">All Games</option>
                                    {% for game in games %}
                                    <option value="{{ game }}" {% if game == selected_game %}selected{% endif %}>{{ game }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <!-- Release Count -->
                            <div class="text-light">
                                <small><i class="fas fa-list me-1"></i>{{ releases|length }} upcoming releases</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="padding: 20px;">
                    {% if error %}
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                    </div>
                    {% endif %}

                    <!-- Releases Table -->
                    <div class="table-responsive">
                        <table class="table table-dark table-striped table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 20%;">Release Date</th>
                                    <th style="width: 30%;">Game</th>
                                    <th style="width: 35%;">Expansion</th>
                                    <th style="width: 15%;">Catalog Items</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if releases %}
                                    {% for release in releases %}
                                    <tr class="release-row" data-game="{{ release.gameName }}">
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">{{ release.formatted_date }}</span>
                                                <small class="text-muted">{{ release.sort_date.strftime('%A') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="game-color-indicator me-2" style="width: 12px; height: 12px; border-radius: 50%;" data-game="{{ release.gameName }}"></div>
                                                <span class="fw-semibold">{{ release.gameName }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="expansion-name">{{ release.expansionName }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="catalog-count-number">{{ release.catalog_count }}</span>
                                                <small class="text-muted ms-2">items</small>
                                                {% if release.catalog_count > 0 %}
                                                    <i class="fas fa-check-circle text-success ms-2" title="Items available in catalog"></i>
                                                {% else %}
                                                    <i class="fas fa-exclamation-circle text-warning ms-2" title="No items in catalog yet"></i>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4">
                                            <i class="fas fa-calendar-times fa-2x mb-2"></i>
                                            <div>No upcoming releases found</div>
                                            {% if selected_game %}
                                            <small>Try selecting a different game or view all games</small>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<style>
/* Filter Styling */
.filter-container select {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
}

.filter-container select option {
    background: #1a1a2e;
    color: #ffffff;
}

/* Table Styling */
.table-dark {
    --bs-table-bg: rgba(255, 255, 255, 0.02);
    --bs-table-striped-bg: rgba(255, 255, 255, 0.05);
    --bs-table-hover-bg: rgba(255, 255, 255, 0.08);
}

.table-dark th {
    border-color: rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
    color: #ffffff;
}

.table-dark td {
    border-color: rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.08) !important;
}

/* Catalog Count Styling */
.catalog-count-number {
    font-weight: 600;
    font-size: 1.1rem;
    color: #ffffff;
}

.catalog-count-number:hover {
    color: #ff4081;
}

/* Game Color Indicator */
.game-color-indicator {
    flex-shrink: 0;
}

/* Expansion Name Styling */
.expansion-name {
    font-weight: 500;
    color: #ffffff;
}

/* Release Row Hover Effect */
.release-row {
    transition: background-color 0.2s ease;
}

.release-row:hover .expansion-name {
    color: #ff4081;
}

/* Badge Styling */
.badge {
    font-size: 0.75rem;
}

/* Empty State Styling */
.table tbody tr td {
    padding: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    .catalog-count-number {
        font-size: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gameFilter = document.getElementById('gameFilter');

    // Game colors
    const gameColors = {
        'Magic: The Gathering': '#FF6B35',
        'Pokemon': '#FFCC02',
        'Yu-Gi-Oh!': '#8E44AD',
        'Dragon Ball Super': '#E74C3C',
        'One Piece': '#3498DB',
        'Digimon': '#2ECC71',
        'Flesh and Blood': '#E67E22',
        'Lorcana': '#9B59B6'
    };

    // Set game color indicators
    const gameIndicators = document.querySelectorAll('.game-color-indicator');
    gameIndicators.forEach(indicator => {
        const gameName = indicator.getAttribute('data-game');
        const color = gameColors[gameName] || '#6C757D';
        indicator.style.backgroundColor = color;
    });

    // Game filter functionality
    gameFilter.addEventListener('change', function() {
        const selectedGame = this.value;
        const url = new URL(window.location);
        if (selectedGame) {
            url.searchParams.set('game', selectedGame);
        } else {
            url.searchParams.delete('game');
        }
        window.location.href = url.toString();
    });

    // Add hover effects for catalog counts
    const catalogCounts = document.querySelectorAll('.catalog-count-number');
    catalogCounts.forEach(count => {
        count.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
        });

        count.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>
{% endblock %}
